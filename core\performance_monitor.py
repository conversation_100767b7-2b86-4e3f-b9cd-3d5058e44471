"""
性能监控系统
提供性能监控、内存分析和诊断工具
"""

import logging
import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from collections import deque
import json

from core.logger import get_logger


@dataclass
class PerformanceMetric:
    """性能指标"""
    timestamp: str
    metric_name: str
    value: float
    unit: str
    category: str = "general"
    tags: Dict[str, str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = {}


@dataclass
class SystemResourceUsage:
    """系统资源使用情况"""
    timestamp: str
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    network_bytes_sent: int = 0
    network_bytes_recv: int = 0


@dataclass
class AllocationPerformance:
    """分配性能指标"""
    timestamp: str
    total_points: int
    allocation_time: float
    success_rate: float
    points_per_second: float
    memory_peak_mb: float
    errors_count: int = 0


@dataclass
class PerformanceAlert:
    """性能告警"""
    timestamp: str
    alert_type: str
    severity: str
    message: str
    metric_name: str
    threshold_value: float
    actual_value: float
    resolved: bool = False


class PerformanceCollector:
    """性能数据收集器"""
    
    def __init__(self, collection_interval: float = 1.0):
        """
        初始化性能收集器
        
        Args:
            collection_interval: 收集间隔（秒）
        """
        self.collection_interval = collection_interval
        self.logger = get_logger(__name__)
        self.is_collecting = False
        self.collection_thread: Optional[threading.Thread] = None
        
        # 性能数据缓存
        self.metrics_buffer: deque = deque(maxlen=1000)
        self.system_usage_buffer: deque = deque(maxlen=1000)
        
        # 回调函数
        self.metric_callbacks: List[Callable[[PerformanceMetric], None]] = []
    
    def start_collection(self):
        """开始性能数据收集"""
        if self.is_collecting:
            return
        
        self.is_collecting = True
        self.collection_thread = threading.Thread(target=self._collection_loop, daemon=True)
        self.collection_thread.start()
        
        self.logger.info("性能数据收集已启动")
    
    def stop_collection(self):
        """停止性能数据收集"""
        self.is_collecting = False
        
        if self.collection_thread and self.collection_thread.is_alive():
            self.collection_thread.join(timeout=5.0)
        
        self.logger.info("性能数据收集已停止")
    
    def _collection_loop(self):
        """收集循环"""
        while self.is_collecting:
            try:
                # 收集系统资源使用情况
                system_usage = self._collect_system_usage()
                self.system_usage_buffer.append(system_usage)
                
                # 收集自定义性能指标
                self._collect_custom_metrics()
                
                time.sleep(self.collection_interval)
                
            except Exception as e:
                self.logger.error(f"性能数据收集异常: {e}")
                time.sleep(self.collection_interval)
    
    def _collect_system_usage(self) -> SystemResourceUsage:
        """收集系统资源使用情况"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=None)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_mb = memory.used / (1024 * 1024)
            memory_available_mb = memory.available / (1024 * 1024)
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_usage_percent = (disk.used / disk.total) * 100
            
            # 网络使用情况
            network = psutil.net_io_counters()
            network_bytes_sent = network.bytes_sent
            network_bytes_recv = network.bytes_recv
            
            return SystemResourceUsage(
                timestamp=datetime.now().isoformat(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used_mb=memory_used_mb,
                memory_available_mb=memory_available_mb,
                disk_usage_percent=disk_usage_percent,
                network_bytes_sent=network_bytes_sent,
                network_bytes_recv=network_bytes_recv
            )
            
        except Exception as e:
            self.logger.error(f"收集系统资源使用情况失败: {e}")
            return SystemResourceUsage(
                timestamp=datetime.now().isoformat(),
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_used_mb=0.0,
                memory_available_mb=0.0,
                disk_usage_percent=0.0
            )
    
    def _collect_custom_metrics(self):
        """收集自定义性能指标"""
        # 这里可以添加应用程序特定的性能指标收集
        pass
    
    def add_metric(self, metric: PerformanceMetric):
        """
        添加性能指标
        
        Args:
            metric: 性能指标
        """
        self.metrics_buffer.append(metric)
        
        # 触发回调
        for callback in self.metric_callbacks:
            try:
                callback(metric)
            except Exception as e:
                self.logger.error(f"性能指标回调异常: {e}")
    
    def add_metric_callback(self, callback: Callable[[PerformanceMetric], None]):
        """
        添加指标回调函数
        
        Args:
            callback: 回调函数
        """
        self.metric_callbacks.append(callback)
    
    def get_recent_metrics(self, count: int = 100) -> List[PerformanceMetric]:
        """
        获取最近的性能指标
        
        Args:
            count: 获取数量
            
        Returns:
            性能指标列表
        """
        return list(self.metrics_buffer)[-count:]
    
    def get_recent_system_usage(self, count: int = 100) -> List[SystemResourceUsage]:
        """
        获取最近的系统使用情况
        
        Args:
            count: 获取数量
            
        Returns:
            系统使用情况列表
        """
        return list(self.system_usage_buffer)[-count:]


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self):
        """初始化性能分析器"""
        self.logger = get_logger(__name__)
    
    def analyze_allocation_performance(self, allocation_history: List[AllocationPerformance]) -> Dict[str, Any]:
        """
        分析分配性能
        
        Args:
            allocation_history: 分配性能历史
            
        Returns:
            分析结果
        """
        if not allocation_history:
            return {}
        
        try:
            # 计算统计指标
            allocation_times = [perf.allocation_time for perf in allocation_history]
            success_rates = [perf.success_rate for perf in allocation_history]
            points_per_second = [perf.points_per_second for perf in allocation_history]
            memory_peaks = [perf.memory_peak_mb for perf in allocation_history]
            
            analysis = {
                'total_allocations': len(allocation_history),
                'allocation_time': {
                    'min': min(allocation_times),
                    'max': max(allocation_times),
                    'avg': sum(allocation_times) / len(allocation_times),
                    'median': self._calculate_median(allocation_times)
                },
                'success_rate': {
                    'min': min(success_rates),
                    'max': max(success_rates),
                    'avg': sum(success_rates) / len(success_rates)
                },
                'throughput': {
                    'min_points_per_sec': min(points_per_second),
                    'max_points_per_sec': max(points_per_second),
                    'avg_points_per_sec': sum(points_per_second) / len(points_per_second)
                },
                'memory_usage': {
                    'min_peak_mb': min(memory_peaks),
                    'max_peak_mb': max(memory_peaks),
                    'avg_peak_mb': sum(memory_peaks) / len(memory_peaks)
                }
            }
            
            # 性能趋势分析
            if len(allocation_history) >= 2:
                analysis['trends'] = self._analyze_trends(allocation_history)
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"分析分配性能失败: {e}")
            return {}
    
    def analyze_system_performance(self, system_usage_history: List[SystemResourceUsage]) -> Dict[str, Any]:
        """
        分析系统性能
        
        Args:
            system_usage_history: 系统使用历史
            
        Returns:
            分析结果
        """
        if not system_usage_history:
            return {}
        
        try:
            cpu_usage = [usage.cpu_percent for usage in system_usage_history]
            memory_usage = [usage.memory_percent for usage in system_usage_history]
            disk_usage = [usage.disk_usage_percent for usage in system_usage_history]
            
            analysis = {
                'cpu_usage': {
                    'min': min(cpu_usage),
                    'max': max(cpu_usage),
                    'avg': sum(cpu_usage) / len(cpu_usage),
                    'median': self._calculate_median(cpu_usage)
                },
                'memory_usage': {
                    'min': min(memory_usage),
                    'max': max(memory_usage),
                    'avg': sum(memory_usage) / len(memory_usage),
                    'median': self._calculate_median(memory_usage)
                },
                'disk_usage': {
                    'min': min(disk_usage),
                    'max': max(disk_usage),
                    'avg': sum(disk_usage) / len(disk_usage),
                    'median': self._calculate_median(disk_usage)
                }
            }
            
            # 资源使用趋势
            analysis['resource_trends'] = self._analyze_resource_trends(system_usage_history)
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"分析系统性能失败: {e}")
            return {}
    
    def _calculate_median(self, values: List[float]) -> float:
        """计算中位数"""
        sorted_values = sorted(values)
        n = len(sorted_values)
        
        if n % 2 == 0:
            return (sorted_values[n//2 - 1] + sorted_values[n//2]) / 2
        else:
            return sorted_values[n//2]
    
    def _analyze_trends(self, allocation_history: List[AllocationPerformance]) -> Dict[str, str]:
        """分析性能趋势"""
        trends = {}
        
        if len(allocation_history) < 2:
            return trends
        
        # 分析分配时间趋势
        recent_times = [perf.allocation_time for perf in allocation_history[-5:]]
        earlier_times = [perf.allocation_time for perf in allocation_history[-10:-5]] if len(allocation_history) >= 10 else []
        
        if earlier_times:
            recent_avg = sum(recent_times) / len(recent_times)
            earlier_avg = sum(earlier_times) / len(earlier_times)
            
            if recent_avg < earlier_avg * 0.9:
                trends['allocation_time'] = 'improving'
            elif recent_avg > earlier_avg * 1.1:
                trends['allocation_time'] = 'degrading'
            else:
                trends['allocation_time'] = 'stable'
        
        return trends
    
    def _analyze_resource_trends(self, system_usage_history: List[SystemResourceUsage]) -> Dict[str, str]:
        """分析资源使用趋势"""
        trends = {}
        
        if len(system_usage_history) < 10:
            return trends
        
        # 分析CPU使用趋势
        recent_cpu = [usage.cpu_percent for usage in system_usage_history[-5:]]
        earlier_cpu = [usage.cpu_percent for usage in system_usage_history[-10:-5]]
        
        recent_cpu_avg = sum(recent_cpu) / len(recent_cpu)
        earlier_cpu_avg = sum(earlier_cpu) / len(earlier_cpu)
        
        if recent_cpu_avg > earlier_cpu_avg * 1.2:
            trends['cpu'] = 'increasing'
        elif recent_cpu_avg < earlier_cpu_avg * 0.8:
            trends['cpu'] = 'decreasing'
        else:
            trends['cpu'] = 'stable'
        
        # 分析内存使用趋势
        recent_memory = [usage.memory_percent for usage in system_usage_history[-5:]]
        earlier_memory = [usage.memory_percent for usage in system_usage_history[-10:-5]]
        
        recent_memory_avg = sum(recent_memory) / len(recent_memory)
        earlier_memory_avg = sum(earlier_memory) / len(earlier_memory)
        
        if recent_memory_avg > earlier_memory_avg * 1.2:
            trends['memory'] = 'increasing'
        elif recent_memory_avg < earlier_memory_avg * 0.8:
            trends['memory'] = 'decreasing'
        else:
            trends['memory'] = 'stable'
        
        return trends


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化性能监控器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = get_logger(__name__)
        
        # 性能配置
        perf_config = config.get('performance_monitoring', {})
        collection_interval = perf_config.get('collection_interval', 1.0)
        
        # 初始化组件
        self.collector = PerformanceCollector(collection_interval)
        self.analyzer = PerformanceAnalyzer()
        
        # 告警配置
        self.alert_thresholds = perf_config.get('alert_thresholds', {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_usage_percent': 90.0,
            'allocation_time': 30.0
        })
        
        # 告警历史
        self.alerts: List[PerformanceAlert] = []
        
        # 分配性能历史
        self.allocation_performance_history: List[AllocationPerformance] = []
        
        # 添加告警回调
        self.collector.add_metric_callback(self._check_alerts)
        
        self.logger.info("性能监控器初始化完成")
    
    def start_monitoring(self):
        """开始性能监控"""
        self.collector.start_collection()
        self.logger.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.collector.stop_collection()
        self.logger.info("性能监控已停止")
    
    def record_allocation_performance(self, total_points: int, allocation_time: float, 
                                    success_rate: float, memory_peak_mb: float, 
                                    errors_count: int = 0):
        """
        记录分配性能
        
        Args:
            total_points: 总点数
            allocation_time: 分配时间
            success_rate: 成功率
            memory_peak_mb: 内存峰值
            errors_count: 错误数量
        """
        points_per_second = total_points / allocation_time if allocation_time > 0 else 0
        
        performance = AllocationPerformance(
            timestamp=datetime.now().isoformat(),
            total_points=total_points,
            allocation_time=allocation_time,
            success_rate=success_rate,
            points_per_second=points_per_second,
            memory_peak_mb=memory_peak_mb,
            errors_count=errors_count
        )
        
        self.allocation_performance_history.append(performance)
        
        # 添加性能指标
        self.collector.add_metric(PerformanceMetric(
            timestamp=performance.timestamp,
            metric_name="allocation_time",
            value=allocation_time,
            unit="seconds",
            category="allocation"
        ))
        
        self.collector.add_metric(PerformanceMetric(
            timestamp=performance.timestamp,
            metric_name="allocation_success_rate",
            value=success_rate,
            unit="percent",
            category="allocation"
        ))
    
    def _check_alerts(self, metric: PerformanceMetric):
        """
        检查告警条件
        
        Args:
            metric: 性能指标
        """
        threshold = self.alert_thresholds.get(metric.metric_name)
        if threshold is None:
            return
        
        if metric.value > threshold:
            alert = PerformanceAlert(
                timestamp=datetime.now().isoformat(),
                alert_type="THRESHOLD_EXCEEDED",
                severity="WARNING",
                message=f"{metric.metric_name} 超过阈值",
                metric_name=metric.metric_name,
                threshold_value=threshold,
                actual_value=metric.value
            )
            
            self.alerts.append(alert)
            self.logger.warning(f"性能告警: {alert.message}, 阈值: {threshold}, 实际值: {metric.value}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """
        获取性能摘要
        
        Returns:
            性能摘要字典
        """
        summary = {
            'monitoring_status': 'active' if self.collector.is_collecting else 'inactive',
            'collection_interval': self.collector.collection_interval,
            'metrics_collected': len(self.collector.metrics_buffer),
            'system_usage_records': len(self.collector.system_usage_buffer),
            'allocation_records': len(self.allocation_performance_history),
            'active_alerts': len([alert for alert in self.alerts if not alert.resolved])
        }
        
        # 最新系统使用情况
        recent_system_usage = self.collector.get_recent_system_usage(1)
        if recent_system_usage:
            latest_usage = recent_system_usage[0]
            summary['current_system_usage'] = {
                'cpu_percent': latest_usage.cpu_percent,
                'memory_percent': latest_usage.memory_percent,
                'disk_usage_percent': latest_usage.disk_usage_percent
            }
        
        # 分配性能分析
        if self.allocation_performance_history:
            allocation_analysis = self.analyzer.analyze_allocation_performance(
                self.allocation_performance_history
            )
            summary['allocation_performance'] = allocation_analysis
        
        return summary
    
    def get_alerts(self, resolved: Optional[bool] = None) -> List[PerformanceAlert]:
        """
        获取告警列表
        
        Args:
            resolved: 是否已解决，None表示获取所有
            
        Returns:
            告警列表
        """
        if resolved is None:
            return self.alerts.copy()
        
        return [alert for alert in self.alerts if alert.resolved == resolved]
    
    def resolve_alert(self, alert_index: int):
        """
        解决告警
        
        Args:
            alert_index: 告警索引
        """
        if 0 <= alert_index < len(self.alerts):
            self.alerts[alert_index].resolved = True
            self.logger.info(f"告警已解决: {self.alerts[alert_index].message}")
    
    def export_performance_data(self, output_path: str, 
                               start_time: Optional[datetime] = None,
                               end_time: Optional[datetime] = None) -> bool:
        """
        导出性能数据
        
        Args:
            output_path: 输出路径
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            导出是否成功
        """
        try:
            # 收集数据
            data = {
                'export_timestamp': datetime.now().isoformat(),
                'performance_summary': self.get_performance_summary(),
                'system_usage_history': [asdict(usage) for usage in self.collector.get_recent_system_usage()],
                'allocation_performance_history': [asdict(perf) for perf in self.allocation_performance_history],
                'alerts': [asdict(alert) for alert in self.alerts],
                'metrics': [asdict(metric) for metric in self.collector.get_recent_metrics()]
            }
            
            # 时间过滤
            if start_time or end_time:
                data = self._filter_data_by_time(data, start_time, end_time)
            
            # 导出到JSON文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"性能数据导出成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出性能数据失败: {e}")
            return False
    
    def _filter_data_by_time(self, data: Dict[str, Any], 
                            start_time: Optional[datetime],
                            end_time: Optional[datetime]) -> Dict[str, Any]:
        """
        按时间过滤数据
        
        Args:
            data: 原始数据
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            过滤后的数据
        """
        # 简化的时间过滤实现
        # 实际应该解析timestamp字符串并进行时间比较
        return data
