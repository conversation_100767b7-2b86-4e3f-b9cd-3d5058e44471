"""
Excel处理工具
提供Excel文件读写功能
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import pandas as pd

from core.logger import get_logger
from core.data_models import IOPoint, Cable, SignalType


class ExcelReader:
    """Excel读取器"""

    def __init__(self):
        """初始化Excel读取器"""
        self.logger = get_logger(__name__)

    def read_excel_file(self, file_path: str, sheet_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        读取Excel文件

        Args:
            file_path: Excel文件路径
            sheet_names: 要读取的工作表名称列表，None表示读取所有

        Returns:
            Excel数据字典
        """
        self.logger.info(f"读取Excel文件: {file_path}")

        try:
            if not Path(file_path).exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")

            # 使用pandas读取Excel文件
            if sheet_names is None:
                # 读取所有工作表
                data = pd.read_excel(file_path, sheet_name=None, engine='openpyxl')
            else:
                # 读取指定工作表
                data = pd.read_excel(file_path, sheet_name=sheet_names, engine='openpyxl')

            self.logger.info(f"成功读取Excel文件，包含工作表: {list(data.keys()) if isinstance(data, dict) else 'single sheet'}")
            return data

        except Exception as e:
            self.logger.error(f"读取Excel文件失败: {e}")
            raise

    def read_iodb_file(self, file_path: str) -> Dict[str, Any]:
        """
        读取IODB文件

        Args:
            file_path: IODB文件路径

        Returns:
            IODB数据
        """
        self.logger.info(f"读取IODB文件: {file_path}")

        try:
            # 读取IODB文件的所有工作表
            data = self.read_excel_file(file_path)

            # 处理IODB数据格式
            processed_data = self._process_iodb_data(data)

            return processed_data

        except Exception as e:
            self.logger.error(f"读取IODB文件失败: {e}")
            raise

    def read_pidb_file(self, file_path: str) -> Dict[str, Any]:
        """
        读取PIDB文件

        Args:
            file_path: PIDB文件路径

        Returns:
            PIDB数据
        """
        self.logger.info(f"读取PIDB文件: {file_path}")

        try:
            # 读取PIDB文件的chassis和cabinet工作表
            required_sheets = ['chassis', 'cabinet']
            data = self.read_excel_file(file_path, required_sheets)

            # 处理PIDB数据格式
            processed_data = self._process_pidb_data(data)

            return processed_data

        except Exception as e:
            self.logger.error(f"读取PIDB文件失败: {e}")
            raise

    def _process_iodb_data(self, raw_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        处理IODB原始数据

        Args:
            raw_data: 原始Excel数据

        Returns:
            处理后的IODB数据
        """
        processed_data = {
            'io_points': [],
            'cables': {},
            'summary': {
                'total_points': 0,
                'total_cables': 0,
                'signal_types': {},
                'systems': set(),
                'locations': set()
            }
        }

        # 假设IODB数据在第一个工作表中
        if not raw_data:
            return processed_data

        # 获取第一个工作表
        sheet_name = list(raw_data.keys())[0]
        df = raw_data[sheet_name]

        self.logger.info(f"处理IODB数据，共{len(df)}行记录")

        # 处理每一行数据
        for index, row in df.iterrows():
            try:
                io_point = self._create_io_point_from_row(row)
                if io_point:
                    processed_data['io_points'].append(io_point)

                    # 更新统计信息
                    self._update_iodb_summary(processed_data['summary'], io_point)

                    # 组织电缆数据
                    self._organize_cable_data(processed_data['cables'], io_point)

            except Exception as e:
                self.logger.warning(f"处理第{index+2}行数据失败: {e}")
                continue

        processed_data['summary']['total_points'] = len(processed_data['io_points'])
        processed_data['summary']['total_cables'] = len(processed_data['cables'])

        self.logger.info(f"IODB数据处理完成: {processed_data['summary']['total_points']}个I/O点, {processed_data['summary']['total_cables']}条电缆")

        return processed_data

    def _create_io_point_from_row(self, row: pd.Series) -> Optional[IOPoint]:
        """
        从Excel行数据创建IOPoint对象

        Args:
            row: Excel行数据

        Returns:
            IOPoint对象或None
        """
        try:
            # 根据实际IODB文件的列名映射
            # 这里使用常见的列名，实际使用时需要根据具体文件调整
            tag = str(row.get('Tag', row.get('tag', row.get('TAG', ''))))
            cable_name = str(row.get('Cable', row.get('cable_name', row.get('CABLE', ''))))
            pair_number = int(row.get('Pair', row.get('pair_number', row.get('PAIR', 0))))

            # 信号类型映射
            signal_type_str = str(row.get('SignalType', row.get('signal_type', row.get('SIGNAL_TYPE', 'AI'))))
            signal_type = self._parse_signal_type(signal_type_str)

            # 本安属性
            is_str = str(row.get('IS', row.get('is_intrinsic', row.get('Intrinsic', 'NIS'))))
            is_intrinsic = is_str.upper() in ['IS', 'TRUE', '1', 'YES']

            system = str(row.get('System', row.get('system', row.get('SYSTEM', ''))))
            location = str(row.get('Location', row.get('location', row.get('LOCATION', ''))))
            cable_type = str(row.get('CableType', row.get('cable_type', row.get('CABLE_TYPE', ''))))
            wiring_typical = str(row.get('WiringTypical', row.get('wiring_typical', row.get('WIRING_TYPICAL', ''))))
            description = str(row.get('Description', row.get('description', row.get('DESC', '')))

            # 验证必要字段
            if not tag or not cable_name:
                return None

            io_point = IOPoint(
                tag=tag,
                cable_name=cable_name,
                pair_number=pair_number,
                signal_type=signal_type,
                is_intrinsic=is_intrinsic,
                system=system,
                location=location,
                cable_type=cable_type,
                wiring_typical=wiring_typical,
                description=description
            )

            return io_point

        except Exception as e:
            self.logger.warning(f"创建IOPoint失败: {e}")
            return None

    def _parse_signal_type(self, signal_type_str: str) -> SignalType:
        """
        解析信号类型字符串

        Args:
            signal_type_str: 信号类型字符串

        Returns:
            SignalType枚举值
        """
        signal_type_str = signal_type_str.upper().strip()

        if signal_type_str in ['AI', 'ANALOG_INPUT']:
            return SignalType.AI
        elif signal_type_str in ['AO', 'ANALOG_OUTPUT']:
            return SignalType.AO
        elif signal_type_str in ['DI', 'DIGITAL_INPUT']:
            return SignalType.DI
        elif signal_type_str in ['DO', 'DIGITAL_OUTPUT']:
            return SignalType.DO
        else:
            # 默认为AI
            return SignalType.AI

    def _update_iodb_summary(self, summary: Dict[str, Any], io_point: IOPoint):
        """
        更新IODB摘要统计信息

        Args:
            summary: 摘要字典
            io_point: I/O点对象
        """
        # 统计信号类型
        signal_type = io_point.signal_type.value
        summary['signal_types'][signal_type] = summary['signal_types'].get(signal_type, 0) + 1

        # 收集系统和位置信息
        summary['systems'].add(io_point.system)
        summary['locations'].add(io_point.location)

    def _organize_cable_data(self, cables: Dict[str, Cable], io_point: IOPoint):
        """
        组织电缆数据

        Args:
            cables: 电缆字典
            io_point: I/O点对象
        """
        cable_name = io_point.cable_name

        if cable_name not in cables:
            # 创建新的电缆对象
            cable = Cable(
                name=cable_name,
                pair_size=0,  # 将在后续计算
                signal_type=io_point.signal_type,
                is_intrinsic=io_point.is_intrinsic,
                system=io_point.system,
                location=io_point.location,
                cable_type=io_point.cable_type
            )
            cables[cable_name] = cable

        # 添加I/O点到电缆
        cables[cable_name].add_io_point(io_point)

        # 更新pair_size
        used_pairs = cables[cable_name].get_used_pairs()
        cables[cable_name].pair_size = max(used_pairs) if used_pairs else 0

    def _process_pidb_data(self, raw_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        处理PIDB原始数据

        Args:
            raw_data: 原始Excel数据

        Returns:
            处理后的PIDB数据
        """
        processed_data = {
            'chassis': [],
            'cabinets': [],
            'cabinet_mapping': {}  # 机架到机柜的映射
        }

        # 处理chassis工作表
        if 'chassis' in raw_data:
            chassis_df = raw_data['chassis']
            self.logger.info(f"处理PIDB chassis数据，共{len(chassis_df)}行记录")

            for index, row in chassis_df.iterrows():
                try:
                    chassis_data = self._create_chassis_from_row(row)
                    if chassis_data:
                        processed_data['chassis'].append(chassis_data)
                except Exception as e:
                    self.logger.warning(f"处理chassis第{index+2}行数据失败: {e}")

        # 处理cabinet工作表
        if 'cabinet' in raw_data:
            cabinet_df = raw_data['cabinet']
            self.logger.info(f"处理PIDB cabinet数据，共{len(cabinet_df)}行记录")

            for index, row in cabinet_df.iterrows():
                try:
                    cabinet_data = self._create_cabinet_from_row(row)
                    if cabinet_data:
                        processed_data['cabinets'].append(cabinet_data)

                        # 建立机架到机柜的映射
                        for chassis in processed_data['chassis']:
                            if chassis.get('cabinet_name') == cabinet_data.get('name'):
                                processed_data['cabinet_mapping'][chassis.get('name')] = cabinet_data.get('name')

                except Exception as e:
                    self.logger.warning(f"处理cabinet第{index+2}行数据失败: {e}")

        self.logger.info(f"PIDB数据处理完成: {len(processed_data['chassis'])}个机架, {len(processed_data['cabinets'])}个机柜")

        return processed_data

    def _create_chassis_from_row(self, row: pd.Series) -> Optional[Dict[str, Any]]:
        """
        从Excel行数据创建机架数据

        Args:
            row: Excel行数据

        Returns:
            机架数据字典或None
        """
        try:
            chassis_data = {
                'name': str(row.get('Name', row.get('name', row.get('NAME', '')))),
                'type': str(row.get('Type', row.get('type', row.get('TYPE', '')))),
                'cabinet_name': str(row.get('Cabinet', row.get('cabinet', row.get('CABINET', '')))),
                'location': str(row.get('Location', row.get('location', row.get('LOCATION', '')))),
                'position': str(row.get('Position', row.get('position', row.get('POSITION', ''))))
            }

            # 验证必要字段
            if not chassis_data['name']:
                return None

            return chassis_data

        except Exception as e:
            self.logger.warning(f"创建机架数据失败: {e}")
            return None

    def _create_cabinet_from_row(self, row: pd.Series) -> Optional[Dict[str, Any]]:
        """
        从Excel行数据创建机柜数据

        Args:
            row: Excel行数据

        Returns:
            机柜数据字典或None
        """
        try:
            cabinet_data = {
                'name': str(row.get('Name', row.get('name', row.get('NAME', '')))),
                'number': str(row.get('Number', row.get('number', row.get('NUMBER', '')))),
                'location': str(row.get('Location', row.get('location', row.get('LOCATION', '')))),
                'template': str(row.get('Template', row.get('template', row.get('TEMPLATE', '')))),
                'type': str(row.get('Type', row.get('type', row.get('TYPE', ''))))
            }

            # 验证必要字段
            if not cabinet_data['name']:
                return None

            return cabinet_data

        except Exception as e:
            self.logger.warning(f"创建机柜数据失败: {e}")
            return None


class ExcelWriter:
    """Excel写入器"""
    
    def __init__(self):
        """初始化Excel写入器"""
        self.logger = get_logger(__name__)
    
    def write_excel_file(self, data: Dict[str, Any], file_path: str) -> bool:
        """
        写入Excel文件
        
        Args:
            data: 要写入的数据
            file_path: 输出文件路径
            
        Returns:
            是否写入成功
        """
        self.logger.info(f"写入Excel文件: {file_path}")
        
        try:
            # TODO: 使用pandas写入Excel文件
            # import pandas as pd
            # with pd.ExcelWriter(file_path) as writer:
            #     for sheet_name, sheet_data in data.items():
            #         sheet_data.to_excel(writer, sheet_name=sheet_name, index=False)
            
            return True
        except Exception as e:
            self.logger.error(f"写入Excel文件失败: {e}")
            return False
    
    def write_allocation_results(self, results: Dict[str, Any], file_path: str) -> bool:
        """
        写入分配结果
        
        Args:
            results: 分配结果数据
            file_path: 输出文件路径
            
        Returns:
            是否写入成功
        """
        # TODO: 实现分配结果的Excel输出格式
        return self.write_excel_file(results, file_path)
