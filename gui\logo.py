"""
EWReborn Logo组件
提供扁平化简约的品牌标识设计
"""

from PySide6.QtWidgets import QLabel, QWidget
from PySide6.QtGui import QPixmap, QPainter, QPen, QBrush, QLinearGradient, QColor
from PySide6.QtCore import Qt, QSize
from .styles import APPLE_COLORS


class EWRebornLogo(QLabel):
    """EWReborn品牌Logo组件"""
    
    def __init__(self, size: int = 80, parent=None):
        """
        初始化Logo组件
        
        Args:
            size: Logo尺寸
            parent: 父组件
        """
        super().__init__(parent)
        self.logo_size = size
        self.setFixedSize(size, size)
        self.setAlignment(Qt.AlignCenter)
        self._create_logo()
    
    def _create_logo(self):
        """创建扁平化简约Logo"""
        # 创建logo像素图
        pixmap = QPixmap(self.logo_size, self.logo_size)
        pixmap.fill(QColor(0, 0, 0, 0))  # 透明背景
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 计算比例
        scale = self.logo_size / 80.0
        
        # 设计理念：表示I/O点的自动化连接和分配
        self._draw_main_elements(painter, scale)
        self._draw_connection_flow(painter, scale)
        self._draw_system_architecture(painter, scale)
        
        painter.end()
        
        # 设置logo到标签
        self.setPixmap(pixmap)
    
    def _draw_main_elements(self, painter: QPainter, scale: float):
        """绘制主要元素 - 代表I/O点"""
        # 创建主色调渐变
        gradient = QLinearGradient(0, 0, self.logo_size, self.logo_size)
        gradient.setColorAt(0, QColor(APPLE_COLORS['primary']))
        gradient.setColorAt(0.5, QColor('#5AC8FA'))
        gradient.setColorAt(1, QColor(APPLE_COLORS['success']))
        
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.NoPen)
        
        # 左侧I/O点 - 输入
        left_x = int(10 * scale)
        left_y = int(25 * scale)
        point_size = int(20 * scale)
        painter.drawEllipse(left_x, left_y, point_size, point_size)
        
        # 右侧I/O点 - 输出
        right_x = int(50 * scale)
        right_y = int(25 * scale)
        painter.drawEllipse(right_x, right_y, point_size, point_size)
        
        # 中心处理单元 - 代表分配算法
        center_x = int(30 * scale)
        center_y = int(20 * scale)
        center_size = int(30 * scale)
        
        # 使用不同的渐变表示处理核心
        core_gradient = QLinearGradient(center_x, center_y, center_x + center_size, center_y + center_size)
        core_gradient.setColorAt(0, QColor(APPLE_COLORS['primary']))
        core_gradient.setColorAt(1, QColor('#007AFF'))
        
        painter.setBrush(QBrush(core_gradient))
        painter.drawRoundedRect(center_x, center_y, center_size, center_size, 8 * scale, 8 * scale)
    
    def _draw_connection_flow(self, painter: QPainter, scale: float):
        """绘制连接流 - 代表数据传输"""
        # 连接线
        painter.setBrush(QBrush(QColor(APPLE_COLORS['primary'])))
        painter.setPen(Qt.NoPen)
        
        line_y = int(32 * scale)
        line_height = int(6 * scale)
        
        # 左侧连接线
        painter.drawRect(int(25 * scale), line_y, int(10 * scale), line_height)
        
        # 右侧连接线
        painter.drawRect(int(45 * scale), line_y, int(10 * scale), line_height)
        
        # 数据流动点 - 表示实时处理
        painter.setBrush(QBrush(QColor(APPLE_COLORS['warning'])))
        
        dot_size = int(3 * scale)
        dot_y = int(33 * scale)
        
        # 流动的数据点
        painter.drawEllipse(int(27 * scale), dot_y, dot_size, dot_size)
        painter.drawEllipse(int(32 * scale), dot_y, dot_size, dot_size)
        painter.drawEllipse(int(37 * scale), dot_y, dot_size, dot_size)
        painter.drawEllipse(int(42 * scale), dot_y, dot_size, dot_size)
        painter.drawEllipse(int(47 * scale), dot_y, dot_size, dot_size)
    
    def _draw_system_architecture(self, painter: QPainter, scale: float):
        """绘制系统架构线条 - 代表整体系统"""
        painter.setPen(QPen(QColor(APPLE_COLORS['text_quaternary']), max(1, int(2 * scale))))
        painter.setBrush(Qt.NoBrush)
        
        # 顶部系统连接弧线
        top_rect_x = int(15 * scale)
        top_rect_y = int(5 * scale)
        top_rect_w = int(50 * scale)
        top_rect_h = int(25 * scale)
        painter.drawArc(top_rect_x, top_rect_y, top_rect_w, top_rect_h, 0, 180 * 16)
        
        # 底部系统连接弧线
        bottom_rect_x = int(15 * scale)
        bottom_rect_y = int(50 * scale)
        bottom_rect_w = int(50 * scale)
        bottom_rect_h = int(25 * scale)
        painter.drawArc(bottom_rect_x, bottom_rect_y, bottom_rect_w, bottom_rect_h, 180 * 16, 180 * 16)
        
        # 侧边装饰线 - 表示扩展性
        painter.setPen(QPen(QColor(APPLE_COLORS['text_quaternary']), max(1, int(1 * scale))))
        
        # 左侧装饰
        painter.drawLine(int(5 * scale), int(20 * scale), int(5 * scale), int(50 * scale))
        painter.drawLine(int(3 * scale), int(25 * scale), int(7 * scale), int(25 * scale))
        painter.drawLine(int(3 * scale), int(45 * scale), int(7 * scale), int(45 * scale))
        
        # 右侧装饰
        painter.drawLine(int(75 * scale), int(20 * scale), int(75 * scale), int(50 * scale))
        painter.drawLine(int(73 * scale), int(25 * scale), int(77 * scale), int(25 * scale))
        painter.drawLine(int(73 * scale), int(45 * scale), int(77 * scale), int(45 * scale))


class EWRebornMiniLogo(QLabel):
    """EWReborn迷你Logo - 用于标题栏等小空间"""
    
    def __init__(self, size: int = 24, parent=None):
        """
        初始化迷你Logo
        
        Args:
            size: Logo尺寸
            parent: 父组件
        """
        super().__init__(parent)
        self.logo_size = size
        self.setFixedSize(size, size)
        self.setAlignment(Qt.AlignCenter)
        self._create_mini_logo()
    
    def _create_mini_logo(self):
        """创建迷你版Logo"""
        # 创建logo像素图
        pixmap = QPixmap(self.logo_size, self.logo_size)
        pixmap.fill(QColor(0, 0, 0, 0))  # 透明背景
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 简化的设计 - 只保留核心元素
        scale = self.logo_size / 24.0
        
        # 创建简单的渐变
        gradient = QLinearGradient(0, 0, self.logo_size, self.logo_size)
        gradient.setColorAt(0, QColor(APPLE_COLORS['primary']))
        gradient.setColorAt(1, QColor(APPLE_COLORS['success']))
        
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.NoPen)
        
        # 简化的连接图标
        # 左点
        painter.drawEllipse(int(2 * scale), int(8 * scale), int(6 * scale), int(6 * scale))
        
        # 右点
        painter.drawEllipse(int(16 * scale), int(8 * scale), int(6 * scale), int(6 * scale))
        
        # 连接线
        painter.drawRect(int(6 * scale), int(10 * scale), int(12 * scale), int(2 * scale))
        
        # 中心点
        painter.setBrush(QBrush(QColor(APPLE_COLORS['warning'])))
        painter.drawEllipse(int(10 * scale), int(9 * scale), int(4 * scale), int(4 * scale))
        
        painter.end()
        
        # 设置logo到标签
        self.setPixmap(pixmap)


def create_logo_icon(size: int = 32) -> QPixmap:
    """
    创建Logo图标像素图
    
    Args:
        size: 图标尺寸
        
    Returns:
        QPixmap对象
    """
    logo = EWRebornLogo(size)
    return logo.pixmap()
