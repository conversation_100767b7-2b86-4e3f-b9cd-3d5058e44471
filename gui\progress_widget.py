"""
进度显示组件
显示I/O分配过程的详细进度信息
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QProgressBar, QTextEdit, QGroupBox, QPushButton,
    QGraphicsDropShadowEffect
)
from PySide6.QtCore import Qt, Signal, QTimer, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QFont, QTextCursor, QColor

from core.logger import get_logger
from .styles import APPLE_COLORS


class ProgressWidget(QWidget):
    """进度显示组件"""
    
    # 信号定义
    cancel_requested = Signal()  # 取消请求信号
    
    def __init__(self, parent=None):
        """
        初始化进度显示组件
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.logger = get_logger(__name__)
        self.start_time = None
        self.current_step = ""
        self.total_steps = 0
        self.completed_steps = 0
        
        # 初始化UI
        self._setup_ui()
        self._connect_signals()
        
        # 应用苹果风格增强
        self._apply_apple_enhancements()

        self.logger.info("进度显示组件初始化完成")
    
    def _setup_ui(self):
        """设置用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 总体进度组
        progress_group = QGroupBox("分配进度")
        progress_layout = QVBoxLayout(progress_group)
        
        # 主进度条
        self.main_progress_bar = QProgressBar()
        self.main_progress_bar.setMinimum(0)
        self.main_progress_bar.setMaximum(100)
        self.main_progress_bar.setValue(0)
        self.main_progress_bar.setTextVisible(True)
        progress_layout.addWidget(self.main_progress_bar)
        
        # 当前步骤信息
        step_layout = QHBoxLayout()
        step_layout.addWidget(QLabel("当前步骤:"))
        self.current_step_label = QLabel("就绪")
        self.current_step_label.setStyleSheet("font-weight: bold; color: #0066cc;")
        step_layout.addWidget(self.current_step_label)
        step_layout.addStretch()
        progress_layout.addLayout(step_layout)
        
        # 统计信息
        stats_layout = QHBoxLayout()
        
        self.processed_label = QLabel("已处理: 0")
        stats_layout.addWidget(self.processed_label)
        
        self.success_label = QLabel("成功: 0")
        self.success_label.setStyleSheet("color: green;")
        stats_layout.addWidget(self.success_label)
        
        self.failed_label = QLabel("失败: 0")
        self.failed_label.setStyleSheet("color: red;")
        stats_layout.addWidget(self.failed_label)
        
        self.time_label = QLabel("耗时: 00:00:00")
        stats_layout.addWidget(self.time_label)
        
        stats_layout.addStretch()
        progress_layout.addLayout(stats_layout)
        
        main_layout.addWidget(progress_group)
        
        # 详细日志组
        log_group = QGroupBox("详细日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        # 日志控制按钮
        log_control_layout = QHBoxLayout()
        
        self.clear_log_btn = QPushButton("清除日志")
        self.clear_log_btn.setMaximumWidth(100)
        log_control_layout.addWidget(self.clear_log_btn)
        
        self.auto_scroll_btn = QPushButton("自动滚动")
        self.auto_scroll_btn.setCheckable(True)
        self.auto_scroll_btn.setChecked(True)
        self.auto_scroll_btn.setMaximumWidth(100)
        log_control_layout.addWidget(self.auto_scroll_btn)
        
        log_control_layout.addStretch()
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setMaximumWidth(80)
        self.cancel_btn.setEnabled(False)
        self.cancel_btn.setStyleSheet("QPushButton { background-color: #ff6b6b; color: white; }")
        log_control_layout.addWidget(self.cancel_btn)
        
        log_layout.addLayout(log_control_layout)
        
        main_layout.addWidget(log_group)
        
        # 时间更新定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self._update_time)
    
    def _connect_signals(self):
        """连接信号和槽"""
        self.clear_log_btn.clicked.connect(self.clear_log)
        self.cancel_btn.clicked.connect(self._on_cancel_clicked)
    
    def start_progress(self, total_steps: int, title: str = "处理中"):
        """
        开始进度显示
        
        Args:
            total_steps: 总步骤数
            title: 进度标题
        """
        self.total_steps = total_steps
        self.completed_steps = 0
        self.start_time = datetime.now()
        
        self.main_progress_bar.setMaximum(total_steps)
        self.main_progress_bar.setValue(0)
        self.main_progress_bar.setFormat(f"{title} - %p%")
        
        self.current_step_label.setText("开始处理...")
        self.processed_label.setText("已处理: 0")
        self.success_label.setText("成功: 0")
        self.failed_label.setText("失败: 0")
        
        self.cancel_btn.setEnabled(True)
        
        # 启动时间更新
        self.timer.start(1000)  # 每秒更新一次
        
        self.add_log("INFO", f"开始{title}，总计{total_steps}个步骤")
        self.logger.info(f"进度显示开始: {title}, 总步骤: {total_steps}")
    
    def update_progress(self, completed: int, current_step: str = "", success_count: int = 0, failed_count: int = 0):
        """
        更新进度
        
        Args:
            completed: 已完成步骤数
            current_step: 当前步骤描述
            success_count: 成功数量
            failed_count: 失败数量
        """
        self.completed_steps = completed
        
        self.main_progress_bar.setValue(completed)
        
        if current_step:
            self.current_step = current_step
            self.current_step_label.setText(current_step)
        
        self.processed_label.setText(f"已处理: {completed}")
        self.success_label.setText(f"成功: {success_count}")
        self.failed_label.setText(f"失败: {failed_count}")
    
    def finish_progress(self, success: bool = True, message: str = ""):
        """
        完成进度显示
        
        Args:
            success: 是否成功完成
            message: 完成消息
        """
        self.timer.stop()
        self.cancel_btn.setEnabled(False)
        
        if success:
            self.main_progress_bar.setValue(self.total_steps)
            self.current_step_label.setText("完成")
            self.current_step_label.setStyleSheet("font-weight: bold; color: green;")
            final_message = message or "处理完成"
        else:
            self.current_step_label.setText("失败")
            self.current_step_label.setStyleSheet("font-weight: bold; color: red;")
            final_message = message or "处理失败"
        
        self.add_log("INFO" if success else "ERROR", final_message)
        
        # 计算总耗时
        if self.start_time:
            duration = datetime.now() - self.start_time
            duration_str = str(duration).split('.')[0]  # 移除微秒
            self.time_label.setText(f"耗时: {duration_str}")
        
        self.logger.info(f"进度显示完成: {'成功' if success else '失败'}")
    
    def add_log(self, level: str, message: str, timestamp: Optional[datetime] = None):
        """
        添加日志消息
        
        Args:
            level: 日志级别
            message: 日志消息
            timestamp: 时间戳
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        time_str = timestamp.strftime("%H:%M:%S")
        
        # 根据级别设置颜色
        if level == "ERROR":
            color = "red"
        elif level == "WARNING":
            color = "orange"
        elif level == "SUCCESS":
            color = "green"
        else:
            color = "black"
        
        log_entry = f'<span style="color: gray;">[{time_str}]</span> <span style="color: {color}; font-weight: bold;">{level}</span>: {message}'
        
        self.log_text.append(log_entry)
        
        # 自动滚动到底部
        if self.auto_scroll_btn.isChecked():
            cursor = self.log_text.textCursor()
            cursor.movePosition(QTextCursor.End)
            self.log_text.setTextCursor(cursor)
    
    def clear_log(self):
        """清除日志"""
        self.log_text.clear()
        self.logger.info("进度日志已清除")
    
    def _update_time(self):
        """更新耗时显示"""
        if self.start_time:
            duration = datetime.now() - self.start_time
            duration_str = str(duration).split('.')[0]  # 移除微秒
            self.time_label.setText(f"耗时: {duration_str}")
    
    def _on_cancel_clicked(self):
        """取消按钮点击处理"""
        self.cancel_requested.emit()
        self.cancel_btn.setEnabled(False)
        self.add_log("WARNING", "用户请求取消操作")
        self.logger.info("用户请求取消进度操作")
    
    def reset(self):
        """重置进度显示"""
        self.timer.stop()
        self.start_time = None
        self.current_step = ""
        self.total_steps = 0
        self.completed_steps = 0
        
        self.main_progress_bar.setValue(0)
        self.main_progress_bar.setFormat("%p%")
        self.current_step_label.setText("就绪")
        self.current_step_label.setStyleSheet("font-weight: bold; color: #0066cc;")
        
        self.processed_label.setText("已处理: 0")
        self.success_label.setText("成功: 0")
        self.failed_label.setText("失败: 0")
        self.time_label.setText("耗时: 00:00:00")
        
        self.cancel_btn.setEnabled(False)
        
        self.logger.info("进度显示已重置")

    def _apply_apple_enhancements(self):
        """应用苹果风格增强效果"""
        try:
            # 增强进度条样式
            self._enhance_progress_bar()

            # 增强按钮样式
            self._enhance_buttons()

            # 增强组框样式
            self._enhance_group_boxes()

            # 增强文本编辑器样式
            self._enhance_text_editor()

            self.logger.info("进度组件苹果风格增强应用成功")

        except Exception as e:
            self.logger.error(f"应用进度组件苹果风格增强失败: {e}")

    def _enhance_progress_bar(self):
        """增强进度条样式"""
        # 主进度条
        self.main_progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: none;
                border-radius: 10px;
                background-color: {APPLE_COLORS['surface_tertiary']};
                text-align: center;
                font-weight: 600;
                height: 20px;
                color: {APPLE_COLORS['text_primary']};
                font-size: 13px;
            }}
            QProgressBar::chunk {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                           stop: 0 {APPLE_COLORS['primary']},
                                           stop: 0.5 #5AC8FA,
                                           stop: 1 {APPLE_COLORS['success']});
                border-radius: 10px;
                margin: 1px;
            }}
        """)

        # 添加进度条阴影
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(8)
        shadow.setColor(QColor(0, 0, 0, 20))
        shadow.setOffset(0, 2)
        self.main_progress_bar.setGraphicsEffect(shadow)

    def _enhance_buttons(self):
        """增强按钮样式"""
        # 取消按钮
        self.cancel_btn.setProperty("class", "danger")
        self.cancel_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {APPLE_COLORS['error']};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: 600;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: #E6342A;
            }}
            QPushButton:pressed {{
                background-color: #CC2E24;
            }}
            QPushButton:disabled {{
                background-color: {APPLE_COLORS['text_quaternary']};
                color: {APPLE_COLORS['text_tertiary']};
            }}
        """)

        # 其他按钮
        for btn in [self.clear_log_btn, self.auto_scroll_btn]:
            btn.setProperty("class", "secondary")
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {APPLE_COLORS['surface_tertiary']};
                    color: {APPLE_COLORS['text_primary']};
                    border: 1px solid {APPLE_COLORS['border']};
                    border-radius: 6px;
                    padding: 6px 12px;
                    font-weight: 500;
                    font-size: 12px;
                }}
                QPushButton:hover {{
                    background-color: {APPLE_COLORS['surface_secondary']};
                    border-color: {APPLE_COLORS['separator']};
                }}
                QPushButton:checked {{
                    background-color: {APPLE_COLORS['primary']};
                    color: white;
                    border-color: {APPLE_COLORS['primary']};
                }}
            """)

    def _enhance_group_boxes(self):
        """增强组框样式"""
        for group_box in [self.findChild(QGroupBox)]:
            if group_box:
                # 添加阴影效果
                shadow = QGraphicsDropShadowEffect()
                shadow.setBlurRadius(15)
                shadow.setColor(QColor(0, 0, 0, 10))
                shadow.setOffset(0, 5)
                group_box.setGraphicsEffect(shadow)

    def _enhance_text_editor(self):
        """增强文本编辑器样式"""
        self.log_text.setStyleSheet(f"""
            QTextEdit {{
                background-color: {APPLE_COLORS['surface']};
                border: 1px solid {APPLE_COLORS['border']};
                border-radius: 10px;
                padding: 12px;
                font-family: 'SF Mono', 'Monaco', 'Consolas', 'Courier New', monospace;
                font-size: 11px;
                line-height: 1.5;
                color: {APPLE_COLORS['text_primary']};
            }}
            QTextEdit:focus {{
                border-color: {APPLE_COLORS['primary']};
                outline: none;
            }}
        """)

        # 添加文本编辑器阴影
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 15))
        shadow.setOffset(0, 3)
        self.log_text.setGraphicsEffect(shadow)
