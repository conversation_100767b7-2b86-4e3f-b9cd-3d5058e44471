"""
IO分配表生成功能测试
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import os
import tempfile
from pathlib import Path

from core.report_generator import ReportTemplate, ReportManager, ReportGeneratorFactory
from core.io_report_builder import IOReportBuilder
from core.naming_engine import <PERSON>ing<PERSON><PERSON><PERSON>, NamingContext, NamingElementType
from core.pidb_enhanced_reader import PidbEnhancedReader
from core.data_models import IOPoint, AllocationResult, SignalType


class TestIOReportGeneration(unittest.TestCase):
    """IO分配表生成测试"""
    
    def setUp(self):
        """测试设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.template_path = os.path.join(self.temp_dir, "test_template.xlsx")
        
        # 创建模拟的Excel模板文件
        self._create_mock_template()
        
        # 创建测试数据
        self.test_io_points = self._create_test_io_points()
        self.test_allocation_result = AllocationResult(
            success=True,
            allocated_points=self.test_io_points,
            failed_points=[],
            errors=[],
            warnings=[]
        )
    
    def tearDown(self):
        """测试清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def _create_mock_template(self):
        """创建模拟的Excel模板"""
        import openpyxl
        
        workbook = openpyxl.Workbook()
        
        # 创建cover工作表
        cover_sheet = workbook.active
        cover_sheet.title = "cover"
        cover_sheet['A1'] = "项目名称"
        
        # 创建IOlist工作表
        iolist_sheet = workbook.create_sheet("IOlist")
        
        # 添加表头
        headers = [
            "编号", "信号类型", "卡件型号", "ETP名", "通道号", "位号", "描述",
            "", "", "", "", "", "", "", "", "柜名", "安全栅/继电器/隔离器型号",
            "防雷栅型号", "ETP型号", "安全栅/继电器/隔离器编号", "防雷栅编号",
            "典型回路名", "端子排名", "端子号", "TR端子排名", "TR端子号",
            "电缆名", "pair number", "", ""
        ]
        
        for col, header in enumerate(headers, 1):
            iolist_sheet.cell(row=6, column=col, value=header)
        
        workbook.save(self.template_path)
        workbook.close()
    
    def _create_test_io_points(self):
        """创建测试IO点"""
        io_points = []
        
        for i in range(3):
            io_point = IOPoint(
                tag=f"AI_TEST_{i+1:03d}",
                signal_type=SignalType.AI,
                description=f"测试AI点{i+1}",
                cable_name=f"CABLE_{i+1}",
                pair_number=f"{i+1:02d}"
            )
            
            # 设置分配信息
            io_point.allocated_rack = f"Rack_{i+1}"
            io_point.allocated_slot = i + 1
            io_point.allocated_channel = (i * 4) + 1
            io_point.allocated_cabinet = f"1103-SIS-SYS-{i+1:03d}"
            io_point.allocated_rail = f"Rail_F"
            
            io_points.append(io_point)
        
        return io_points
    
    def test_report_template_creation(self):
        """测试报表模板创建"""
        template = ReportTemplate(self.template_path, "测试模板", "测试描述")
        
        self.assertEqual(template.name, "测试模板")
        self.assertEqual(template.description, "测试描述")
        self.assertTrue(template.template_path.exists())
        
        # 测试加载模板
        workbook = template.load_template()
        self.assertIsNotNone(workbook)
        self.assertIn("cover", workbook.sheetnames)
        self.assertIn("IOlist", workbook.sheetnames)
    
    def test_naming_engine(self):
        """测试命名引擎"""
        naming_engine = NamingEngine()
        
        # 创建测试IO点
        io_point = self.test_io_points[0]
        
        # 创建命名上下文
        additional_data = {
            'cabinet_numbers': {'1103-SIS-SYS-001': '01'},
            'card_info': {'part_number': '3721'}
        }
        context = NamingContext(io_point, additional_data)
        
        # 测试安全栅命名
        barrier_name = naming_engine.generate_name('barrier', context)
        self.assertTrue(len(barrier_name) > 0)
        self.assertTrue(barrier_name.startswith('BA'))
        
        # 测试端子排命名
        terminal_name = naming_engine.generate_name('terminal_block', context)
        self.assertTrue(len(terminal_name) > 0)
        self.assertTrue(terminal_name.startswith('TB'))
    
    @patch('core.pidb_enhanced_reader.ExcelReader')
    def test_pidb_enhanced_reader(self, mock_excel_reader):
        """测试增强的PIDB读取器"""
        # 模拟Excel数据
        mock_project_data = {
            'project': Mock(spec=['iterrows']),
            'chassis': Mock(spec=['iterrows']),
            'cabinet': Mock(spec=['iterrows'])
        }
        
        mock_excel_reader.return_value.read_excel_file.return_value = mock_project_data
        
        # 模拟iterrows返回值
        mock_project_data['project'].iterrows.return_value = [
            (0, ['项目名称', '测试项目']),
            (1, ['用户名称', '测试用户'])
        ]
        
        mock_project_data['chassis'].iterrows.return_value = []
        mock_project_data['cabinet'].iterrows.return_value = []
        
        reader = PidbEnhancedReader()
        
        with patch('pathlib.Path.exists', return_value=True):
            pidb_data = reader.read_pidb_file("test.xlsx")
        
        self.assertIn('project', pidb_data)
        self.assertIn('chassis', pidb_data)
        self.assertIn('cabinets', pidb_data)
    
    def test_io_report_builder_validation(self):
        """测试IO分配表构建器数据验证"""
        template = ReportTemplate(self.template_path, "测试模板")
        builder = IOReportBuilder(template)
        
        # 测试缺少数据源的情况
        errors = builder.validate_data_sources()
        self.assertTrue(len(errors) > 0)
        
        # 添加必需的数据源
        builder.add_data_source('allocation_result', self.test_allocation_result)
        builder.add_data_source('pidb_data', {'project': {}, 'cabinets': []})
        builder.add_data_source('wiring_typicals', {})
        builder.add_data_source('naming_rules', {})
        
        # 再次验证
        errors = builder.validate_data_sources()
        self.assertEqual(len(errors), 0)
    
    @patch('openpyxl.load_workbook')
    def test_io_report_generation(self, mock_load_workbook):
        """测试IO分配表生成"""
        # 创建模拟的工作簿
        mock_workbook = MagicMock()
        mock_cover_sheet = MagicMock()
        mock_iolist_sheet = MagicMock()
        
        mock_workbook.sheetnames = ['cover', 'IOlist']
        mock_workbook.__getitem__.side_effect = lambda name: {
            'cover': mock_cover_sheet,
            'IOlist': mock_iolist_sheet
        }[name]
        
        mock_load_workbook.return_value = mock_workbook
        
        # 创建报表构建器
        template = ReportTemplate(self.template_path, "测试模板")
        builder = IOReportBuilder(template)
        
        # 添加数据源
        builder.add_data_source('allocation_result', self.test_allocation_result)
        builder.add_data_source('pidb_data', {
            'project': {'project_name': '测试项目', 'user_name': '测试用户'},
            'cabinets': [],
            'cabinet_numbers': {'1103-SIS-SYS-001': '01'}
        })
        builder.add_data_source('wiring_typicals', {})
        builder.add_data_source('naming_rules', {})
        
        # 生成报表
        output_path = os.path.join(self.temp_dir, "test_output.xlsx")
        success = builder.generate_report(output_path)
        
        self.assertTrue(success)
        
        # 验证工作簿保存被调用
        mock_workbook.save.assert_called_once()
    
    def test_report_manager_integration(self):
        """测试报表管理器集成"""
        report_manager = ReportManager()
        
        # 注册模板
        template = ReportTemplate(self.template_path, "测试模板")
        report_manager.register_template("test_template", template)
        
        # 准备数据源
        data_sources = {
            'allocation_result': self.test_allocation_result,
            'pidb_data': {
                'project': {'project_name': '测试项目'},
                'cabinets': [],
                'cabinet_numbers': {}
            },
            'wiring_typicals': {},
            'naming_rules': {}
        }
        
        # 生成报表
        output_path = os.path.join(self.temp_dir, "manager_test_output.xlsx")
        
        with patch('openpyxl.load_workbook') as mock_load_workbook:
            mock_workbook = MagicMock()
            mock_workbook.sheetnames = ['cover', 'IOlist']
            mock_load_workbook.return_value = mock_workbook
            
            success = report_manager.generate_report(
                report_type='io_allocation',
                template_id='test_template',
                output_path=output_path,
                data_sources=data_sources
            )
            
            self.assertTrue(success)


if __name__ == '__main__':
    unittest.main()
