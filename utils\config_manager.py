"""
配置管理器
负责加载、保存和管理应用程序配置
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
import logging


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，默认为resources/config.json
        """
        self.project_root = Path(__file__).parent.parent
        self.config_file = config_file or self.project_root / "resources" / "config.json"
        self.user_config_file = self.project_root / "user_config.json"
        self._config = {}
        self._default_config = self._get_default_config()
        
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "application": {
                "name": "EWReborn - I/O点自动分配系统",
                "version": "1.0.0",
                "window": {
                    "width": 1400,
                    "height": 900,
                    "min_width": 1000,
                    "min_height": 600
                }
            },
            "data_paths": {
                "cabinet_profiles": "data/cabinet_profiles",
                "wiring_typical": "data/wiring_typical",
                "iodb": "data/iodb",
                "pidb": "data/pidb"
            },
            "allocation_settings": {
                "enable_parttype_matching": True,
                "enable_detailed_logging": True,
                "max_allocation_attempts": 1000,
                "allocation_order": "cable_name_pair_asc"
            },
            "validation_rules": {
                "tag_uniqueness": True,
                "cable_pair_validation": True,
                "cable_attribute_consistency": True
            },
            "logging": {
                "level": "INFO",
                "file_path": "logs/ewreborn.log",
                "max_file_size": "10MB",
                "backup_count": 5
            },
            "gui": {
                "theme": "default",
                "show_progress_details": True,
                "auto_save_results": True
            }
        }
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        # 从默认配置开始
        self._config = self._default_config.copy()
        
        # 尝试加载系统配置文件
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    system_config = json.load(f)
                self._merge_config(self._config, system_config)
            except Exception as e:
                logging.warning(f"加载系统配置文件失败: {e}")
        
        # 尝试加载用户配置文件
        if self.user_config_file.exists():
            try:
                with open(self.user_config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                self._merge_config(self._config, user_config)
            except Exception as e:
                logging.warning(f"加载用户配置文件失败: {e}")
        
        # 转换相对路径为绝对路径
        self._resolve_paths()
        
        return self._config
    
    def save_config(self, config: Optional[Dict[str, Any]] = None) -> bool:
        """
        保存配置到用户配置文件
        
        Args:
            config: 要保存的配置，如果为None则保存当前配置
            
        Returns:
            是否保存成功
        """
        if config is not None:
            self._config = config
        
        try:
            # 确保目录存在
            self.user_config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.user_config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            logging.error(f"保存用户配置文件失败: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def _merge_config(self, base: Dict[str, Any], override: Dict[str, Any]) -> None:
        """
        合并配置字典
        
        Args:
            base: 基础配置
            override: 覆盖配置
        """
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
    
    def _resolve_paths(self) -> None:
        """将相对路径转换为绝对路径"""
        data_paths = self._config.get('data_paths', {})
        for key, path in data_paths.items():
            if not os.path.isabs(path):
                data_paths[key] = str(self.project_root / path)
        
        # 处理日志文件路径
        log_path = self._config.get('logging', {}).get('file_path', '')
        if log_path and not os.path.isabs(log_path):
            self._config['logging']['file_path'] = str(self.project_root / log_path)
    
    def get_data_path(self, data_type: str) -> str:
        """
        获取数据路径
        
        Args:
            data_type: 数据类型 (cabinet_profiles, wiring_typical, iodb, pidb)
            
        Returns:
            数据路径
        """
        return self.get(f'data_paths.{data_type}', '')
    
    def reset_to_default(self) -> None:
        """重置为默认配置"""
        self._config = self._default_config.copy()
    
    @property
    def config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return self._config.copy()
