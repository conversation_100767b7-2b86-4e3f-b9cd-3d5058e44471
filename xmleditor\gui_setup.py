# gui_setup.py
from PySide6.QtWidgets import (
    QPushButton, QVBoxLayout, QHBoxLayout, QWidget,
    QSplitter, QTabWidget, QTableWidget,
    QMenu
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QAction, QKeySequence

# XmlTreeWidget 需要从 main_editor 导入，但这会造成循环导入
# 为了避免这种情况，XmlTreeWidget 的实例化仍然在 main_editor.py 中进行
# 或者将 XmlTreeWidget 也移到 gui_setup.py (如果它不强依赖 MainWindow 的太多逻辑)
# 目前，我们假设 XmlTreeWidget 在 main_editor.py 中定义并传入

def setup_main_ui(main_window, XmlTreeWidgetClass):
    """
    设置主窗口的核心UI元素。
    参数:
        main_window: MainWindow的实例。
        XmlTreeWidgetClass: XmlTreeWidget类的引用。
    """
    main_window.central_widget = QWidget()
    main_window.setCentralWidget(main_window.central_widget)
    main_layout = QVBoxLayout(main_window.central_widget)

    # 顶部按钮区域
    top_button_layout = QHBoxLayout()
    main_window.btn_import_xml = QPushButton("导入 XML")
    main_window.btn_import_xml.clicked.connect(main_window.import_xml_files) # 连接到 MainWindow 的方法
    top_button_layout.addWidget(main_window.btn_import_xml)

    main_window.btn_save_xml = QPushButton("保存勾选的 XML")
    main_window.btn_save_xml.clicked.connect(main_window.save_checked_xmls) # 连接到 MainWindow 的方法
    top_button_layout.addWidget(main_window.btn_save_xml)

    main_window.btn_new_xml = QPushButton("新建 XML Profile")
    main_window.btn_new_xml.clicked.connect(main_window.new_xml_profile)
    top_button_layout.addWidget(main_window.btn_new_xml)

    top_button_layout.addStretch() # 伸缩项，将按钮推向左侧
    main_layout.addLayout(top_button_layout)

    # 主内容分割器 (左右两部分)
    main_window.splitter = QSplitter(Qt.Horizontal)

    # 左侧：主类别选项卡 (WiringTypical, CabinetProfile) 和对应的树视图
    main_window.main_category_tabs = QTabWidget()
    main_window.main_category_tabs.currentChanged.connect(main_window.on_main_category_tab_changed)

    # "WiringTypical" 选项卡页面
    wt_page = QWidget()
    wt_layout = QVBoxLayout(wt_page)
    main_window.wiring_typical_tree = XmlTreeWidgetClass(main_window) # 使用传入的类创建实例
    main_window.wiring_typical_tree.setHeaderLabel("WiringTypicals (文件名或Name属性)")
    main_window.wiring_typical_tree.currentItemChanged.connect(
        lambda current, previous: main_window.on_tree_item_selected_in_active_tree(current, previous)
    )
    main_window.wiring_typical_tree.setContextMenuPolicy(Qt.CustomContextMenu) # 允许自定义上下文菜单
    main_window.wiring_typical_tree.customContextMenuRequested.connect(
        lambda pos: main_window.show_tree_context_menu(pos, main_window.wiring_typical_tree)
    )
    wt_layout.addWidget(main_window.wiring_typical_tree)
    main_window.main_category_tabs.addTab(wt_page, "WiringTypical")

    # "CabinetProfile" 选项卡页面
    cp_page = QWidget()
    cp_layout = QVBoxLayout(cp_page)
    main_window.cabinet_profile_tree = XmlTreeWidgetClass(main_window) # 使用传入的类创建实例
    main_window.cabinet_profile_tree.setHeaderLabel("CabinetProfiles (文件名或Name属性)")
    main_window.cabinet_profile_tree.currentItemChanged.connect(
        lambda current, previous: main_window.on_tree_item_selected_in_active_tree(current, previous)
    )
    main_window.cabinet_profile_tree.setContextMenuPolicy(Qt.CustomContextMenu) # 允许自定义上下文菜单
    main_window.cabinet_profile_tree.customContextMenuRequested.connect(
        lambda pos: main_window.show_tree_context_menu(pos, main_window.cabinet_profile_tree)
    )
    cp_layout.addWidget(main_window.cabinet_profile_tree)
    main_window.main_category_tabs.addTab(cp_page, "CabinetProfile")

    main_window.splitter.addWidget(main_window.main_category_tabs) # 左侧添加到分割器

    # 右侧：详细信息选项卡 (Attributes, Properties, Signatures)
    main_window.right_details_tabs = QTabWidget()

    # "组件属性" 选项卡
    main_window.attributes_tab = QWidget()
    attributes_page_layout = QVBoxLayout(main_window.attributes_tab)
    main_window.attributes_table = QTableWidget() # 用于显示和编辑 ProfileComponent 的属性
    main_window.attributes_table.setColumnCount(2)
    main_window.attributes_table.setHorizontalHeaderLabels(["属性", "值"])
    main_window.attributes_table.cellChanged.connect(main_window.on_attribute_table_changed) # 连接信号
    attributes_page_layout.addWidget(main_window.attributes_table)

    main_window.btn_add_attribute = QPushButton("+ 添加属性")
    main_window.btn_add_attribute.clicked.connect(main_window.add_new_attribute_to_table)
    attributes_page_layout.addWidget(main_window.btn_add_attribute, 0, Qt.AlignLeft) # 按钮左对齐

    main_window.right_details_tabs.addTab(main_window.attributes_tab, "组件属性")

    # "ProfileProperties" 选项卡
    main_window.properties_tab = QWidget()
    properties_layout = QVBoxLayout(main_window.properties_tab)
    main_window.properties_table = QTableWidget() # 用于显示 ProfileProperties
    main_window.properties_table.setColumnCount(5)
    main_window.properties_table.setHorizontalHeaderLabels(["名称", "类型", "值", "单位", "描述"])
    main_window.properties_table.cellChanged.connect(main_window.on_profile_property_table_changed) # 连接信号
    properties_layout.addWidget(main_window.properties_table)
    main_window.right_details_tabs.addTab(main_window.properties_tab, "ProfileProperties")

    # "ProfileSignatures" 选项卡
    main_window.signatures_tab = QWidget()
    signatures_layout = QVBoxLayout(main_window.signatures_tab)
    main_window.signatures_table = QTableWidget() # 用于显示 ProfileSignatures
    main_window.signatures_table.setColumnCount(5)
    main_window.signatures_table.setHorizontalHeaderLabels(["名称", "类型", "值", "单位", "描述"])
    main_window.signatures_table.cellChanged.connect(main_window.on_profile_signature_table_changed) # 连接信号
    signatures_layout.addWidget(main_window.signatures_table)
    main_window.right_details_tabs.addTab(main_window.signatures_tab, "ProfileSignatures")

    main_window.splitter.addWidget(main_window.right_details_tabs) # 右侧添加到分割器
    main_window.splitter.setSizes([400, 800]) # 设置分割器的初始大小比例
    main_layout.addWidget(main_window.splitter)

def setup_main_actions_and_menu(main_window):
    """设置主窗口的动作和菜单栏。"""
    # 文件菜单动作
    main_window.new_action = QAction("新建Profile", main_window)
    main_window.new_action.triggered.connect(main_window.new_xml_profile)

    main_window.open_action = QAction("导入XML...", main_window) # 文本中包含省略号，表示会打开对话框
    main_window.open_action.triggered.connect(main_window.import_xml_files)

    main_window.save_checked_action = QAction("保存勾选的 XML", main_window)
    main_window.save_checked_action.triggered.connect(main_window.save_checked_xmls)
    main_window.save_checked_action.setShortcut(QKeySequence.Save) # Ctrl+S 快捷键

    main_window.save_as_action = QAction("选中XML另存为...", main_window)
    main_window.save_as_action.triggered.connect(main_window.save_active_xml_as)

    # 编辑菜单动作
    main_window.delete_selected_action = QAction("删除选中项", main_window) # 用于树视图项目
    main_window.delete_selected_action.triggered.connect(main_window.delete_selected_item_smart)
    main_window.delete_selected_action.setShortcut(QKeySequence.Delete) # Delete 键快捷键

    main_window.rename_action = QAction("重命名选中项 (Name)", main_window)
    main_window.rename_action.triggered.connect(main_window.rename_selected_node_smart)
    main_window.rename_action.setShortcut(QKeySequence(Qt.Key_F2)) # F2 快捷键

    main_window.copy_selected_action = QAction("复制选中组件", main_window)
    main_window.copy_selected_action.triggered.connect(main_window.copy_selected_component)
    main_window.copy_selected_action.setShortcut(QKeySequence.Copy) # Ctrl+C

    main_window.paste_action = QAction("粘贴组件到选中项", main_window)
    main_window.paste_action.triggered.connect(main_window.paste_component_to_selected)
    main_window.paste_action.setShortcut(QKeySequence.Paste) # Ctrl+V
    main_window.paste_action.setEnabled(False) # 初始时禁用，直到有内容复制到剪贴板

    main_window.add_child_action = QAction("新建子组件到选中项", main_window)
    main_window.add_child_action.triggered.connect(main_window.add_new_component_as_child)

    main_window.add_sibling_action = QAction("新建兄弟组件到选中项", main_window)
    main_window.add_sibling_action.triggered.connect(main_window.add_new_component_as_sibling)

    # 创建菜单栏和菜单
    menu_bar = main_window.menuBar()
    file_menu = menu_bar.addMenu("文件(&F)")
    file_menu.addActions([main_window.new_action, main_window.open_action, main_window.save_checked_action, main_window.save_as_action])

    edit_menu = menu_bar.addMenu("编辑(&E)")
    edit_menu.addActions([main_window.add_child_action, main_window.add_sibling_action,
                          main_window.rename_action,
                          main_window.delete_selected_action,
                          main_window.copy_selected_action,
                          main_window.paste_action])

    # 确保树控件已创建后再添加动作 (这些动作主要用于树视图的上下文菜单，也使得快捷键在树控件聚焦时生效)
    if hasattr(main_window, 'wiring_typical_tree') and hasattr(main_window, 'cabinet_profile_tree'):
        for tree_widget in [main_window.wiring_typical_tree, main_window.cabinet_profile_tree]:
            if tree_widget: # 确保 tree_widget 不是 None
                tree_widget.addAction(main_window.delete_selected_action) # 使 Delete 快捷键在树上生效
                tree_widget.addAction(main_window.rename_action)        # 使 F2 快捷键在树上生效
                tree_widget.addAction(main_window.copy_selected_action)   # 使 Ctrl+C 在树上生效
                tree_widget.addAction(main_window.paste_action)         # 使 Ctrl+V 在树上生效