# main_editor.py

# 导入必要的模块
import sys
import os
import xml.etree.ElementTree as ET
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QPushButton, QVBoxLayout, QHBoxLayout, QWidget,
    QFileDialog, QMessageBox, QSplitter, QTreeWidget, QTreeWidgetItem,
    QTableWidget, QTableWidgetItem, QAbstractItemView, QMenu,
    QInputDialog, QTabWidget, QTreeWidgetItemIterator
)
from PySide6.QtCore import Qt, QMimeData, QByteArray
from PySide6.QtGui import QAction, QKeySequence, QDropEvent

# 导入自定义模块
import xml_operations
import editor_utils
import gui_setup

# 定义常量
MIME_TYPE_PROFILE_COMPONENT = 'application/xml-profilecomponent-node'
CLIPBOARD_WRAPPER_TAG = 'ClipboardContent'

# 自定义树控件类，支持拖放操作
class XmlTreeWidget(QTreeWidget):
    def __init__(self, parent_window=None):
        super().__init__(parent_window)
        # 启用拖放功能
        self.setDragEnabled(True)
        self.setAcceptDrops(True)
        self.setDropIndicatorShown(True)
        self.setDragDropMode(QAbstractItemView.InternalMove)
        self.main_window = parent_window

    def mimeTypes(self):
        # 定义支持的 MIME 类型
        return [MIME_TYPE_PROFILE_COMPONENT]

    def mimeData(self, items):
        # 生成拖动数据的 MIME 数据
        if not items or len(items) > 1:
            return None
        item = items[0]
        if item.parent() is None:
            return None
        element = item.data(0, Qt.UserRole)
        if element is None or element.tag != "ProfileComponent":
            return None
        mime_data = QMimeData()
        try:
            element_string_bytes = xml_operations.element_to_string(element, encoding='utf-8')
            mime_data.setData(MIME_TYPE_PROFILE_COMPONENT, element_string_bytes)
            self.main_window._dragged_item_cache = item
        except Exception as e:
            print(f"创建 mimeData 时出错: {e}")
            self.main_window._dragged_item_cache = None
            return None
        return mime_data

    def dropEvent(self, event: QDropEvent):
        # 处理拖放事件（仅支持同层级排序）
        print("\n--- dropEvent 开始 (同层级排序模式) ---")
        if not event.mimeData().hasFormat(MIME_TYPE_PROFILE_COMPONENT):
            print("  [dropEvent] MimeType不匹配, 事件忽略。")
            event.ignore()
            return

        source_item = getattr(self.main_window, '_dragged_item_cache', None)
        self.main_window._dragged_item_cache = None

        if source_item is None or source_item.parent() is None:
            print(f"  [dropEvent] 无效的源项目 (source_item: {source_item}), 事件忽略。")
            event.ignore()
            return

        source_element_to_move = source_item.data(0, Qt.UserRole)
        if source_element_to_move is None or source_element_to_move.tag != "ProfileComponent":
            print(f"  [dropEvent] 源项目数据无效 (element: {source_element_to_move}), 事件忽略。")
            event.ignore()
            return
        source_element_name = source_element_to_move.get('Name', 'N/A')
        print(f"  [dropEvent] 正在拖动源组件: {source_element_name} (原始项: {source_item.text(0)})")

        source_xml_tree, source_file_path = self.main_window.get_xml_info_for_item(source_item)
        if source_xml_tree is None:
            print("  [dropEvent] 无法获取源XML树, 事件忽略。")
            event.ignore()
            return
        source_root_element = source_xml_tree.getroot()

        original_components_parent, original_index = xml_operations.find_element_parent_and_index(
            source_root_element, source_element_to_move
        )

        if original_components_parent is None or original_components_parent.tag != "Components":
            print(f"  [dropEvent] 无法确定源元素的原始父级<Components> (parent: {original_components_parent}), 事件忽略。")
            QMessageBox.warning(self, "拖放错误", "无法确定源元素的原始父级<Components>。")
            event.ignore()
            return

        original_parent_pc_element_for_log, _ = xml_operations.find_element_parent_and_index(source_root_element, original_components_parent)
        original_parent_pc_name_for_log = original_parent_pc_element_for_log.get('Name', 'RootFilePC') if original_parent_pc_element_for_log is not None else "UnknownParentPC"
        print(f"  [dropEvent] 源组件原始父级<Components> (其父PC为: {original_parent_pc_name_for_log}), 原始索引: {original_index}")

        target_tree_item_at_drop = self.itemAt(event.position().toPoint())
        drop_indicator = self.dropIndicatorPosition()
        print(f"  [dropEvent] 目标树项: {target_tree_item_at_drop.text(0) if target_tree_item_at_drop is not None else 'None (空白区域)'}, 指示器: {drop_indicator}")

        if target_tree_item_at_drop is None or \
           target_tree_item_at_drop.parent() is None or \
           source_item.parent() != target_tree_item_at_drop.parent() or \
           drop_indicator == QAbstractItemView.DropIndicatorPosition.OnItem:
            print("[dropEvent] 非同层级排序操作，或拖放到项上（非之间），事件忽略。")
            QMessageBox.information(self, "拖放限制", "组件只能在同一父级下拖动到其他组件的上方或下方进行排序。")
            event.ignore()
            return

        target_element_at_drop = target_tree_item_at_drop.data(0, Qt.UserRole)
        if target_element_at_drop is None or target_element_at_drop.tag != "ProfileComponent":
            print("[dropEvent] 目标项不是有效的ProfileComponent，事件忽略。")
            event.ignore()
            return

        target_components_parent, index_of_target_in_parent = xml_operations.find_element_parent_and_index(
            source_root_element, target_element_at_drop
        )

        if original_components_parent != target_components_parent:
            print("[dropEvent] 源和目标的父<Components>节点不同，非同层排序，事件忽略。")
            QMessageBox.warning(self, "操作无效", "组件只能在同一父级内拖动排序。")
            event.ignore()
            return

        insert_index_in_target_xml = -1
        if drop_indicator == QAbstractItemView.DropIndicatorPosition.AboveItem:
            insert_index_in_target_xml = index_of_target_in_parent
        elif drop_indicator == QAbstractItemView.DropIndicatorPosition.BelowItem:
            insert_index_in_target_xml = index_of_target_in_parent + 1
        else:
            event.ignore()
            return

        print(f"  [dropEvent] 同层级排序。目标父<Components> (obj id: {id(target_components_parent)}), 计划插入索引: {insert_index_in_target_xml}")

        if (drop_indicator == QAbstractItemView.DropIndicatorPosition.BelowItem and insert_index_in_target_xml == original_index + 1 and source_item == target_tree_item_at_drop) or \
           (drop_indicator == QAbstractItemView.DropIndicatorPosition.AboveItem and insert_index_in_target_xml == original_index and source_item == target_tree_item_at_drop):
             print(f"  [dropEvent] 检测到拖放到自身原位附近，事件忽略。")
             event.ignore()
             return

        adjusted_temp_insert_index = insert_index_in_target_xml
        if insert_index_in_target_xml > original_index:
             adjusted_temp_insert_index -=1
        if adjusted_temp_insert_index == original_index:
            print(f"  [dropEvent] 计算后插入位置与原位置相同 ({original_index})，事件忽略。")
            event.ignore()
            return

        try:
            print(f"  [dropEvent] 尝试从父级 (obj id: {id(original_components_parent)}) 移除元素 '{source_element_name}' (原索引 {original_index})")
            print(f"  [dropEvent] 父级移除前的子元素 ({len(list(original_components_parent))}个): {[el.get('Name', 'N/A') for el in original_components_parent]}")
            original_components_parent.remove(source_element_to_move)
            print(f"  [dropEvent] 成功从父级移除。父级移除后的子元素 ({len(list(original_components_parent))}个): {[el.get('Name', 'N/A') for el in original_components_parent]}")
        except Exception as e:
            print(f"  [dropEvent] Exception: 移除元素时发生错误: {e}。")
            QMessageBox.warning(self, "拖放错误", f"移除元素时发生错误: {e}。")
            event.ignore()
            return

        final_insert_index = insert_index_in_target_xml
        if insert_index_in_target_xml > original_index:
            final_insert_index -= 1
        print(f"  [dropEvent] 调整后最终插入索引: {final_insert_index}")

        try:
            print(f"  [dropEvent] 尝试将元素 '{source_element_name}' 插入到父级 (obj id: {id(target_components_parent)}) 的索引 {final_insert_index}")
            print(f"  [dropEvent] 父级插入前的子元素 ({len(list(target_components_parent))}个): {[el.get('Name', 'N/A') for el in target_components_parent]}")

            actual_len_target_parent = len(list(target_components_parent))
            if final_insert_index < 0: final_insert_index = actual_len_target_parent
            if final_insert_index > actual_len_target_parent: final_insert_index = actual_len_target_parent

            target_components_parent.insert(final_insert_index, source_element_to_move)
            print(f"  [dropEvent] 成功插入到新位置。父级插入后的子元素 ({len(list(target_components_parent))}个): {[el.get('Name', 'N/A') for el in target_components_parent]}")

            event.acceptProposedAction()
            # 刷新树视图并选中移动的元素
            self.main_window.reload_specific_xml_in_active_tree(source_file_path)
            # 确保选中新创建的树项
            selected_item = self.main_window.find_and_select_item_by_element(source_element_to_move, source_file_path)
            if selected_item is None:
                print(f"  [dropEvent] 警告: 无法找到移动后的元素 '{source_element_name}' 的树项。")
                QMessageBox.warning(self, "拖放警告", f"组件 '{source_element_name}' 已移动，但无法在树视图中选中。")
            else:
                self.main_window.statusBar().showMessage(f"组件 '{source_element_name}' 已重新排序。", 3000)
        except Exception as e:
            print(f"  [dropEvent] Exception: 插入元素到新位置失败: {e}")
            QMessageBox.critical(self, "拖放插入错误", f"插入元素到新位置失败: {e}")
            try:
                original_components_parent.insert(original_index, source_element_to_move)
                print(f"  [dropEvent] 尝试将元素放回原处索引 {original_index}")
                self.main_window.reload_specific_xml_in_active_tree(source_file_path)
            except Exception as re_err:
                print(f"  [dropEvent] Exception: 放回元素失败: {re_err}")
                QMessageBox.critical(self, "严重错误", f"放回元素失败: {re_err}. XML数据可能已损坏。")
            event.ignore()
        print("--- dropEvent 结束 ---")

    def _is_ancestor_or_self(self, root_element, potential_ancestor_element, child_components_node):
        # 检查是否为祖先或自身
        if potential_ancestor_element is None or child_components_node is None or root_element is None:
            return False
        parent_pc_of_components, _ = xml_operations.find_element_parent_and_index(root_element, child_components_node)
        if parent_pc_of_components is None: return False
        if potential_ancestor_element == parent_pc_of_components: return True

        current_pc_ancestor = parent_pc_of_components
        while True:
            grand_parent_components_node, _ = xml_operations.find_element_parent_and_index(root_element, current_pc_ancestor)
            if grand_parent_components_node is None or grand_parent_components_node.tag != "Components": break
            great_grand_parent_pc, _ = xml_operations.find_element_parent_and_index(root_element, grand_parent_components_node)
            if great_grand_parent_pc is None: break
            if great_grand_parent_pc == potential_ancestor_element: return True
            if great_grand_parent_pc == current_pc_ancestor: break
            current_pc_ancestor = great_grand_parent_pc
            if current_pc_ancestor == root_element:
                return current_pc_ancestor == potential_ancestor_element
        return False

# 主窗口类
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        # 设置窗口标题和大小
        self.setWindowTitle("XML Profile 编辑器")
        self.setGeometry(100, 100, 1200, 800)

        # 初始化数据结构
        self.wiring_typical_files = {}
        self.cabinet_profile_files = {}
        self.active_tree_widget = None
        self.active_files_dict = None
        self.clipboard_element_string = None
        self._dragged_item_cache = None

        # 设置 UI 和菜单
        gui_setup.setup_main_ui(self, XmlTreeWidget)
        gui_setup.setup_main_actions_and_menu(self)
        self.on_main_category_tab_changed(0)

    def on_main_category_tab_changed(self, index):
        # 切换主类别选项卡
        if index == 0:
            self.active_tree_widget = self.wiring_typical_tree
            self.active_files_dict = self.wiring_typical_files
            self.setWindowTitle("XML Profile 编辑器 - WiringTypical")
        elif index == 1:
            self.active_tree_widget = self.cabinet_profile_tree
            self.active_files_dict = self.cabinet_profile_files
            self.setWindowTitle("XML Profile 编辑器 - CabinetProfile")
        else:
            self.active_tree_widget = None
            self.active_files_dict = None
            self.setWindowTitle("XML Profile 编辑器")
            return
        self.update_details_pane(None)
        self.clipboard_element_string = None
        self.paste_action.setEnabled(False)

    def update_details_pane(self, tree_item):
        # 更新右侧详细信息面板
        editor_utils.update_details_pane_content(tree_item,
                                                 self.attributes_table,
                                                 self.properties_table,
                                                 self.signatures_table,
                                                 self.btn_add_attribute)
        if tree_item is not None and tree_item.parent() is None:
            root_name = tree_item.data(0, Qt.UserRole).get("Name", "未知文件")
            current_category = self.main_category_tabs.tabText(self.main_category_tabs.currentIndex())
            self.setWindowTitle(f"XML Profile 编辑器 - {current_category} [{root_name}]")
        elif tree_item is None and self.active_tree_widget is not None:
            current_category = self.main_category_tabs.tabText(self.main_category_tabs.currentIndex())
            self.setWindowTitle(f"XML Profile 编辑器 - {current_category}")

    def on_attribute_table_changed(self, row, column):
        # 处理属性表格更改
        editor_utils.handle_attribute_table_change(self, row, column)

    def new_xml_profile(self):
        # 创建新的 XML Profile
        if not self.active_tree_widget or self.active_files_dict is None:
            QMessageBox.warning(self, "操作无效", "没有活动的类别选项卡被选中。")
            return

        name_attr, ok = QInputDialog.getText(self, '新建 XML Profile', '输入根 ProfileComponent 的 Name 属性:', text="NewProfile")
        if ok and name_attr:
            current_category_name = self.main_category_tabs.tab_starText(self.main_category_tabs.currentIndex())
            default_type = "WiringTypical" if current_category_name == "WiringTypical" else "CabinetProfile"

            save_dir_suggestion = os.path.join(os.getcwd(), current_category_name)
            if not os.path.exists(save_dir_suggestion):
                try: os.makedirs(save_dir_suggestion)
                except OSError: save_dir_suggestion = os.getcwd()

            default_filename = os.path.join(save_dir_suggestion, f"{name_attr}.xml")
            file_path, _ = QFileDialog.getSaveFileName(self, "选择新Profile的保存位置", default_filename, "XML 文件 (*.xml)")

            if not file_path: return

            if os.path.exists(file_path):
                reply = QMessageBox.question(self, "文件已存在", f"文件 '{file_path}' 已存在。是否覆盖?", QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                if reply == QMessageBox.No: return

            if file_path in self.active_files_dict:
                 QMessageBox.warning(self, "加载冲突", f"具有相同路径 '{os.path.basename(file_path)}' 的文件已在当前类别加载。")
                 return

            root_element = ET.Element("ProfileComponent", attrib={"Name": name_attr, "Type": default_type})
            ET.SubElement(root_element, "ProfileProperties")
            ET.SubElement(root_element, "ProfileSignatures")
            ET.SubElement(root_element, "Components")
            new_xml_tree = ET.ElementTree(root_element)

            try:
                xml_operations.save_elementtree_to_xml(new_xml_tree, file_path, create_subdir=True)
                self.active_files_dict[file_path] = new_xml_tree

                top_level_item = QTreeWidgetItem(self.active_tree_widget, [root_element.get("Name")])
                top_level_item.setData(0, Qt.UserRole, root_element)
                top_level_item.setData(0, Qt.UserRole + 1, file_path)
                top_level_item.setFlags(top_level_item.flags() | Qt.ItemIsUserCheckable)
                top_level_item.setCheckState(0, Qt.Unchecked)
                top_level_item.setExpanded(True)
                self.active_tree_widget.setCurrentItem(top_level_item)
                self.statusBar().showMessage(f"新建 Profile '{name_attr}' 并保存到 '{file_path}'。", 3000)
            except Exception as e:
                QMessageBox.critical(self, "创建失败", f"创建并保存新 Profile 失败: {e}")
        elif ok and not name_attr:
            QMessageBox.warning(self, "输入无效", "Name 属性不能为空。")

    def import_xml_files(self):
        # 导入 XML 文件
        if not self.active_tree_widget or self.active_files_dict is None:
            QMessageBox.warning(self, "操作无效", "没有活动的类别选项卡被选中。")
            return

        file_paths, _ = QFileDialog.getOpenFileNames(self,
                                                     f"导入XML文件到 {self.main_category_tabs.tabText(self.main_category_tabs.currentIndex())}",
                                                     "",
                                                     "XML 文件 (*.xml);;所有文件 (*)")
        if file_paths:
            loaded_count = 0
            for file_path in file_paths:
                if file_path in self.active_files_dict:
                    QMessageBox.information(self, "文件已存在", f"文件 '{os.path.basename(file_path)}' 已在此类别中加载。")
                    continue
                try:
                    xml_tree_obj = xml_operations.load_xml_to_elementtree(file_path)
                    root_element = xml_tree_obj.getroot()
                    if root_element.tag != "ProfileComponent":
                        QMessageBox.warning(self, "格式错误", f"文件 '{os.path.basename(file_path)}' 的根元素不是 ProfileComponent。")
                        continue

                    self.active_files_dict[file_path] = xml_tree_obj

                    item_display_name = root_element.get("Name", os.path.basename(file_path))
                    top_level_item = QTreeWidgetItem(self.active_tree_widget, [item_display_name])
                    top_level_item.setData(0, Qt.UserRole, root_element)
                    top_level_item.setData(0, Qt.UserRole + 1, file_path)
                    top_level_item.setFlags(top_level_item.flags() | Qt.ItemIsUserCheckable)
                    top_level_item.setCheckState(0, Qt.Unchecked)

                    components_node = root_element.find("Components")
                    if components_node is not None:
                        for child_pc_element in components_node.findall("ProfileComponent"):
                            editor_utils.populate_profile_component_recursive(child_pc_element, top_level_item, enable_checkbox_for_children=False)
                    loaded_count += 1
                except Exception as e:
                    QMessageBox.critical(self, "加载错误", f"加载文件 '{file_path}' 失败: {e}")

            if loaded_count > 0:
                self.statusBar().showMessage(f"成功加载 {loaded_count} 个XML文件到当前类别。", 3000)
                if self.active_tree_widget.topLevelItemCount() > 0:
                    first_item = self.active_tree_widget.topLevelItem(0)
                    first_item.setExpanded(True)
                    self.active_tree_widget.setCurrentItem(first_item)

    def get_checked_items(self):
        # 获取所有勾选的树项
        if self.active_tree_widget is None: return []
        checked_items = []
        iterator = QTreeWidgetItemIterator(self.active_tree_widget, QTreeWidgetItemIterator.All)
        while iterator.value():
            item = iterator.value()
            if item.checkState(0) == Qt.Checked:
                checked_items.append(item)
            iterator += 1
        return checked_items

    def get_checked_file_paths(self):
        # 获取勾选的文件路径
        checked_items = self.get_checked_items()
        checked_file_paths = set()
        for item in checked_items:
            if item.parent() is None:
                 file_path = item.data(0, Qt.UserRole + 1)
                 if file_path is not None:
                     checked_file_paths.add(file_path)
        return list(checked_file_paths)

    def save_checked_xmls(self):
        # 保存所有勾选的 XML 文件
        checked_file_paths = self.get_checked_file_paths()
        if not checked_file_paths:
            QMessageBox.information(self, "保存", "没有勾选任何文件项进行保存。")
            return

        saved_count = 0
        errors = []
        for file_path in checked_file_paths:
            if file_path in self.active_files_dict:
                try:
                    final_file_path = file_path
                    if os.path.exists(file_path):
                        reply = QMessageBox.question(self, "文件已存在", f"文件 '{file_path}' 已存在。是否覆盖?",
                                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                        if reply == QMessageBox.No:
                            final_file_path = xml_operations.get_unique_filepath(file_path)
                            xml_operations.save_elementtree_to_xml(self.active_files_dict[file_path], final_file_path)
                            # 更新文件字典和树视图
                            self.active_files_dict[final_file_path] = self.active_files_dict.pop(file_path)
                            top_item = self.get_top_level_item_for_file(file_path, self.active_tree_widget)
                            if top_item:
                                top_item.setData(0, Qt.UserRole + 1, final_file_path)
                                top_item.setText(0, os.path.basename(final_file_path))
                        else:
                            xml_operations.save_elementtree_to_xml(self.active_files_dict[file_path], final_file_path)
                    else:
                        xml_operations.save_elementtree_to_xml(self.active_files_dict[file_path], final_file_path)
                    saved_count += 1
                except Exception as e:
                    errors.append(f"'{os.path.basename(file_path)}': {e}")
            else:
                errors.append(f"'{os.path.basename(file_path)}': 文件不在活动字典中。")

        status_msg = f"成功保存 {saved_count} 个文件。"
        if errors:
            status_msg += f" 部分文件保存失败 ({len(errors)} 个)。"
            QMessageBox.warning(self, "保存完成 (有错误)", f"{status_msg}\n详细错误:\n" + "\n".join(errors))
        else:
            self.statusBar().showMessage(status_msg, 3000)

    def delete_selected_item_smart(self):
        # 删除选中的树项（文件或组件）
        if self.active_tree_widget is None: return
        selected_item = self.active_tree_widget.currentItem()
        if selected_item is None:
            QMessageBox.information(self, "删除", "没有选中任何项进行删除。")
            return

        reply = QMessageBox.question(self, "确认删除", f"确定要删除选中的项 '{selected_item.text(0)}' 吗？", QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.No: return

        xml_tree_obj, file_path = self.get_xml_info_for_item(selected_item)
        if xml_tree_obj is None or file_path is None:
            QMessageBox.warning(self, "删除错误", "无法获取选中项的XML信息。")
            return

        if selected_item.parent() is None:
            if file_path in self.active_files_dict:
                del self.active_files_dict[file_path]
            self.active_tree_widget.takeTopLevelItem(self.active_tree_widget.indexOfTopLevelItem(selected_item))
            self.update_details_pane(None)
            self.statusBar().showMessage(f"文件 '{selected_item.text(0)}' 已从列表移除。", 3000)
        else:
            element_to_delete = selected_item.data(0, Qt.UserRole)
            if element_to_delete == xml_tree_obj.getroot():
                QMessageBox.warning(self, "操作无效", "不能直接删除根组件，请删除对应的文件项。")
                return

            parent_components, _ = xml_operations.find_element_parent_and_index(xml_tree_obj.getroot(), element_to_delete)
            if parent_components is not None and parent_components.tag == "Components":
                try:
                    parent_components.remove(element_to_delete)
                    self.update_details_pane(None)
                    self.statusBar().showMessage(f"组件 '{element_to_delete.get('Name')}' 已删除。", 3000)
                    self.reload_specific_xml_in_active_tree(file_path)
                except Exception as e:
                    QMessageBox.critical(self, "删除组件错误", f"删除组件失败: {e}")
            else:
                QMessageBox.warning(self, "删除错误", "无法找到组件的父级<Components>节点。")

    def copy_selected_component(self):
        # 复制选中的组件
        if self.active_tree_widget is None: return
        selected_item = self.active_tree_widget.currentItem()
        if selected_item is None or selected_item.parent() is None:
            QMessageBox.information(self, "复制组件", "请选择一个组件项进行复制（非文件项）。")
            self.clipboard_element_string = None
            self.paste_action.setEnabled(False)
            return

        selected_element = selected_item.data(0, Qt.UserRole)
        if selected_element is not None and selected_element.tag == "ProfileComponent":
            try:
                copied_element_for_clipboard = xml_operations.deep_copy_element(selected_element)
                wrapper_element = ET.Element(CLIPBOARD_WRAPPER_TAG)
                wrapper_element.append(copied_element_for_clipboard)
                self.clipboard_element_string = xml_operations.element_to_string(wrapper_element, encoding='unicode')
                self.statusBar().showMessage(f"组件 '{selected_element.get('Name')}' 已复制。", 2000)
                self.paste_action.setEnabled(True)
            except Exception as e:
                self.clipboard_element_string = None
                self.paste_action.setEnabled(False)
                QMessageBox.critical(self, "复制错误", f"复制组件时出错: {e}")
        else:
            self.clipboard_element_string = None
            self.paste_action.setEnabled(False)

    def paste_component_to_selected(self):
        # 粘贴组件到选中的目标
        if self.clipboard_element_string is None:
            QMessageBox.information(self, "粘贴组件", "剪贴板为空。")
            return

        elements_to_paste_xml = []
        try:
            clipboard_content_root = xml_operations.string_to_element(self.clipboard_element_string)
            if clipboard_content_root is not None and clipboard_content_root.tag == CLIPBOARD_WRAPPER_TAG:
                elements_to_paste_xml = list(clipboard_content_root.findall("ProfileComponent"))
            elif clipboard_content_root is not None and clipboard_content_root.tag == "ProfileComponent":
                elements_to_paste_xml = [clipboard_content_root]

            if not elements_to_paste_xml:
                QMessageBox.warning(self, "粘贴错误", "剪贴板内容不是有效的ProfileComponent。")
                return
        except xml_operations.XMLOperationError as e:
            QMessageBox.warning(self, "粘贴错误", f"无法解析剪贴板内容: {e}")
            return

        if self.active_tree_widget is None: return
        selected_item = self.active_tree_widget.currentItem()
        if selected_item is None:
            QMessageBox.warning(self, "粘贴目标不明确", "请选择一个目标组件或文件项。")
            return

        xml_tree_obj, file_path = self.get_xml_info_for_item(selected_item)
        if xml_tree_obj is None:
            QMessageBox.warning(self, "粘贴错误", "无法确定目标XML文件。")
            return

        target_pc_element = selected_item.data(0, Qt.UserRole)
        if target_pc_element is None or target_pc_element.tag != "ProfileComponent":
            QMessageBox.warning(self, "粘贴错误", "选中的目标不是有效的ProfileComponent。")
            return

        target_components_node = target_pc_element.find("Components")
        if target_components_node is None:
            target_components_node = ET.SubElement(target_pc_element, "Components")

        pasted_count = 0
        first_pasted_element_in_tree = None
        try:
            for element_xml_from_clipboard in elements_to_paste_xml:
                new_element_instance = xml_operations.deep_copy_element(element_xml_from_clipboard)
                target_components_node.append(new_element_instance)
                if pasted_count == 0:
                    first_pasted_element_in_tree = new_element_instance
                pasted_count += 1
        except Exception as e:
            QMessageBox.critical(self, "粘贴错误", f"添加元素到目标位置失败: {e}")
            self.reload_specific_xml_in_active_tree(file_path)
            if first_pasted_element_in_tree is not None:
                self.find_and_select_item_by_element(first_pasted_element_in_tree, file_path)
            else:
                self.active_tree_widget.setCurrentItem(selected_item)
            return

        if pasted_count > 0:
             self.reload_specific_xml_in_active_tree(file_path)
             if first_pasted_element_in_tree is not None:
                 self.find_and_select_item_by_element(first_pasted_element_in_tree, file_path)
             self.statusBar().showMessage(f"已粘贴 {pasted_count} 个组件到 '{target_pc_element.get('Name')}'。", 2000)

    def reload_specific_xml_in_active_tree(self, file_path_to_reload):
        # 重新加载指定 XML 文件到活动树
        if self.active_tree_widget is None or file_path_to_reload not in self.active_files_dict:
            return

        xml_tree_obj = self.active_files_dict[file_path_to_reload]
        root_element_reloaded = xml_tree_obj.getroot()

        top_level_item_to_update = self.get_top_level_item_for_file(file_path_to_reload, self.active_tree_widget)
        if top_level_item_to_update is not None:
            is_expanded = top_level_item_to_update.isExpanded()
            current_check_state = top_level_item_to_update.checkState(0)
            # 保存当前选中的元素（而不是树项）
            current_selected_element = None
            current_selected_item = self.active_tree_widget.currentItem()
            if current_selected_item:
                current_selected_element = current_selected_item.data(0, Qt.UserRole)

            # 清除旧的子项
            for i in range(top_level_item_to_update.childCount() - 1, -1, -1):
                top_level_item_to_update.removeChild(top_level_item_to_update.child(i))

            # 更新顶级项
            top_level_item_to_update.setText(0, root_element_reloaded.get("Name", os.path.basename(file_path_to_reload)))
            top_level_item_to_update.setData(0, Qt.UserRole, root_element_reloaded)
            top_level_item_to_update.setCheckState(0, current_check_state)

            # 重新填充子组件
            components_node = root_element_reloaded.find("Components")
            if components_node is not None:
                for child_pc_element in components_node.findall("ProfileComponent"):
                    editor_utils.populate_profile_component_recursive(child_pc_element, top_level_item_to_update, enable_checkbox_for_children=False)

            top_level_item_to_update.setExpanded(is_expanded)

            # 恢复选中项
            if current_selected_element:
                found_item = self.find_item_by_element(current_selected_element)
                if found_item:
                    self.active_tree_widget.setCurrentItem(found_item)
                    self.active_tree_widget.scrollToItem(found_item)
                    self.update_details_pane(found_item)

    def find_item_by_element(self, element_to_find):
        # 根据元素查找树项
        if element_to_find is None or self.active_tree_widget is None:
            return None
        iterator = QTreeWidgetItemIterator(self.active_tree_widget, QTreeWidgetItemIterator.All)
        while iterator.value():
            item = iterator.value()
            if item.data(0, Qt.UserRole) == element_to_find:
                return item
            iterator += 1
        return None

    def find_and_select_item_by_element(self, element_to_find, in_file_path):
        # 查找并选中指定元素对应的树项
        if element_to_find is None or self.active_tree_widget is None or in_file_path is None:
            return None

        top_level_item_of_file = self.get_top_level_item_for_file(in_file_path, self.active_tree_widget)
        if top_level_item_of_file is None:
            print(f"[find_and_select] 未找到文件 '{in_file_path}' 的顶级项。")
            return None

        found_item = self._find_item_recursive(top_level_item_of_file, element_to_find)

        if found_item is not None:
            self.active_tree_widget.setCurrentItem(found_item)
            parent = found_item.parent()
            while parent is not None:
                parent.setExpanded(True)
                parent = parent.parent()
            self.active_tree_widget.scrollToItem(found_item, QAbstractItemView.EnsureVisible)
            self.update_details_pane(found_item)
            return found_item
        else:
            element_name_debug = element_to_find.get('Name', '未知元素') if element_to_find is not None else 'None'
            print(f"[find_and_select] 未能在文件 '{os.path.basename(in_file_path)}' (顶级项: '{top_level_item_of_file.text(0)}') 中找到元素 '{element_name_debug}' 对应的树项。")
        return None

    def _find_item_recursive(self, parent_item, element_to_find):
        # 递归查找树项
        if parent_item is not None and parent_item.data(0, Qt.UserRole) == element_to_find:
            return parent_item
        if parent_item is None:
            return None

        for i in range(parent_item.childCount()):
            child_item = parent_item.child(i)
            found_in_children = self._find_item_recursive(child_item, element_to_find)
            if found_in_children is not None:
                return found_in_children
        return None

    def add_new_component(self, as_child: bool):
        # 添加新组件（子节点或兄弟节点）
        if self.active_tree_widget is None: return
        selected_item = self.active_tree_widget.currentItem()
        if selected_item is None:
            QMessageBox.warning(self, "操作无效", "请先选择一个目标文件项或组件。")
            return

        xml_tree_obj, file_path = self.get_xml_info_for_item(selected_item)
        if xml_tree_obj is None:
            QMessageBox.warning(self, "操作无效", "无法确定目标XML文件。")
            return

        selected_pc_element = selected_item.data(0, Qt.UserRole)
        if selected_pc_element is None or selected_pc_element.tag != "ProfileComponent":
            QMessageBox.warning(self, "操作无效", "选中的项不是有效的 ProfileComponent。")
            return

        new_name_attr, ok = QInputDialog.getText(self, "新建组件", "输入新组件的 Name 属性:", text="NewComponent")
        if not (ok and new_name_attr):
            return

        new_component_element = ET.Element("ProfileComponent", attrib={"Name": new_name_attr, "Type": "DefaultType"})
        ET.SubElement(new_component_element, "ProfileProperties")
        ET.SubElement(new_component_element, "ProfileSignatures")
        ET.SubElement(new_component_element, "Components")

        target_components_node_for_new_element = None
        insert_idx = -1

        if as_child:
            target_components_node_for_new_element = selected_pc_element.find("Components")
            if target_components_node_for_new_element is None:
                target_components_node_for_new_element = ET.SubElement(selected_pc_element, "Components")
            insert_idx = -1
        else:
            if selected_item.parent() is None:
                QMessageBox.warning(self, "操作无效", "不能为文件项添加兄弟组件。")
                return
            parent_components_node_of_selected, index_of_selected = xml_operations.find_element_parent_and_index(
                xml_tree_obj.getroot(), selected_pc_element
            )
            if parent_components_node_of_selected is not None and parent_components_node_of_selected.tag == "Components":
                target_components_node_for_new_element = parent_components_node_of_selected
                insert_idx = index_of_selected + 1
            else:
                QMessageBox.critical(self, "添加错误", "无法找到选中组件的父<Components>节点。")
                return

        if target_components_node_for_new_element is not None:
            try:
                if insert_idx != -1 and 0 <= insert_idx <= len(list(target_components_node_for_new_element)):
                    target_components_node_for_new_element.insert(insert_idx, new_component_element)
                else:
                    target_components_node_for_new_element.append(new_component_element)

                self.reload_specific_xml_in_active_tree(file_path)
                self.find_and_select_item_by_element(new_component_element, file_path)
                self.statusBar().showMessage(f"组件 '{new_name_attr}' 已添加。", 3000)
            except Exception as e:
                QMessageBox.critical(self, "添加错误", f"添加新组件失败: {e}")
        else:
            QMessageBox.critical(self, "添加错误", "无法确定新组件的父<Components>节点。")

    def add_new_component_as_child(self):
        # 添加子组件
        self.add_new_component(as_child=True)

    def add_new_component_as_sibling(self):
        # 添加兄弟组件
        self.add_new_component(as_child=False)

    def closeEvent(self, event):
        # 处理窗口关闭事件
        reply = QMessageBox.question(self, '退出程序', "确定要退出吗？", QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()

    def on_tree_item_selected_in_active_tree(self, current_item, previous_item):
        # 处理树项选中事件
        self.update_details_pane(current_item)

    def on_profile_property_table_changed(self, row, column):
        # 处理 ProfileProperties 表格更改
        editor_utils.handle_sub_property_table_change(self, self.properties_table, row, column, "ProfileProperty")

    def on_profile_signature_table_changed(self, row, column):
        # 处理 ProfileSignatures 表格更改
        editor_utils.handle_sub_property_table_change(self, self.signatures_table, row, column, "ProfileSignature")

    def get_xml_info_for_item(self, tree_item):
        # 获取树项的 XML 信息
        if tree_item is None: return None, None
        top_level_item = tree_item
        while top_level_item.parent() is not None:
            top_level_item = top_level_item.parent()

        file_path = top_level_item.data(0, Qt.UserRole + 1)
        if file_path is not None and file_path in self.active_files_dict:
            return self.active_files_dict[file_path], file_path
        return None, None

    def get_active_selected_xml_info(self):
        # 获取当前选中的 XML 信息
        if self.active_tree_widget is None: return None, None
        current_item = self.active_tree_widget.currentItem()
        return self.get_xml_info_for_item(current_item)

    def save_active_xml_as(self):
        # 另存为当前 XML 文件
        xml_tree_to_save, original_file_path = self.get_active_selected_xml_info()
        if xml_tree_to_save is None or original_file_path is None:
            QMessageBox.warning(self, "未选择文件", "请先在列表中选择一个XML文件进行另存为。")
            return

        suggested_name = os.path.basename(original_file_path)
        new_file_path, _ = QFileDialog.getSaveFileName(self, "XML文件另存为", suggested_name, "XML 文件 (*.xml);;所有文件 (*)")

        if new_file_path:
            try:
                xml_operations.save_elementtree_to_xml(xml_tree_to_save, new_file_path)
                if new_file_path != original_file_path:
                    self.active_files_dict[new_file_path] = xml_tree_to_save
                    if original_file_path in self.active_files_dict:
                        del self.active_files_dict[original_file_path]

                    top_item = self.get_top_level_item_for_file(original_file_path, self.active_tree_widget)
                    if top_item is not None:
                        top_item.setData(0, Qt.UserRole + 1, new_file_path)
                        root_name_after_save = xml_tree_to_save.getroot().get("Name")
                        top_item.setText(0, root_name_after_save if root_name_after_save is not None else os.path.basename(new_file_path))
                        self.active_tree_widget.setCurrentItem(top_item)
                    else:
                        self.reload_active_tree_from_dict()
                else:
                    self.active_tree_widget.setCurrentItem(self.get_top_level_item_for_file(new_file_path, self.active_tree_widget))

                self.statusBar().showMessage(f"文件已另存为: {new_file_path}", 3000)
            except Exception as e:
                QMessageBox.critical(self, "另存为错误", f"文件另存为 '{new_file_path}' 失败: {e}")

    def get_top_level_item_for_file(self, file_path_to_find, tree_widget):
        # 获取文件路径对应的顶级树项
        if tree_widget is None: return None
        for i in range(tree_widget.topLevelItemCount()):
            item = tree_widget.topLevelItem(i)
            if item.data(0, Qt.UserRole + 1) == file_path_to_find:
                return item
        return None

    def reload_active_tree_from_dict(self):
        # 从字典重新加载活动树
        if self.active_tree_widget is None or self.active_files_dict is None: return

        current_selected_file_path = None
        current_selected_element = None
        if self.active_tree_widget.currentItem():
            sel_item = self.active_tree_widget.currentItem()
            _, current_selected_file_path = self.get_xml_info_for_item(sel_item)
            current_selected_element = sel_item.data(0, Qt.UserRole)

        self.active_tree_widget.clear()
        sorted_file_paths = sorted(self.active_files_dict.keys())

        item_to_reselect = None

        for file_path in sorted_file_paths:
            xml_tree_obj = self.active_files_dict[file_path]
            root_element = xml_tree_obj.getroot()
            item_display_name = root_element.get("Name", os.path.basename(file_path))
            top_level_item = QTreeWidgetItem(self.active_tree_widget, [item_display_name])
            top_level_item.setData(0, Qt.UserRole, root_element)
            top_level_item.setData(0, Qt.UserRole + 1, file_path)
            top_level_item.setFlags(top_level_item.flags() | Qt.ItemIsUserCheckable)
            top_level_item.setCheckState(0, Qt.Unchecked)

            components_node = root_element.find("Components")
            if components_node is not None:
                for child_pc_element in components_node.findall("ProfileComponent"):
                    editor_utils.populate_profile_component_recursive(child_pc_element, top_level_item, enable_checkbox_for_children=False)

            if file_path == current_selected_file_path:
                if root_element == current_selected_element:
                    item_to_reselect = top_level_item
                else:
                    found_child = self._find_item_recursive(top_level_item, current_selected_element)
                    if found_child:
                        item_to_reselect = found_child

        if item_to_reselect:
            self.active_tree_widget.setCurrentItem(item_to_reselect)
            parent = item_to_reselect.parent()
            while parent:
                parent.setExpanded(True)
                parent = parent.parent()
            if item_to_reselect.childCount() > 0: item_to_reselect.setExpanded(True)
        elif self.active_tree_widget.topLevelItemCount() > 0:
            first_item = self.active_tree_widget.topLevelItem(0)
            first_item.setExpanded(True)
            self.active_tree_widget.setCurrentItem(first_item)

    def show_tree_context_menu(self, position, tree_widget_ref):
        # 显示树右键菜单
        if tree_widget_ref != self.active_tree_widget: return

        selected_item = tree_widget_ref.itemAt(position)
        menu = QMenu(self)

        if selected_item is not None:
            element = selected_item.data(0, Qt.UserRole)
            is_profile_component = element is not None and element.tag == "ProfileComponent"
            is_top_level_file_item = selected_item.parent() is None

            if is_profile_component:
                menu.addAction(self.add_child_action)

            if not is_top_level_file_item and is_profile_component:
                menu.addAction(self.add_sibling_action)
                menu.addAction(self.copy_selected_action)

            if is_profile_component:
                menu.addAction(self.rename_action)

            if self.clipboard_element_string is not None and is_profile_component:
                self.paste_action.setText(f"粘贴组件到 '{selected_item.text(0)}'")
                menu.addAction(self.paste_action)

            menu.addAction(self.delete_selected_action)
        else:
            menu.addAction(self.open_action)

        menu.exec(tree_widget_ref.mapToGlobal(position))

    def rename_selected_node_smart(self):
        # 重命名选中的节点
        if self.active_tree_widget is None: return
        selected_item = self.active_tree_widget.currentItem()
        if selected_item is None: return

        element = selected_item.data(0, Qt.UserRole)
        if element is None or element.tag != "ProfileComponent": return

        is_top_level_file_item = selected_item.parent() is None
        prompt_title = "重命名文件 (根组件Name)" if is_top_level_file_item else "重命名组件"
        old_name = element.get("Name", "")

        new_name_attr, ok = QInputDialog.getText(self, prompt_title, "输入新的 Name 属性:", text=old_name)
        if ok and new_name_attr and new_name_attr != old_name:
            element.set("Name", new_name_attr)
            selected_item.setText(0, new_name_attr)

            if self.active_tree_widget.currentItem() == selected_item:
                self.update_details_pane(selected_item)

            status_msg = f"Name 属性已更新为 '{new_name_attr}'。"
            if is_top_level_file_item:
                status_msg += f" 文件 '{os.path.basename(selected_item.data(0, Qt.UserRole + 1))}' 的根组件已修改。"
            self.statusBar().showMessage(status_msg, 3000)
        elif ok and not new_name_attr:
            QMessageBox.warning(self, "输入无效", "Name 属性不能为空。")

    def add_new_attribute_to_table(self):
        # 为属性表格添加新属性
        if self.active_tree_widget is None: return
        selected_tree_item = self.active_tree_widget.currentItem()
        if selected_tree_item is None:
            QMessageBox.warning(self, "无选中项", "请先选择一个组件。")
            return

        pc_element = selected_tree_item.data(0, Qt.UserRole)
        if pc_element is None or pc_element.tag != "ProfileComponent":
            QMessageBox.warning(self, "无效选中项", "选中的项不是一个有效的 ProfileComponent。")
            return

        attr_name, ok_name = QInputDialog.getText(self, "添加新属性", "属性名称:")
        if not (ok_name and attr_name):
            return
        if attr_name in pc_element.attrib:
            QMessageBox.warning(self, "属性已存在", f"属性 '{attr_name}' 已存在。")
            return

        attr_value, ok_value = QInputDialog.getText(self, "添加新属性", f"属性 '{attr_name}' 的值:")
        if ok_value:
            pc_element.set(attr_name, attr_value)
            self.update_details_pane(selected_tree_item)
            self.statusBar().showMessage(f"已添加属性 '{attr_name}' = '{attr_value}'。", 3000)

if __name__ == '__main__':
    # 主程序入口
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())