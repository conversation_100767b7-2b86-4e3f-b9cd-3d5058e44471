"""
数据加载器 - 简化版本
负责加载机柜配置、典型回路、IODB和PIDB数据
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd

from core.logger import get_logger
from core.data_models import IOPoint, SignalType
from utils.excel_utils_simple import ExcelReader
from utils.xml_parser import XMLParser


class DataLoader:
    """数据加载器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化数据加载器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = get_logger(__name__)
        self.excel_reader = ExcelReader()
        self.xml_parser = XMLParser()

        # 获取数据路径配置
        self.data_paths = config.get('data_paths', {})
        self.cabinet_profiles_path = self.data_paths.get('cabinet_profiles', '01B_Cabinet Templates')
        self.wiring_typical_path = self.data_paths.get('wiring_typical', '01C_Wiring Typical')
        self.iodb_path = self.data_paths.get('iodb', '04A_IODB')
        self.pidb_path = self.data_paths.get('pidb', '04B_PIDB')
    
    def load_all_data(self) -> Dict[str, Any]:
        """
        加载所有配置数据
        
        Returns:
            包含所有数据的字典
        """
        self.logger.info("开始加载所有数据")
        
        result = {
            'cabinet_profiles': {},
            'wiring_typicals': {},
            'load_status': {
                'cabinet_profiles': False,
                'wiring_typicals': False,
                'iodb': False,
                'pidb': False
            }
        }
        
        try:
            # 加载机柜配置
            cabinet_profiles = self.load_cabinet_profiles()
            if cabinet_profiles:
                result['cabinet_profiles'] = cabinet_profiles
                result['load_status']['cabinet_profiles'] = True
            
            # 加载典型回路
            wiring_typicals = self.load_wiring_typicals()
            if wiring_typicals:
                result['wiring_typicals'] = wiring_typicals
                result['load_status']['wiring_typicals'] = True
            
            self.logger.info("数据加载完成")
            return result
            
        except Exception as e:
            self.logger.error(f"数据加载失败: {e}")
            raise
    
    def load_cabinet_profiles(self) -> Dict[str, Any]:
        """
        加载机柜配置文件
        
        Returns:
            机柜配置字典
        """
        self.logger.info(f"加载机柜配置: {self.cabinet_profiles_path}")
        
        cabinet_profiles = {}
        cabinet_path = Path(self.cabinet_profiles_path)
        
        if not cabinet_path.exists():
            self.logger.warning(f"机柜配置目录不存在: {cabinet_path}")
            return cabinet_profiles
        
        # 查找XML配置文件
        xml_files = list(cabinet_path.glob("*.xml"))
        # 也查找子目录中的XML文件
        for subdir in cabinet_path.iterdir():
            if subdir.is_dir():
                xml_files.extend(subdir.glob("*.xml"))

        self.logger.info(f"找到{len(xml_files)}个机柜配置文件")

        for xml_file in xml_files:
            try:
                config_data = self.xml_parser.parse_cabinet_xml(str(xml_file))
                cabinet_name = xml_file.stem
                cabinet_profiles[cabinet_name] = config_data
                self.logger.debug(f"加载机柜配置: {cabinet_name}")
            except Exception as e:
                self.logger.error(f"加载机柜配置文件失败 {xml_file}: {e}")
        
        self.logger.info("机柜配置文件加载成功")
        return cabinet_profiles
    
    def load_wiring_typicals(self) -> Dict[str, Any]:
        """
        加载典型回路文件
        
        Returns:
            典型回路字典
        """
        self.logger.info(f"加载典型回路: {self.wiring_typical_path}")
        
        wiring_typicals = {}
        typical_path = Path(self.wiring_typical_path)
        
        if not typical_path.exists():
            self.logger.warning(f"典型回路目录不存在: {typical_path}")
            return wiring_typicals
        
        # 查找XML文件
        xml_files = list(typical_path.glob("*.xml"))
        self.logger.info(f"找到{len(xml_files)}个典型回路文件")
        
        for xml_file in xml_files:
            try:
                # 使用XML解析器解析典型回路文件
                typical_config = self.xml_parser.parse_wiring_typical_xml(str(xml_file))
                typical_name = xml_file.stem
                wiring_typicals[typical_name] = typical_config
                self.logger.debug(f"加载典型回路: {typical_name}")
            except Exception as e:
                self.logger.error(f"加载典型回路文件失败 {xml_file}: {e}")
        
        self.logger.info("典型回路文件加载成功")
        return wiring_typicals
    
    def load_iodb_data(self, file_path: str) -> Dict[str, Any]:
        """
        加载IODB数据
        
        Args:
            file_path: IODB文件路径
            
        Returns:
            IODB数据字典
        """
        self.logger.info(f"加载IODB数据: {file_path}")
        
        try:
            # 使用Excel读取器读取文件
            excel_data = self.excel_reader.read_excel_file(file_path)
            
            # 处理Excel数据
            io_points = []
            cables = {}
            
            # 假设第一个工作表包含IODB数据
            if isinstance(excel_data, dict):
                sheet_name = list(excel_data.keys())[0]
                df = excel_data[sheet_name]
            else:
                df = excel_data
            
            self.logger.info(f"IODB工作表包含 {len(df)} 行数据")
            
            # 处理每一行数据
            for index, row in df.iterrows():
                try:
                    # 创建IOPoint对象，使用实际的列名
                    io_point = IOPoint(
                        tag=str(row.get('tagname', row.get('Tag', ''))),
                        signal_type=self._parse_signal_type(str(row.get('signaltype', row.get('Signal_Type', 'DI')))),
                        description=str(row.get('description', row.get('Description', ''))),
                        location=str(row.get('location', row.get('Location', ''))),
                        cabinet=str(row.get('Cabinet', '')),
                        rack=str(row.get('Rack', '')),
                        slot=str(row.get('Slot', '')),
                        channel=str(row.get('Channel', '')),
                        cable_name=str(row.get('cable name', row.get('Cable_Name', ''))),
                        pair_number=int(row.get('pair number', row.get('Pair_Number', 1))),
                        is_intrinsic=str(row.get('is', '')).upper() == 'IS',
                        system=str(row.get('system', row.get('System', ''))),
                        cable_type=str(row.get('cable type', row.get('Cable_Type', ''))),
                        wiring_typical=str(row.get('wiring typical', row.get('Wiring_Typical', '')))
                    )
                    io_points.append(io_point)

                    # 处理电缆信息
                    cable_name = str(row.get('cable name', row.get('Cable_Name', '')))
                    if cable_name and cable_name != 'nan' and cable_name.strip():
                        if cable_name not in cables:
                            cables[cable_name] = {
                                'name': cable_name,
                                'io_points': [],
                                'pair_size': int(row.get('cable size', row.get('Pair_Size', 1)))
                            }

                        cables[cable_name]['io_points'].append(io_point)
                        
                except Exception as e:
                    self.logger.warning(f"处理IODB行数据失败 (行{index}): {e}")
                    continue
            
            self.logger.info(f"成功处理 {len(io_points)} 个I/O点，{len(cables)} 条电缆")
            self.logger.info(f"成功加载IODB数据: {len(io_points)}个I/O点，{len(cables)}条电缆")
            
            return {
                'io_points': io_points,
                'cables': cables,
                'summary': {
                    'total_points': len(io_points),
                    'total_cables': len(cables)
                }
            }
            
        except Exception as e:
            self.logger.error(f"加载IODB数据失败: {e}")
            raise
    
    def load_pidb_data(self, file_path: str) -> Dict[str, Any]:
        """
        加载PIDB数据

        Args:
            file_path: PIDB文件路径

        Returns:
            PIDB数据字典
        """
        self.logger.info(f"加载PIDB数据: {file_path}")

        try:
            # 使用Excel读取器读取文件
            excel_data = self.excel_reader.read_excel_file(file_path)

            # 解析PIDB数据结构
            racks = []
            cabinets = []

            # 分析Excel数据结构
            if isinstance(excel_data, dict):
                for sheet_name, df in excel_data.items():
                    self.logger.info(f"分析PIDB工作表: {sheet_name}, 行数: {len(df)}")

                    # 尝试从工作表名称推断内容类型
                    if 'rack' in sheet_name.lower() or 'chassis' in sheet_name.lower():
                        sheet_racks = self._extract_racks_from_pidb(df, sheet_name)
                        racks.extend(sheet_racks)
                    elif 'cabinet' in sheet_name.lower():
                        sheet_cabinets = self._extract_cabinets_from_pidb(df, sheet_name)
                        cabinets.extend(sheet_cabinets)
                    else:
                        # 通用解析，尝试识别机架和机柜信息
                        sheet_racks, sheet_cabinets = self._parse_generic_pidb_sheet(df, sheet_name)
                        racks.extend(sheet_racks)
                        cabinets.extend(sheet_cabinets)

            # 如果没有找到明确的机架和机柜信息，创建默认结构
            if not racks and not cabinets:
                self.logger.info("未找到明确的机架和机柜信息，创建默认结构")
                # 基于项目信息创建默认机柜
                cabinets = [
                    {'name': 'PPG_BAR', 'type': 'Control Cabinet', 'location': 'Field'},
                    {'name': 'PPG_RIO', 'type': 'Remote I/O Cabinet', 'location': 'Field'},
                    {'name': 'PPG_SYS', 'type': 'System Cabinet', 'location': 'Control Room'}
                ]

                # 为每个机柜创建默认机架
                for cabinet in cabinets:
                    for i in range(2):  # 每个机柜2个机架
                        racks.append({
                            'name': f"{cabinet['name']}_R{i+1:02d}",
                            'cabinet': cabinet['name'],
                            'max_slots': 16,
                            'type': 'Standard Rack'
                        })

            return {
                'racks': racks,
                'cabinets': cabinets,
                'raw_data': excel_data,
                'summary': {
                    'total_racks': len(racks),
                    'total_cabinets': len(cabinets),
                    'loaded': True
                }
            }

        except Exception as e:
            self.logger.error(f"加载PIDB数据失败: {e}")
            raise
    
    def create_data_models(self, iodb_data: Dict[str, Any], pidb_data: Dict[str, Any], 
                          cabinet_profiles: Dict[str, Any], wiring_typicals: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建数据模型对象
        
        Args:
            iodb_data: IODB数据
            pidb_data: PIDB数据
            cabinet_profiles: 机柜配置
            wiring_typicals: 典型回路
            
        Returns:
            数据模型字典
        """
        self.logger.info("创建数据模型对象")
        
        try:
            # 从IODB数据提取信息
            io_points = iodb_data.get('io_points', [])
            cables_dict = iodb_data.get('cables', {})
            cables = list(cables_dict.values())
            
            # 创建机柜对象
            cabinets = []
            for cabinet_name, cabinet_config in cabinet_profiles.items():
                cabinet_obj = {
                    'name': cabinet_name,
                    'config': cabinet_config,
                    'rails': cabinet_config.get('rails', [])
                }
                cabinets.append(cabinet_obj)
                self.logger.debug(f"机柜 {cabinet_name} 创建成功，包含 {len(cabinet_obj['rails'])} 个导轨")
            
            # 创建典型回路对象
            wiring_typical_objects = []
            for typical_name, typical_config in wiring_typicals.items():
                # 创建一个简单的对象类来模拟典型回路
                class WiringTypical:
                    def __init__(self, name, config):
                        self.name = name
                        self.config = config
                        self.components = config.get('components', [])

                typical_obj = WiringTypical(typical_name, typical_config)
                wiring_typical_objects.append(typical_obj)
            
            self.logger.info(f"数据模型创建完成: {len(io_points)}个I/O点, {len(cables)}条电缆, {len(cabinets)}个机柜, {len(wiring_typical_objects)}个典型回路")
            
            return {
                'io_points': io_points,
                'cables': cables,
                'cabinets': cabinets,
                'wiring_typicals': wiring_typical_objects
            }
            
        except Exception as e:
            self.logger.error(f"创建数据模型失败: {e}")
            raise
    
    def _extract_racks_from_pidb(self, df: pd.DataFrame, sheet_name: str) -> List[Dict[str, Any]]:
        """
        从PIDB工作表提取机架信息

        Args:
            df: 数据框
            sheet_name: 工作表名称

        Returns:
            机架列表
        """
        racks = []
        # 实现机架提取逻辑
        # 这里是简化实现，实际需要根据PIDB格式调整
        return racks

    def _extract_cabinets_from_pidb(self, df: pd.DataFrame, sheet_name: str) -> List[Dict[str, Any]]:
        """
        从PIDB工作表提取机柜信息

        Args:
            df: 数据框
            sheet_name: 工作表名称

        Returns:
            机柜列表
        """
        cabinets = []
        # 实现机柜提取逻辑
        # 这里是简化实现，实际需要根据PIDB格式调整
        return cabinets

    def _parse_generic_pidb_sheet(self, df: pd.DataFrame, sheet_name: str) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        通用PIDB工作表解析

        Args:
            df: 数据框
            sheet_name: 工作表名称

        Returns:
            (机架列表, 机柜列表)
        """
        racks = []
        cabinets = []
        # 实现通用解析逻辑
        return racks, cabinets

    def _parse_signal_type(self, signal_type_str: str) -> SignalType:
        """
        解析信号类型字符串

        Args:
            signal_type_str: 信号类型字符串

        Returns:
            SignalType枚举值
        """
        try:
            return SignalType(signal_type_str.upper())
        except ValueError:
            self.logger.warning(f"未知信号类型: {signal_type_str}，使用默认值DI")
            return SignalType.DI
