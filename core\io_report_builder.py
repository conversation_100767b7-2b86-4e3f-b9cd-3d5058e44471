"""
IO分配表构建器
负责生成IO分配报表，特别处理spare点的显示格式
"""

import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from core.logger import get_logger
from core.data_models import IOPoint


@dataclass
class ReportTemplate:
    """报表模板"""
    template_path: str
    name: str
    description: str = ""


class IOReportBuilder:
    """IO分配表构建器"""
    
    def __init__(self, template: ReportTemplate):
        """
        初始化IO分配表构建器
        
        Args:
            template: 报表模板
        """
        self.template = template
        self.logger = get_logger(__name__)
        self.data_sources = {}
    
    def add_data_source(self, name: str, data: Any):
        """
        添加数据源
        
        Args:
            name: 数据源名称
            data: 数据内容
        """
        self.data_sources[name] = data
        self.logger.debug(f"添加数据源: {name}")
    
    def validate_data_sources(self) -> List[str]:
        """
        验证数据源完整性
        
        Returns:
            错误列表
        """
        errors = []
        required_sources = ['allocation_result', 'pidb_data', 'wiring_typicals', 'naming_rules']
        
        for source in required_sources:
            if source not in self.data_sources:
                errors.append(f"缺少必需的数据源: {source}")
        
        return errors
    
    def generate_report(self, output_path: str) -> bool:
        """
        生成IO分配报表
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            生成是否成功
        """
        try:
            self.logger.info(f"开始生成IO分配报表: {output_path}")
            
            # 验证数据源
            errors = self.validate_data_sources()
            if errors:
                for error in errors:
                    self.logger.error(error)
                return False
            
            # 获取分配结果
            allocation_result = self.data_sources.get('allocation_result')
            if not allocation_result:
                self.logger.error("分配结果为空")
                return False
            
            # 处理IO点数据，特别处理spare点
            processed_points = self._process_io_points(allocation_result.allocated_points)
            
            # 这里应该有实际的Excel生成逻辑
            # 由于没有openpyxl等依赖，这里只是模拟
            self.logger.info(f"处理了 {len(processed_points)} 个IO点")
            
            # 统计spare点
            spare_count = sum(1 for point in processed_points if point.get('is_spare', False))
            regular_count = len(processed_points) - spare_count
            
            self.logger.info(f"常规IO点: {regular_count}, Spare点: {spare_count}")
            self.logger.info("IO分配报表生成完成")
            
            return True
            
        except Exception as e:
            self.logger.error(f"生成IO分配报表失败: {e}")
            return False
    
    def _process_io_points(self, io_points: List[IOPoint]) -> List[Dict[str, Any]]:
        """
        处理IO点数据，特别处理spare点的显示格式
        
        Args:
            io_points: IO点列表
            
        Returns:
            处理后的IO点数据
        """
        processed_points = []
        
        for point in io_points:
            processed_point = {
                'tag': self._format_tag_for_display(point),
                'description': self._format_description_for_display(point),
                'signal_type': point.signal_type.value if hasattr(point.signal_type, 'value') else str(point.signal_type),
                'location': point.location,
                'cabinet': point.cabinet,
                'rack': point.rack,
                'slot': point.slot,
                'channel': point.channel,
                'cable_name': point.cable_name,
                'pair_number': point.pair_number,
                'is_intrinsic': point.is_intrinsic,
                'system': point.system,
                'cable_type': point.cable_type,
                'wiring_typical': point.wiring_typical,
                'is_spare': getattr(point, 'is_spare', False)
            }
            
            processed_points.append(processed_point)
        
        return processed_points
    
    def _format_tag_for_display(self, point: IOPoint) -> str:
        """
        格式化Tag名用于显示
        对于spare点，去掉数字后缀和下划线，显示为"SPARE"
        
        Args:
            point: IO点对象
            
        Returns:
            格式化后的Tag名
        """
        if hasattr(point, 'is_spare') and point.is_spare:
            # 对于spare点，显示为"SPARE"
            return "SPARE"
        else:
            # 对于常规点，显示原始tag名
            return point.tag
    
    def _format_description_for_display(self, point: IOPoint) -> str:
        """
        格式化描述用于显示
        对于spare点，统一显示为"-"
        
        Args:
            point: IO点对象
            
        Returns:
            格式化后的描述
        """
        if hasattr(point, 'is_spare') and point.is_spare:
            # 对于spare点，描述统一为"-"
            return "-"
        else:
            # 对于常规点，显示原始描述
            return point.description


class ReportManager:
    """报表管理器"""
    
    def __init__(self):
        """初始化报表管理器"""
        self.logger = get_logger(__name__)
        self.templates = {}
        self.generators = {}
    
    def register_template(self, template_id: str, template: ReportTemplate):
        """
        注册报表模板
        
        Args:
            template_id: 模板ID
            template: 报表模板
        """
        self.templates[template_id] = template
        self.logger.info(f"注册报表模板: {template_id}")
    
    def generate_report(self, report_type: str, template_id: str, 
                       output_path: str, data_sources: Dict[str, Any]) -> bool:
        """
        生成报表
        
        Args:
            report_type: 报表类型
            template_id: 模板ID
            output_path: 输出路径
            data_sources: 数据源
            
        Returns:
            生成是否成功
        """
        try:
            if template_id not in self.templates:
                self.logger.error(f"未找到模板: {template_id}")
                return False
            
            template = self.templates[template_id]
            
            if report_type == 'io_allocation':
                builder = IOReportBuilder(template)
                
                # 添加数据源
                for name, data in data_sources.items():
                    builder.add_data_source(name, data)
                
                # 生成报表
                return builder.generate_report(output_path)
            else:
                self.logger.error(f"不支持的报表类型: {report_type}")
                return False
                
        except Exception as e:
            self.logger.error(f"生成报表失败: {e}")
            return False


class ReportGeneratorFactory:
    """报表生成器工厂"""
    
    _generators = {
        'io_allocation': IOReportBuilder
    }
    
    @classmethod
    def get_available_types(cls) -> List[str]:
        """获取可用的报表类型"""
        return list(cls._generators.keys())
    
    @classmethod
    def create_generator(cls, report_type: str, template: ReportTemplate):
        """
        创建报表生成器
        
        Args:
            report_type: 报表类型
            template: 报表模板
            
        Returns:
            报表生成器实例
        """
        if report_type in cls._generators:
            return cls._generators[report_type](template)
        else:
            raise ValueError(f"不支持的报表类型: {report_type}")
