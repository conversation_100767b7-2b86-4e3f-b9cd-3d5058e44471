"""
配置界面组件
负责应用程序配置的用户界面
"""

import logging
from typing import Dict, Any

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QPushButton, QLabel, QLineEdit, QSpinBox, QCheckBox,
    QComboBox, QGroupBox, QTabWidget, QMessageBox,
    QFileDialog, QTextEdit, QTableWidget, QTableWidgetItem, QHeaderView
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from core.logger import get_logger


class ConfigWidget(QWidget):
    """配置界面组件"""
    
    # 信号定义
    config_changed = Signal(dict)  # 配置更改信号
    
    def __init__(self, config: Dict[str, Any], parent=None):
        """
        初始化配置界面
        
        Args:
            config: 应用程序配置
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.config = config.copy()
        self.logger = get_logger(__name__)
        
        # 初始化UI
        self._setup_ui()
        self._load_config_values()
        self._connect_signals()
        
        self.logger.info("配置界面初始化完成")
    
    def _setup_ui(self):
        """设置用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建选项卡控件
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 创建各个配置选项卡
        self._create_general_tab()
        self._create_data_paths_tab()
        self._create_allocation_tab()
        self._create_spare_tab()
        self._create_logging_tab()
        
        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.reset_btn = QPushButton("重置为默认")
        button_layout.addWidget(self.reset_btn)
        
        self.apply_btn = QPushButton("应用")
        button_layout.addWidget(self.apply_btn)
        
        self.save_btn = QPushButton("保存")
        button_layout.addWidget(self.save_btn)
        
        main_layout.addLayout(button_layout)
    
    def _create_general_tab(self):
        """创建常规设置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 应用程序设置组
        app_group = QGroupBox("应用程序设置")
        app_layout = QGridLayout(app_group)
        
        # 窗口大小设置
        app_layout.addWidget(QLabel("窗口宽度:"), 0, 0)
        self.window_width_spin = QSpinBox()
        self.window_width_spin.setRange(800, 2560)
        self.window_width_spin.setSuffix(" px")
        app_layout.addWidget(self.window_width_spin, 0, 1)
        
        app_layout.addWidget(QLabel("窗口高度:"), 1, 0)
        self.window_height_spin = QSpinBox()
        self.window_height_spin.setRange(600, 1440)
        self.window_height_spin.setSuffix(" px")
        app_layout.addWidget(self.window_height_spin, 1, 1)
        
        layout.addWidget(app_group)
        
        # GUI设置组
        gui_group = QGroupBox("界面设置")
        gui_layout = QVBoxLayout(gui_group)
        
        self.show_progress_details_cb = QCheckBox("显示详细进度信息")
        gui_layout.addWidget(self.show_progress_details_cb)
        
        self.auto_save_results_cb = QCheckBox("自动保存结果")
        gui_layout.addWidget(self.auto_save_results_cb)
        
        layout.addWidget(gui_group)
        
        # 添加弹性空间
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "常规")
    
    def _create_data_paths_tab(self):
        """创建数据路径设置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 数据路径设置组
        paths_group = QGroupBox("数据文件路径")
        paths_layout = QGridLayout(paths_group)
        
        # 机柜配置路径
        paths_layout.addWidget(QLabel("机柜配置:"), 0, 0)
        self.cabinet_profiles_edit = QLineEdit()
        paths_layout.addWidget(self.cabinet_profiles_edit, 0, 1)
        cabinet_browse_btn = QPushButton("浏览...")
        paths_layout.addWidget(cabinet_browse_btn, 0, 2)
        
        # 典型回路路径
        paths_layout.addWidget(QLabel("典型回路:"), 1, 0)
        self.wiring_typical_edit = QLineEdit()
        paths_layout.addWidget(self.wiring_typical_edit, 1, 1)
        wiring_browse_btn = QPushButton("浏览...")
        paths_layout.addWidget(wiring_browse_btn, 1, 2)
        
        # IODB路径
        paths_layout.addWidget(QLabel("IODB数据:"), 2, 0)
        self.iodb_edit = QLineEdit()
        paths_layout.addWidget(self.iodb_edit, 2, 1)
        iodb_browse_btn = QPushButton("浏览...")
        paths_layout.addWidget(iodb_browse_btn, 2, 2)
        
        # PIDB路径
        paths_layout.addWidget(QLabel("PIDB数据:"), 3, 0)
        self.pidb_edit = QLineEdit()
        paths_layout.addWidget(self.pidb_edit, 3, 1)
        pidb_browse_btn = QPushButton("浏览...")
        paths_layout.addWidget(pidb_browse_btn, 3, 2)
        
        layout.addWidget(paths_group)
        
        # 连接浏览按钮
        cabinet_browse_btn.clicked.connect(
            lambda: self._browse_directory(self.cabinet_profiles_edit, "选择机柜配置文件夹")
        )
        wiring_browse_btn.clicked.connect(
            lambda: self._browse_directory(self.wiring_typical_edit, "选择典型回路文件夹")
        )
        iodb_browse_btn.clicked.connect(
            lambda: self._browse_directory(self.iodb_edit, "选择IODB数据文件夹")
        )
        pidb_browse_btn.clicked.connect(
            lambda: self._browse_directory(self.pidb_edit, "选择PIDB数据文件夹")
        )
        
        # 添加弹性空间
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "数据路径")
    
    def _create_allocation_tab(self):
        """创建分配设置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 分配设置组
        allocation_group = QGroupBox("分配设置")
        allocation_layout = QGridLayout(allocation_group)
        
        # PartType匹配
        self.enable_parttype_matching_cb = QCheckBox("启用PartType匹配")
        allocation_layout.addWidget(self.enable_parttype_matching_cb, 0, 0, 1, 2)
        
        # 详细日志
        self.enable_detailed_logging_cb = QCheckBox("启用详细日志")
        allocation_layout.addWidget(self.enable_detailed_logging_cb, 1, 0, 1, 2)
        
        # 最大分配尝试次数
        allocation_layout.addWidget(QLabel("最大分配尝试次数:"), 2, 0)
        self.max_allocation_attempts_spin = QSpinBox()
        self.max_allocation_attempts_spin.setRange(100, 10000)
        allocation_layout.addWidget(self.max_allocation_attempts_spin, 2, 1)
        
        # 分配顺序
        allocation_layout.addWidget(QLabel("分配顺序:"), 3, 0)
        self.allocation_order_combo = QComboBox()
        self.allocation_order_combo.addItems([
            "cable_name_pair_asc",
            "cable_name_pair_desc",
            "system_grouped"
        ])
        allocation_layout.addWidget(self.allocation_order_combo, 3, 1)
        
        layout.addWidget(allocation_group)
        
        # 验证规则组
        validation_group = QGroupBox("验证规则")
        validation_layout = QVBoxLayout(validation_group)
        
        self.tag_uniqueness_cb = QCheckBox("Tag唯一性验证")
        validation_layout.addWidget(self.tag_uniqueness_cb)
        
        self.cable_pair_validation_cb = QCheckBox("Cable配对验证")
        validation_layout.addWidget(self.cable_pair_validation_cb)
        
        self.cable_attribute_consistency_cb = QCheckBox("Cable属性一致性验证")
        validation_layout.addWidget(self.cable_attribute_consistency_cb)
        
        layout.addWidget(validation_group)
        
        # 添加弹性空间
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "分配设置")

    def _create_spare_tab(self):
        """创建Spare点设置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Spare点基本设置组
        spare_basic_group = QGroupBox("Spare点基本设置")
        spare_basic_layout = QGridLayout(spare_basic_group)

        # 默认Spare下限
        spare_basic_layout.addWidget(QLabel("默认Spare下限:"), 0, 0)
        self.default_spare_limit_spin = QSpinBox()
        self.default_spare_limit_spin.setRange(0, 10)
        self.default_spare_limit_spin.setValue(2)
        spare_basic_layout.addWidget(self.default_spare_limit_spin, 0, 1)

        # 启用电缆Spare点
        self.enable_cable_spare_cb = QCheckBox("启用电缆Spare点")
        self.enable_cable_spare_cb.setChecked(True)
        spare_basic_layout.addWidget(self.enable_cable_spare_cb, 1, 0, 1, 2)

        # 启用ETP Spare点
        self.enable_etp_spare_cb = QCheckBox("启用ETP Spare点")
        self.enable_etp_spare_cb.setChecked(True)
        spare_basic_layout.addWidget(self.enable_etp_spare_cb, 2, 0, 1, 2)

        # Spare点命名前缀
        spare_basic_layout.addWidget(QLabel("Spare点命名前缀:"), 3, 0)
        self.spare_naming_prefix_edit = QLineEdit()
        self.spare_naming_prefix_edit.setText("SPARE_")
        spare_basic_layout.addWidget(self.spare_naming_prefix_edit, 3, 1)

        # Spare点描述
        spare_basic_layout.addWidget(QLabel("Spare点描述:"), 4, 0)
        self.spare_description_edit = QLineEdit()
        self.spare_description_edit.setText("-")
        spare_basic_layout.addWidget(self.spare_description_edit, 4, 1)

        layout.addWidget(spare_basic_group)

        # ETP Spare下限设置组
        etp_spare_group = QGroupBox("ETP Spare下限设置")
        etp_spare_layout = QVBoxLayout(etp_spare_group)

        # 说明文本
        info_label = QLabel("为不同ETP型号设置个性化的Spare下限。如果未设置，将使用默认Spare下限。")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666; font-size: 12px; margin: 5px;")
        etp_spare_layout.addWidget(info_label)

        # ETP配置表格
        self.etp_spare_table = QTableWidget()
        self.etp_spare_table.setColumnCount(2)
        self.etp_spare_table.setHorizontalHeaderLabels(["ETP型号", "Spare下限"])

        # 设置表格属性
        header = self.etp_spare_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.Fixed)
        self.etp_spare_table.setColumnWidth(1, 100)

        # 添加一些常见的ETP型号
        common_etp_types = ["CPM16-AI3700", "CPM16-AO3700", "CPM16-DI3700", "CPM16-DO3700"]
        self.etp_spare_table.setRowCount(len(common_etp_types))

        for i, etp_type in enumerate(common_etp_types):
            self.etp_spare_table.setItem(i, 0, QTableWidgetItem(etp_type))
            self.etp_spare_table.setItem(i, 1, QTableWidgetItem("2"))

        etp_spare_layout.addWidget(self.etp_spare_table)

        # 表格操作按钮
        table_button_layout = QHBoxLayout()

        self.add_etp_btn = QPushButton("添加ETP型号")
        self.remove_etp_btn = QPushButton("删除选中行")

        table_button_layout.addWidget(self.add_etp_btn)
        table_button_layout.addWidget(self.remove_etp_btn)
        table_button_layout.addStretch()

        etp_spare_layout.addLayout(table_button_layout)

        layout.addWidget(etp_spare_group)

        # 连接信号
        self.add_etp_btn.clicked.connect(self._add_etp_row)
        self.remove_etp_btn.clicked.connect(self._remove_etp_row)

        # 添加弹性空间
        layout.addStretch()

        self.tab_widget.addTab(tab, "Spare设置")

    def _add_etp_row(self):
        """添加ETP配置行"""
        row_count = self.etp_spare_table.rowCount()
        self.etp_spare_table.insertRow(row_count)
        self.etp_spare_table.setItem(row_count, 0, QTableWidgetItem(""))
        self.etp_spare_table.setItem(row_count, 1, QTableWidgetItem("2"))

    def _remove_etp_row(self):
        """删除选中的ETP配置行"""
        current_row = self.etp_spare_table.currentRow()
        if current_row >= 0:
            self.etp_spare_table.removeRow(current_row)

    def _create_logging_tab(self):
        """创建日志设置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 日志设置组
        logging_group = QGroupBox("日志设置")
        logging_layout = QGridLayout(logging_group)
        
        # 日志级别
        logging_layout.addWidget(QLabel("日志级别:"), 0, 0)
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        logging_layout.addWidget(self.log_level_combo, 0, 1)
        
        # 日志文件路径
        logging_layout.addWidget(QLabel("日志文件:"), 1, 0)
        self.log_file_edit = QLineEdit()
        logging_layout.addWidget(self.log_file_edit, 1, 1)
        log_browse_btn = QPushButton("浏览...")
        logging_layout.addWidget(log_browse_btn, 1, 2)
        
        # 最大文件大小
        logging_layout.addWidget(QLabel("最大文件大小:"), 2, 0)
        self.max_file_size_edit = QLineEdit()
        logging_layout.addWidget(self.max_file_size_edit, 2, 1)
        
        # 备份文件数量
        logging_layout.addWidget(QLabel("备份文件数量:"), 3, 0)
        self.backup_count_spin = QSpinBox()
        self.backup_count_spin.setRange(1, 20)
        logging_layout.addWidget(self.backup_count_spin, 3, 1)
        
        layout.addWidget(logging_group)
        
        # 连接浏览按钮
        log_browse_btn.clicked.connect(
            lambda: self._browse_file(self.log_file_edit, "选择日志文件", "日志文件 (*.log)")
        )
        
        # 添加弹性空间
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "日志")
    
    def _load_config_values(self):
        """加载配置值到界面"""
        # 常规设置
        window_config = self.config.get('application', {}).get('window', {})
        self.window_width_spin.setValue(window_config.get('width', 1400))
        self.window_height_spin.setValue(window_config.get('height', 900))
        
        gui_config = self.config.get('gui', {})
        self.show_progress_details_cb.setChecked(gui_config.get('show_progress_details', True))
        self.auto_save_results_cb.setChecked(gui_config.get('auto_save_results', True))
        
        # 数据路径
        data_paths = self.config.get('data_paths', {})
        self.cabinet_profiles_edit.setText(data_paths.get('cabinet_profiles', ''))
        self.wiring_typical_edit.setText(data_paths.get('wiring_typical', ''))
        self.iodb_edit.setText(data_paths.get('iodb', ''))
        self.pidb_edit.setText(data_paths.get('pidb', ''))
        
        # 分配设置
        allocation_config = self.config.get('allocation_settings', {})
        self.enable_parttype_matching_cb.setChecked(allocation_config.get('enable_parttype_matching', True))
        self.enable_detailed_logging_cb.setChecked(allocation_config.get('enable_detailed_logging', True))
        self.max_allocation_attempts_spin.setValue(allocation_config.get('max_allocation_attempts', 1000))
        
        order = allocation_config.get('allocation_order', 'cable_name_pair_asc')
        index = self.allocation_order_combo.findText(order)
        if index >= 0:
            self.allocation_order_combo.setCurrentIndex(index)
        
        # 验证规则
        validation_config = self.config.get('validation_rules', {})
        self.tag_uniqueness_cb.setChecked(validation_config.get('tag_uniqueness', True))
        self.cable_pair_validation_cb.setChecked(validation_config.get('cable_pair_validation', True))
        self.cable_attribute_consistency_cb.setChecked(validation_config.get('cable_attribute_consistency', True))

        # Spare设置
        spare_config = self.config.get('spare_settings', {})
        self.default_spare_limit_spin.setValue(spare_config.get('default_spare_limit', 2))
        self.enable_cable_spare_cb.setChecked(spare_config.get('enable_cable_spare', True))
        self.enable_etp_spare_cb.setChecked(spare_config.get('enable_etp_spare', True))
        self.spare_naming_prefix_edit.setText(spare_config.get('spare_naming_prefix', 'SPARE_'))
        self.spare_description_edit.setText(spare_config.get('spare_description', '-'))

        # 加载ETP Spare下限配置
        etp_spare_limits = spare_config.get('etp_spare_limits', {})
        self._load_etp_spare_limits(etp_spare_limits)

        # 日志设置
        logging_config = self.config.get('logging', {})
        level = logging_config.get('level', 'INFO')
        index = self.log_level_combo.findText(level)
        if index >= 0:
            self.log_level_combo.setCurrentIndex(index)
        
        self.log_file_edit.setText(logging_config.get('file_path', ''))
        self.max_file_size_edit.setText(logging_config.get('max_file_size', '10MB'))
        self.backup_count_spin.setValue(logging_config.get('backup_count', 5))
    
    def _connect_signals(self):
        """连接信号和槽"""
        self.reset_btn.clicked.connect(self._reset_config)
        self.apply_btn.clicked.connect(self._apply_config)
        self.save_btn.clicked.connect(self._save_config)
    
    def _browse_directory(self, line_edit: QLineEdit, title: str):
        """浏览目录"""
        directory = QFileDialog.getExistingDirectory(self, title, line_edit.text())
        if directory:
            line_edit.setText(directory)
    
    def _browse_file(self, line_edit: QLineEdit, title: str, filter_str: str):
        """浏览文件"""
        file_path, _ = QFileDialog.getSaveFileName(self, title, line_edit.text(), filter_str)
        if file_path:
            line_edit.setText(file_path)
    
    def _reset_config(self):
        """重置配置"""
        reply = QMessageBox.question(
            self, "重置配置", "确定要重置为默认配置吗？",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            # TODO: 重置为默认配置
            QMessageBox.information(self, "重置配置", "配置已重置为默认值")
    
    def _apply_config(self):
        """应用配置"""
        self._collect_config_values()
        self.config_changed.emit(self.config)
        QMessageBox.information(self, "应用配置", "配置已应用")
    
    def _save_config(self):
        """保存配置"""
        self._collect_config_values()
        self.config_changed.emit(self.config)
        # TODO: 保存配置到文件
        QMessageBox.information(self, "保存配置", "配置已保存")
    
    def _collect_config_values(self):
        """收集界面配置值"""
        # 更新配置字典
        # TODO: 实现配置值收集逻辑
        pass

    def _load_etp_spare_limits(self, etp_spare_limits: dict):
        """加载ETP Spare下限配置到表格"""
        # 清空现有行
        self.etp_spare_table.setRowCount(0)

        # 如果没有配置，使用默认的ETP类型
        if not etp_spare_limits:
            common_etp_types = ["CPM16-AI3700", "CPM16-AO3700", "CPM16-DI3700", "CPM16-DO3700"]
            for etp_type in common_etp_types:
                etp_spare_limits[etp_type] = 2

        # 添加配置行
        for etp_type, limit in etp_spare_limits.items():
            row = self.etp_spare_table.rowCount()
            self.etp_spare_table.insertRow(row)
            self.etp_spare_table.setItem(row, 0, QTableWidgetItem(etp_type))
            self.etp_spare_table.setItem(row, 1, QTableWidgetItem(str(limit)))

    def _collect_etp_spare_limits(self) -> dict:
        """从表格收集ETP Spare下限配置"""
        etp_spare_limits = {}

        for row in range(self.etp_spare_table.rowCount()):
            etp_type_item = self.etp_spare_table.item(row, 0)
            limit_item = self.etp_spare_table.item(row, 1)

            if etp_type_item and limit_item:
                etp_type = etp_type_item.text().strip()
                try:
                    limit = int(limit_item.text().strip())
                    if etp_type and limit >= 0:
                        etp_spare_limits[etp_type] = limit
                except ValueError:
                    continue

        return etp_spare_limits
