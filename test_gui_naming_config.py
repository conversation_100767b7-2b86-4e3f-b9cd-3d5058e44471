"""
GUI命名配置功能测试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_naming_config_dialog():
    """测试命名配置对话框"""
    print("测试命名配置对话框...")

    try:
        # 只测试导入和基本功能，不创建实际的GUI
        from gui.naming_config_dialog import NamingConfigDialog
        print("✓ 命名配置对话框导入成功")

        # 测试示例生成方法（不需要GUI）
        # 创建一个临时实例来测试方法
        class TestDialog:
            def _generate_example(self, device_type: str, prefix: str, separator: str, suffix: str) -> str:
                # 复制原始方法的逻辑
                if device_type in ['barrier', 'relay', 'isolator']:
                    parts = []
                    if prefix:
                        parts.append(prefix)
                    parts.extend(['1', '3', '05'])

                    if separator:
                        example = separator.join(parts)
                    else:
                        example = ''.join(parts)

                    if suffix:
                        example += suffix

                    return example
                return ""

        test_dialog = TestDialog()
        example = test_dialog._generate_example('barrier', 'BA', '', '')
        print(f"✓ 示例生成成功: {example}")

        # 测试器件类型列表
        expected_device_types = [
            ('barrier', '安全栅'),
            ('relay', '继电器'),
            ('isolator', '隔离器'),
            ('surge_protector', '防雷栅'),
            ('terminal_block', '端子排'),
            ('tr_terminal', 'TR端子排')
        ]

        print(f"✓ 支持 {len(expected_device_types)} 种器件类型")

        return True

    except Exception as e:
        print(f"✗ 命名配置对话框测试失败: {e}")
        return False

def test_allocation_widget_integration():
    """测试分配界面集成"""
    print("\n测试分配界面集成...")
    
    try:
        # 测试导入
        from gui.allocation_widget import AllocationWidget
        print("✓ AllocationWidget导入成功")
        
        # 检查是否有命名配置相关的方法
        widget_methods = dir(AllocationWidget)
        
        expected_methods = [
            '_configure_naming_rules',
            '_load_naming_config', 
            '_save_naming_config'
        ]
        
        for method in expected_methods:
            if method in widget_methods:
                print(f"✓ 方法 {method} 存在")
            else:
                print(f"✗ 方法 {method} 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 分配界面集成测试失败: {e}")
        return False

def test_naming_engine_integration():
    """测试命名引擎集成"""
    print("\n测试命名引擎集成...")
    
    try:
        from core.naming_engine import NamingEngine, NamingContext
        from core.data_models import IOPoint, SignalType
        
        # 创建命名引擎
        engine = NamingEngine()
        print("✓ 命名引擎创建成功")
        
        # 创建测试IO点
        io_point = IOPoint(
            tag="AI_TEST_001",
            signal_type=SignalType.AI,
            description="测试AI点",
            cable_name="CABLE_001",
            pair_number="01",
            is_intrinsic=False,
            system="SYS",
            location="FIELD",
            cable_type="CABLE",
            wiring_typical="TYPICAL_001"
        )
        io_point.allocated_rack = "Rack_1"
        io_point.allocated_slot = 3
        io_point.allocated_channel = 5
        
        # 创建命名上下文
        context = NamingContext(io_point, {})
        print("✓ 命名上下文创建成功")
        
        # 测试各种器件命名
        device_types = ['barrier', 'relay', 'isolator', 'surge_protector', 'terminal_block', 'tr_terminal']
        
        for device_type in device_types:
            try:
                name = engine.generate_name(device_type, context)
                print(f"✓ {device_type}命名: {name}")
            except Exception as e:
                print(f"✗ {device_type}命名失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 命名引擎集成测试失败: {e}")
        return False

def test_config_persistence():
    """测试配置持久化"""
    print("\n测试配置持久化...")
    
    try:
        import tempfile
        import json
        
        # 创建测试配置
        test_config = {
            'barrier': {
                'prefix': 'TEST_BA',
                'separator': '_',
                'suffix': '_END'
            },
            'relay': {
                'prefix': 'TEST_RY',
                'separator': '-',
                'suffix': ''
            }
        }
        
        # 测试保存配置
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_config, f, ensure_ascii=False, indent=2)
            temp_file = f.name
        
        print("✓ 配置保存成功")
        
        # 测试加载配置
        with open(temp_file, 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        if loaded_config == test_config:
            print("✓ 配置加载成功")
        else:
            print("✗ 配置加载失败 - 数据不匹配")
            return False
        
        # 清理临时文件
        os.unlink(temp_file)
        
        return True
        
    except Exception as e:
        print(f"✗ 配置持久化测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始GUI命名配置功能测试...\n")
    
    tests = [
        test_naming_config_dialog,
        test_allocation_widget_integration,
        test_naming_engine_integration,
        test_config_persistence
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有GUI命名配置功能测试通过！")
        return True
    else:
        print("✗ 部分测试失败")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
