"""
XML处理工具
扩展现有的XML操作功能
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import xml.etree.ElementTree as ET

from core.logger import get_logger


class XMLProcessor:
    """XML处理器"""
    
    def __init__(self):
        """初始化XML处理器"""
        self.logger = get_logger(__name__)
    
    def load_xml_file(self, file_path: str) -> Optional[ET.ElementTree]:
        """
        加载XML文件
        
        Args:
            file_path: XML文件路径
            
        Returns:
            XML树对象或None
        """
        try:
            self.logger.info(f"加载XML文件: {file_path}")
            tree = ET.parse(file_path)
            return tree
        except Exception as e:
            self.logger.error(f"加载XML文件失败: {e}")
            return None
    
    def load_cabinet_profile(self, file_path: str) -> Dict[str, Any]:
        """
        加载机柜配置文件

        Args:
            file_path: 机柜配置文件路径

        Returns:
            机柜配置数据
        """
        tree = self.load_xml_file(file_path)
        if tree is None:
            return {}

        try:
            root = tree.getroot()
            cabinet_data = {
                'name': Path(file_path).stem,
                'rails': [],
                'racks': [],
                'properties': {},
                'components': []
            }

            # 解析根元素属性
            cabinet_data['properties'].update(root.attrib)

            # 查找所有ProfileComponent元素
            for component_elem in root.findall('.//ProfileComponent'):
                component_data = self.parse_profile_component(component_elem)
                cabinet_data['components'].append(component_data)

                # 根据组件类型分类
                component_type = component_data.get('type', '').lower()
                if 'rail' in component_type:
                    rail_data = self._extract_rail_data(component_data)
                    if rail_data:
                        cabinet_data['rails'].append(rail_data)
                elif 'rack' in component_type or 'chassis' in component_type:
                    rack_data = self._extract_rack_data(component_data)
                    if rack_data:
                        cabinet_data['racks'].append(rack_data)

            self.logger.info(f"机柜配置解析完成: {len(cabinet_data['rails'])}个导轨, {len(cabinet_data['racks'])}个机架")
            return cabinet_data

        except Exception as e:
            self.logger.error(f"解析机柜配置文件失败: {e}")
            return {}
    
    def load_wiring_typical(self, file_path: str) -> Dict[str, Any]:
        """
        加载典型回路文件

        Args:
            file_path: 典型回路文件路径

        Returns:
            典型回路数据
        """
        tree = self.load_xml_file(file_path)
        if tree is None:
            return {}

        try:
            root = tree.getroot()
            typical_data = {
                'name': Path(file_path).stem,
                'signal_type': 'AI',  # 从文件名推断
                'is_intrinsic': 'IS' in Path(file_path).stem.upper(),
                'components': [],
                'connections': [],
                'properties': {}
            }

            # 解析根元素属性
            typical_data['properties'].update(root.attrib)

            # 从文件名解析信号类型
            filename = Path(file_path).stem.upper()
            if 'AI' in filename:
                typical_data['signal_type'] = 'AI'
            elif 'AO' in filename:
                typical_data['signal_type'] = 'AO'
            elif 'DI' in filename:
                typical_data['signal_type'] = 'DI'
            elif 'DO' in filename:
                typical_data['signal_type'] = 'DO'

            # 查找所有组件
            for component_elem in root.findall('.//ProfileComponent'):
                component_data = self.parse_profile_component(component_elem)
                typical_data['components'].append(component_data)

            # 查找连接关系
            for connection_elem in root.findall('.//Connection'):
                connection_data = self._extract_connection_data(connection_elem)
                if connection_data:
                    typical_data['connections'].append(connection_data)

            self.logger.info(f"典型回路解析完成: {len(typical_data['components'])}个组件, {len(typical_data['connections'])}个连接")
            return typical_data

        except Exception as e:
            self.logger.error(f"解析典型回路文件失败: {e}")
            return {}
    
    def parse_profile_component(self, element: ET.Element) -> Dict[str, Any]:
        """
        解析ProfileComponent元素
        
        Args:
            element: ProfileComponent XML元素
            
        Returns:
            组件数据字典
        """
        component_data = {
            'name': element.get('Name', ''),
            'type': element.get('Type', ''),
            'count': element.get('Count', '1'),
            'mapping': element.get('Mapping', ''),
            'properties': {},
            'signatures': {},
            'components': []
        }
        
        # 解析ProfileProperties
        properties_elem = element.find('ProfileProperties')
        if properties_elem is not None:
            for prop in properties_elem.findall('ProfileProperty'):
                prop_name = prop.get('Name', '')
                prop_value = prop.get('Value', '')
                component_data['properties'][prop_name] = prop_value
        
        # 解析ProfileSignatures
        signatures_elem = element.find('ProfileSignatures')
        if signatures_elem is not None:
            for sig in signatures_elem.findall('ProfileSignature'):
                sig_name = sig.get('Name', '')
                sig_value = sig.get('Value', '')
                component_data['signatures'][sig_name] = sig_value
        
        # 递归解析子组件
        components_elem = element.find('Components')
        if components_elem is not None:
            for child_comp in components_elem.findall('ProfileComponent'):
                child_data = self.parse_profile_component(child_comp)
                component_data['components'].append(child_data)
        
        return component_data
    
    def save_xml_file(self, tree: ET.ElementTree, file_path: str) -> bool:
        """
        保存XML文件
        
        Args:
            tree: XML树对象
            file_path: 输出文件路径
            
        Returns:
            是否保存成功
        """
        try:
            self.logger.info(f"保存XML文件: {file_path}")
            
            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            tree.write(file_path, encoding='utf-8', xml_declaration=True)
            return True
        except Exception as e:
            self.logger.error(f"保存XML文件失败: {e}")
            return False

    def _extract_rail_data(self, component_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        从组件数据中提取导轨信息

        Args:
            component_data: 组件数据

        Returns:
            导轨数据字典或None
        """
        try:
            properties = component_data.get('properties', {})

            rail_data = {
                'name': component_data.get('name', ''),
                'type': component_data.get('type', ''),
                'position': properties.get('Position', ''),
                'length': int(properties.get('Length', '0') or '0'),
                'io_type': properties.get('IOType', 'Mixed'),
                'intrinsic': properties.get('Intrinsic', 'Mixed'),
                'part_types': [],
                'part_numbers': [],
                'reserved_from': int(properties.get('ReservedFrom', '0') or '0'),
                'reserved_to': int(properties.get('ReservedTo', '0') or '0')
            }

            # 提取支持的部件类型和编号
            for key, value in properties.items():
                if key.startswith('PartType') and value:
                    rail_data['part_types'].append(value)
                elif key.startswith('PartNum') and value:
                    rail_data['part_numbers'].append(value)

            return rail_data

        except Exception as e:
            self.logger.warning(f"提取导轨数据失败: {e}")
            return None

    def _extract_rack_data(self, component_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        从组件数据中提取机架信息

        Args:
            component_data: 组件数据

        Returns:
            机架数据字典或None
        """
        try:
            properties = component_data.get('properties', {})
            signatures = component_data.get('signatures', {})

            rack_data = {
                'name': component_data.get('name', ''),
                'type': component_data.get('type', ''),
                'rack_type': properties.get('RackType', 'Chassis'),
                'position': properties.get('Position', ''),
                'part_type': properties.get('PartType', ''),
                'part_number': properties.get('PartNumber', ''),
                'available_slots': [],
                'connectors': {}
            }

            # 提取可用槽位信息
            slots_applicable = properties.get('SlotsApplicable', '')
            if slots_applicable:
                # 解析槽位字符串，如 "1L,2L,3L,4L,5L,6L,7L,8L"
                slots = [slot.strip() for slot in slots_applicable.split(',')]
                rack_data['available_slots'] = slots

            # 提取连接器信息
            for key, value in signatures.items():
                if 'ConnectedChannels' in key and value:
                    connector_name = key.replace('_ConnectedChannels', '')
                    channels = [ch.strip() for ch in value.split(',')]
                    rack_data['connectors'][connector_name] = channels

            return rack_data

        except Exception as e:
            self.logger.warning(f"提取机架数据失败: {e}")
            return None

    def _extract_connection_data(self, connection_elem: ET.Element) -> Optional[Dict[str, Any]]:
        """
        从连接元素中提取连接数据

        Args:
            connection_elem: 连接XML元素

        Returns:
            连接数据字典或None
        """
        try:
            connection_data = {
                'from_component': connection_elem.get('FromComponent', ''),
                'to_component': connection_elem.get('ToComponent', ''),
                'from_terminal': connection_elem.get('FromTerminal', ''),
                'to_terminal': connection_elem.get('ToTerminal', ''),
                'signal_type': connection_elem.get('SignalType', ''),
                'properties': {}
            }

            # 提取其他属性
            for key, value in connection_elem.attrib.items():
                if key not in ['FromComponent', 'ToComponent', 'FromTerminal', 'ToTerminal', 'SignalType']:
                    connection_data['properties'][key] = value

            return connection_data

        except Exception as e:
            self.logger.warning(f"提取连接数据失败: {e}")
            return None
