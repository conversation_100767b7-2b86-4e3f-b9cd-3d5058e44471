"""
项目管理器
负责项目的创建、切换和管理
"""

import json
import os
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from core.logger import get_logger


@dataclass
class ProjectInfo:
    """项目信息数据类"""
    name: str
    path: str
    created_date: str
    last_modified: str
    description: str = ""
    version: str = "1.0.0"


class ProjectManager:
    """项目管理器类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化项目管理器

        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = get_logger(__name__)

        # 项目配置
        self.current_project_path = None
        self.current_project_info = None

        # 标准项目文件夹结构
        self.standard_folders = [
            "01B_Cabinet Templates",
            "01C_Wiring Typical",
            "04A_IODB",
            "04B_PIDB",
            "output",
            "logs",
            "temp"
        ]

    def create_new_project(self, project_name: str, parent_path: str,
                          description: str = "") -> bool:
        """
        创建新项目

        Args:
            project_name: 项目名称
            parent_path: 父目录路径
            description: 项目描述

        Returns:
            创建是否成功
        """
        self.logger.info(f"创建新项目: {project_name} 在 {parent_path}")

        try:
            # 创建项目目录
            project_path = Path(parent_path) / project_name

            if project_path.exists():
                self.logger.error(f"项目目录已存在: {project_path}")
                return False

            project_path.mkdir(parents=True, exist_ok=True)

            # 创建标准文件夹结构
            for folder in self.standard_folders:
                folder_path = project_path / folder
                folder_path.mkdir(exist_ok=True)
                self.logger.debug(f"创建文件夹: {folder}")

            # 创建项目配置文件
            project_config = {
                "project_info": {
                    "name": project_name,
                    "description": description,
                    "version": "1.0.0",
                    "created_date": self._get_current_datetime(),
                    "last_modified": self._get_current_datetime()
                },
                "data_paths": {
                    "cabinet_profiles": "01B_Cabinet Templates",
                    "wiring_typical": "01C_Wiring Typical",
                    "iodb": "04A_IODB",
                    "pidb": "04B_PIDB"
                },
                "output_paths": {
                    "reports": "output/reports",
                    "exports": "output/exports",
                    "logs": "logs"
                }
            }

            config_file = project_path / "project_config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(project_config, f, indent=4, ensure_ascii=False)

            self.logger.info(f"项目创建成功: {project_path}")
            return True

        except Exception as e:
            self.logger.error(f"创建项目失败: {e}")
            return False

    def open_project(self, project_path: str) -> bool:
        """
        打开现有项目

        Args:
            project_path: 项目路径

        Returns:
            打开是否成功
        """
        self.logger.info(f"打开项目: {project_path}")

        try:
            project_path = Path(project_path)

            if not project_path.exists():
                self.logger.error(f"项目路径不存在: {project_path}")
                return False

            # 检查项目配置文件
            config_file = project_path / "project_config.json"
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    project_config = json.load(f)

                # 更新项目信息
                project_info_data = project_config.get('project_info', {})
                self.current_project_info = ProjectInfo(
                    name=project_info_data.get('name', project_path.name),
                    path=str(project_path),
                    created_date=project_info_data.get('created_date', ''),
                    last_modified=project_info_data.get('last_modified', ''),
                    description=project_info_data.get('description', ''),
                    version=project_info_data.get('version', '1.0.0')
                )
            else:
                # 如果没有配置文件，创建基本项目信息
                self.current_project_info = ProjectInfo(
                    name=project_path.name,
                    path=str(project_path),
                    created_date=self._get_current_datetime(),
                    last_modified=self._get_current_datetime(),
                    description="现有项目"
                )

            self.current_project_path = str(project_path)

            # 更新全局配置的数据路径
            self._update_config_paths(project_path)

            self.logger.info(f"项目打开成功: {self.current_project_info.name}")
            return True

        except Exception as e:
            self.logger.error(f"打开项目失败: {e}")
            return False

    def get_current_project_info(self) -> Optional[ProjectInfo]:
        """
        获取当前项目信息

        Returns:
            当前项目信息，如果没有打开项目则返回None
        """
        return self.current_project_info

    def is_project_open(self) -> bool:
        """
        检查是否有项目打开

        Returns:
            是否有项目打开
        """
        return self.current_project_path is not None

    def close_project(self):
        """关闭当前项目"""
        if self.current_project_path:
            self.logger.info(f"关闭项目: {self.current_project_info.name}")
            self.current_project_path = None
            self.current_project_info = None

    def get_project_data_paths(self) -> Dict[str, str]:
        """
        获取当前项目的数据路径

        Returns:
            数据路径字典
        """
        if not self.current_project_path:
            return {}

        project_path = Path(self.current_project_path)

        return {
            'cabinet_profiles': str(project_path / "01B_Cabinet Templates"),
            'wiring_typical': str(project_path / "01C_Wiring Typical"),
            'iodb': str(project_path / "04A_IODB"),
            'pidb': str(project_path / "04B_PIDB"),
            'output': str(project_path / "output"),
            'logs': str(project_path / "logs"),
            'temp': str(project_path / "temp")
        }

    def validate_project_structure(self, project_path: str) -> Dict[str, bool]:
        """
        验证项目结构

        Args:
            project_path: 项目路径

        Returns:
            验证结果字典
        """
        project_path = Path(project_path)
        validation_result = {}

        for folder in self.standard_folders:
            folder_path = project_path / folder
            validation_result[folder] = folder_path.exists()

        return validation_result

    def _update_config_paths(self, project_path: Path):
        """
        更新配置中的数据路径

        Args:
            project_path: 项目路径
        """
        # 更新配置中的数据路径为绝对路径
        self.config['data_paths'] = {
            'cabinet_profiles': str(project_path / "01B_Cabinet Templates"),
            'wiring_typical': str(project_path / "01C_Wiring Typical"),
            'iodb': str(project_path / "04A_IODB"),
            'pidb': str(project_path / "04B_PIDB")
        }

    def _get_current_datetime(self) -> str:
        """
        获取当前日期时间字符串

        Returns:
            格式化的日期时间字符串
        """
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
