"""
测试电缆配对验证逻辑问题
"""

from pathlib import Path
from core.data_loader_simple import DataLoader
from core.validator import IODBValidator
from utils.config_manager_simple import ConfigManager


def test_cable_validation_issue():
    """测试电缆配对验证逻辑问题"""
    print("=== 电缆配对验证逻辑问题分析 ===")
    
    # 加载配置
    config_manager = ConfigManager()
    config = config_manager.load_config()
    
    # 创建数据加载器和验证器
    data_loader = DataLoader(config)
    validator = IODBValidator(config)
    
    print("\n📊 加载IODB数据...")
    iodb_file = Path("04A_IODB/IODB_nospare.xlsx")
    if not iodb_file.exists():
        print(f"❌ IODB文件不存在: {iodb_file}")
        return
    
    # 加载IODB数据
    iodb_data = data_loader.load_iodb_data(str(iodb_file))
    io_points = iodb_data.get('io_points', [])
    cables = iodb_data.get('cables', {})
    
    print(f"✓ 加载完成: {len(io_points)} 个I/O点, {len(cables)} 条电缆")
    
    print("\n🔍 分析电缆配对数据...")
    print("前5条电缆的详细信息:")
    
    cable_items = list(cables.items())[:5]
    for cable_name, cable_data in cable_items:
        io_points_count = len(cable_data.get('io_points', []))
        pair_size = cable_data.get('pair_size', 1)
        
        print(f"\n电缆: {cable_name}")
        print(f"  - I/O点数量: {io_points_count}")
        print(f"  - 电缆对数(pair_size): {pair_size}")
        print(f"  - 当前错误逻辑期望: {pair_size * 2} 个I/O点")
        print(f"  - 正确逻辑应该期望: {pair_size} 个I/O点")
        print(f"  - 匹配状态: {'✓ 正确' if io_points_count == pair_size else '❌ 不匹配'}")
        
        # 显示该电缆的I/O点详情
        cable_io_points = cable_data.get('io_points', [])
        if cable_io_points:
            print(f"  - I/O点列表:")
            for i, point in enumerate(cable_io_points[:3]):  # 只显示前3个
                print(f"    {i+1}. {point.tag} (pair: {point.pair_number})")
            if len(cable_io_points) > 3:
                print(f"    ... 还有 {len(cable_io_points) - 3} 个I/O点")
    
    print("\n⚠️ 运行当前的验证逻辑...")
    validation_result = validator.validate_iodb_data(iodb_data)
    
    print(f"\n📋 验证结果:")
    print(f"  - 验证成功: {'是' if validation_result.success else '否'}")
    print(f"  - 错误数量: {len(validation_result.errors)}")
    print(f"  - 警告数量: {len(validation_result.warnings)}")
    
    if validation_result.warnings:
        print(f"\n⚠️ 警告详情 (显示前5个):")
        for i, warning in enumerate(validation_result.warnings[:5]):
            print(f"  {i+1}. {warning}")
        if len(validation_result.warnings) > 5:
            print(f"  ... 还有 {len(validation_result.warnings) - 5} 个警告")
    
    print("\n🔧 问题分析:")
    print("当前验证逻辑的错误假设:")
    print("  - 假设: 每对电缆需要2个I/O点")
    print("  - 计算: expected_points = pair_size * 2")
    print("  - 结果: 所有电缆都产生警告")
    
    print("\n正确的逻辑应该是:")
    print("  - 事实: 一个I/O点对应一对电缆(2根线)")
    print("  - 计算: expected_points = pair_size")
    print("  - 结果: I/O点数量应该等于电缆对数")
    
    print("\n📈 统计分析:")
    correct_matches = 0
    total_cables = len(cables)
    
    for cable_name, cable_data in cables.items():
        io_points_count = len(cable_data.get('io_points', []))
        pair_size = cable_data.get('pair_size', 1)
        
        if io_points_count == pair_size:
            correct_matches += 1
    
    print(f"  - 总电缆数: {total_cables}")
    print(f"  - 正确匹配数 (I/O点数 = 对数): {correct_matches}")
    print(f"  - 正确匹配率: {correct_matches/total_cables*100:.1f}%")
    print(f"  - 错误警告数: {total_cables - correct_matches}")
    
    print("\n=== 分析完成 ===")


if __name__ == "__main__":
    test_cable_validation_issue()
