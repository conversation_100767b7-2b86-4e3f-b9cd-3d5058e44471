# editor_utils.py
import os
import xml.etree.ElementTree as ET
from PySide6.QtWidgets import QTreeWidgetItem, QTableWidgetItem, QMessageBox
from PySide6.QtCore import Qt

import xml_operations # 导入项目内的 XML 处理模块

def populate_profile_component_recursive(profile_component_element, parent_tree_item, enable_checkbox_for_children=False):
    """
    递归地填充 ProfileComponent 元素及其子组件到树视图中。
    参数:
        profile_component_element (ET.Element): 要处理的 ProfileComponent XML 元素。
        parent_tree_item (QTreeWidgetItem): 父树项目。
        enable_checkbox_for_children (bool): 是否为子组件项也启用勾选框。
                                            (在此版本中，此参数通常为 False，因为勾选框只在顶级)
    """
    item_text = profile_component_element.get('Name', profile_component_element.tag)
    tree_item = QTreeWidgetItem(parent_tree_item, [item_text])
    tree_item.setData(0, Qt.UserRole, profile_component_element)

    # 根据参数决定是否为当前项启用勾选框
    # 注意：调用此函数时，如果 parent_tree_item 是顶级文件项，则 enable_checkbox_for_children 应为 False
    # 顶级文件项的勾选框在创建它的时候单独设置
    if enable_checkbox_for_children: # 此标志现在控制的是 *当前递归层级* 的项
        tree_item.setFlags(tree_item.flags() | Qt.ItemIsUserCheckable)
        tree_item.setCheckState(0, Qt.Unchecked)
    # else: 子组件项默认不可勾选

    components_node = profile_component_element.find("Components")
    if components_node is not None:
        for child_pc_element in components_node.findall("ProfileComponent"):
            # 递归调用时，子组件的子组件（更深层级）也不应该有勾选框
            populate_profile_component_recursive(child_pc_element, tree_item, enable_checkbox_for_children=False)

def update_details_pane_content(tree_item, attributes_table, properties_table, signatures_table, btn_add_attribute):
    """
    根据选中的树项（ProfileComponent），更新右侧的属性、Properties、Signatures 表格。
    如果 tree_item 为 None，则清空表格。
    """
    for table in [attributes_table, properties_table, signatures_table]:
        table.blockSignals(True)
        table.setRowCount(0)
        table.blockSignals(False)

    btn_add_attribute.setEnabled(False)

    if not tree_item: return

    element = tree_item.data(0, Qt.UserRole)
    if element is None or element.tag != "ProfileComponent":
        return

    btn_add_attribute.setEnabled(True)

    attributes_table.blockSignals(True)
    pc_attrs_ordered = ["Name", "Type", "Count", "Mapping", "Category"]
    present_attrs = {}
    for attr_name in pc_attrs_ordered:
        if attr_name in element.attrib:
            row = attributes_table.rowCount()
            attributes_table.insertRow(row)
            name_item = QTableWidgetItem(attr_name)
            name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)
            attributes_table.setItem(row, 0, name_item)
            attributes_table.setItem(row, 1, QTableWidgetItem(element.attrib[attr_name]))
            present_attrs[attr_name] = True
    for attr_name, attr_value in element.attrib.items():
        if attr_name not in present_attrs:
            row = attributes_table.rowCount()
            attributes_table.insertRow(row)
            name_item = QTableWidgetItem(attr_name)
            name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)
            attributes_table.setItem(row, 0, name_item)
            attributes_table.setItem(row, 1, QTableWidgetItem(attr_value))
    attributes_table.resizeColumnsToContents()
    attributes_table.blockSignals(False)

    properties_table.blockSignals(True)
    profile_props_element = element.find("ProfileProperties")
    if profile_props_element is not None:
        for prop_element in profile_props_element.findall("ProfileProperty"):
            row = properties_table.rowCount()
            properties_table.insertRow(row)
            name_item = QTableWidgetItem(prop_element.get("Name", ""))
            name_item.setData(Qt.UserRole, prop_element)
            properties_table.setItem(row, 0, name_item)
            properties_table.setItem(row, 1, QTableWidgetItem(prop_element.get("PropertyType", "")))
            properties_table.setItem(row, 2, QTableWidgetItem(prop_element.get("Value", "")))
            properties_table.setItem(row, 3, QTableWidgetItem(prop_element.get("Units", "")))
            properties_table.setItem(row, 4, QTableWidgetItem(prop_element.get("Description", "")))
    properties_table.resizeColumnsToContents()
    properties_table.blockSignals(False)

    signatures_table.blockSignals(True)
    profile_sigs_element = element.find("ProfileSignatures")
    if profile_sigs_element is not None:
        for sig_element in profile_sigs_element.findall("ProfileSignature"):
            row = signatures_table.rowCount()
            signatures_table.insertRow(row)
            name_item = QTableWidgetItem(sig_element.get("Name", ""))
            name_item.setData(Qt.UserRole, sig_element)
            signatures_table.setItem(row, 0, name_item)
            signatures_table.setItem(row, 1, QTableWidgetItem(sig_element.get("PropertyType", "")))
            signatures_table.setItem(row, 2, QTableWidgetItem(sig_element.get("Value", "")))
            signatures_table.setItem(row, 3, QTableWidgetItem(sig_element.get("Units", "")))
            signatures_table.setItem(row, 4, QTableWidgetItem(sig_element.get("Description", "")))
    signatures_table.resizeColumnsToContents()
    signatures_table.blockSignals(False)

def handle_attribute_table_change(main_window, row, column):
    """处理组件属性表格的单元格内容改变。"""
    if column != 1: return # 只处理“值”列的改变

    selected_tree_item = main_window.active_tree_widget.currentItem()
    if not selected_tree_item: return
    pc_element = selected_tree_item.data(0, Qt.UserRole) # 获取关联的XML元素
    if not pc_element or pc_element.tag != "ProfileComponent": return

    attr_name_item = main_window.attributes_table.item(row, 0) # 属性名单元格
    attr_value_item = main_window.attributes_table.item(row, 1) # 属性值单元格
    if not attr_name_item or not attr_value_item: return

    attr_name = attr_name_item.text()
    new_value = attr_value_item.text()

    # 如果值为空，且不是必须的属性，则询问是否删除该属性
    if not new_value and attr_name not in ["Name", "Type", "Mapping", "Category", "Count"]: # 这些是核心属性，不应轻易删除
        reply = QMessageBox.question(main_window, "删除属性?",
                                     f"属性 '{attr_name}' 的值为空。是否要删除此属性?",
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            if attr_name in pc_element.attrib:
                del pc_element.attrib[attr_name] # 从XML元素中删除属性
            main_window.attributes_table.blockSignals(True)
            main_window.attributes_table.removeRow(row) # 从表格中移除该行
            main_window.attributes_table.blockSignals(False)
            main_window.statusBar().showMessage(f"组件属性 '{attr_name}' 已删除。", 2000)
            return # 操作完成，返回

    # 更新XML元素的属性值
    pc_element.set(attr_name, new_value)
    main_window.statusBar().showMessage(f"组件属性 '{attr_name}' 已更新。", 2000)

    # 如果修改的是 "Name" 属性，则同步更新树视图中对应项的显示文本
    if attr_name == "Name":
        selected_tree_item.setText(0, new_value)
        if selected_tree_item.parent() is None: # 如果是顶级文件项
             current_category = main_window.main_category_tabs.tabText(main_window.main_category_tabs.currentIndex())
             main_window.setWindowTitle(f"XML Profile 编辑器 - {current_category} [{new_value}]")

def handle_sub_property_table_change(main_window, table_widget, row, column, element_type_name):
    """通用处理 ProfileProperty/ProfileSignature 表格单元格内容改变。"""
    editable_columns_map = {"Name": 0, "PropertyType": 1, "Value": 2, "Units": 3, "Description": 4}

    target_attribute_name = None # 存储被修改的属性名 (例如 "Name", "Value" 等)
    for attr_name, col_idx in editable_columns_map.items():
        if col_idx == column:
            target_attribute_name = attr_name
            break

    if target_attribute_name is None: return # 如果修改的列不在可编辑映射中，则忽略

    name_item = table_widget.item(row, 0) # "Name" 属性的 QTableWidgetItem，用于获取关联的XML元素
    changed_value_item = table_widget.item(row, column) # 被修改的单元格
    if not name_item or not changed_value_item: return

    sub_element = name_item.data(Qt.UserRole) # 从 "Name" 单元格获取关联的 XML Element
    if not sub_element: return # 如果没有关联的XML元素，则忽略

    new_value = changed_value_item.text()
    element_name_attr_original = sub_element.get("Name") # 获取修改前的 "Name" 属性值，用于消息提示

    # 如果 "Name" 属性被改为空
    if target_attribute_name == "Name" and not new_value:
         reply = QMessageBox.question(main_window, f"删除 {element_type_name}?",
                                     f"'{element_type_name}' 的名称不能为空。是否要删除此项?",
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
         if reply == QMessageBox.Yes:
             selected_tree_item = main_window.active_tree_widget.currentItem() # 获取当前选中的 ProfileComponent 项
             if selected_tree_item:
                 pc_element = selected_tree_item.data(0, Qt.UserRole) # 获取父 ProfileComponent 的 XML 元素
                 # 确定父容器是 "ProfileProperties" 还是 "ProfileSignatures"
                 parent_container_name = "ProfileProperties" if element_type_name == "ProfileProperty" else "ProfileSignatures"
                 parent_container = pc_element.find(parent_container_name) # 找到父容器元素
                 if parent_container is not None:
                     try:
                         parent_container.remove(sub_element) # 从 XML 中删除该子元素
                         table_widget.blockSignals(True)
                         table_widget.removeRow(row) # 从表格中删除该行
                         table_widget.blockSignals(False)
                         main_window.statusBar().showMessage(f"{element_type_name} '{element_name_attr_original}' 已删除。", 2000)
                         return # 操作完成
                     except Exception as e:
                         QMessageBox.critical(main_window, f"删除 {element_type_name} 错误", f"删除时出错: {e}")
             # 如果删除失败或用户取消，恢复原值
             changed_value_item.setText(element_name_attr_original)
             main_window.statusBar().showMessage(f"{element_type_name} 名称不能设置为空。", 2000)
             return

    # 更新 XML 元素的属性
    sub_element.set(target_attribute_name, new_value)

    # 如果修改的是 "Name" 属性，同时更新表格中 "Name" 单元格的显示文本
    if target_attribute_name == "Name":
        name_item.setText(new_value) # 更新表格中 "Name" 单元格的显示

    main_window.statusBar().showMessage(f"{element_type_name} '{element_name_attr_original}' 的 {target_attribute_name} 已更新为 '{new_value}'。", 2000)