#!/usr/bin/env python3
"""
测试GUI启动
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_gui():
    """测试GUI启动"""
    try:
        print("测试GUI启动...")
        
        # 检查PySide6
        try:
            from PySide6.QtWidgets import QApplication
            print("✓ PySide6可用")
        except ImportError:
            print("✗ PySide6不可用，请安装: pip install PySide6")
            return False
        
        # 创建QApplication
        app = QApplication(sys.argv)
        print("✓ QApplication创建成功")
        
        # 加载配置
        from utils.config_manager import ConfigManager
        config_manager = ConfigManager()
        config = config_manager.load_config()
        print("✓ 配置加载成功")
        
        # 设置日志
        from core.logger import setup_logger
        setup_logger(config.get('logging', {}))
        print("✓ 日志系统设置成功")
        
        # 创建主窗口
        from gui.main_window import MainWindow
        main_window = MainWindow(config)
        print("✓ 主窗口创建成功")
        
        # 显示窗口
        main_window.show()
        print("✓ 主窗口显示成功")
        
        print("\nGUI测试成功！窗口应该已经显示。")
        print("关闭窗口以退出测试。")
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"✗ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("EWReborn GUI测试")
    print("=" * 50)
    
    exit_code = test_gui()
    print(f"\n程序退出，退出码: {exit_code}")
