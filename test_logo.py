#!/usr/bin/env python3
"""
Logo展示测试
展示新设计的EWReborn Logo
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

from gui.logo import EWRebornLogo, EWRebornMiniLogo
from gui.styles import get_apple_style, APPLE_COLORS


class LogoShowcase(QWidget):
    """Logo展示窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("EWReborn Logo 设计展示")
        self.setFixedSize(600, 400)
        self._setup_ui()
        self._apply_styles()
    
    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(30)
        layout.setContentsMargins(40, 40, 40, 40)
        
        # 标题
        title = QLabel("EWReborn Logo 设计")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 24, QFont.Bold))
        title.setStyleSheet(f"color: {APPLE_COLORS['text_primary']}; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # Logo展示区域
        logo_layout = QHBoxLayout()
        logo_layout.setSpacing(40)
        
        # 大尺寸Logo
        large_section = QVBoxLayout()
        large_label = QLabel("主Logo (80px)")
        large_label.setAlignment(Qt.AlignCenter)
        large_label.setStyleSheet(f"color: {APPLE_COLORS['text_secondary']}; font-weight: bold;")
        large_logo = EWRebornLogo(80)
        
        large_section.addWidget(large_label)
        large_section.addWidget(large_logo, 0, Qt.AlignCenter)
        
        # 中等尺寸Logo
        medium_section = QVBoxLayout()
        medium_label = QLabel("中等Logo (48px)")
        medium_label.setAlignment(Qt.AlignCenter)
        medium_label.setStyleSheet(f"color: {APPLE_COLORS['text_secondary']}; font-weight: bold;")
        medium_logo = EWRebornLogo(48)
        
        medium_section.addWidget(medium_label)
        medium_section.addWidget(medium_logo, 0, Qt.AlignCenter)
        
        # 小尺寸Logo
        small_section = QVBoxLayout()
        small_label = QLabel("迷你Logo (24px)")
        small_label.setAlignment(Qt.AlignCenter)
        small_label.setStyleSheet(f"color: {APPLE_COLORS['text_secondary']}; font-weight: bold;")
        small_logo = EWRebornMiniLogo(24)
        
        small_section.addWidget(small_label)
        small_section.addWidget(small_logo, 0, Qt.AlignCenter)
        
        logo_layout.addLayout(large_section)
        logo_layout.addLayout(medium_section)
        logo_layout.addLayout(small_section)
        
        layout.addLayout(logo_layout)
        
        # 设计说明
        description = QLabel("""
设计理念：
• 左右两个圆形代表I/O输入输出点
• 中心方形代表智能分配算法核心
• 连接线和数据流点表示自动化处理过程
• 弧形装饰线代表系统架构的完整性
• 渐变色彩体现现代科技感
• 扁平化设计符合当代审美趋势
        """)
        description.setStyleSheet(f"""
            color: {APPLE_COLORS['text_secondary']};
            background-color: {APPLE_COLORS['surface_tertiary']};
            padding: 20px;
            border-radius: 10px;
            line-height: 1.6;
        """)
        layout.addWidget(description)
        
        # 颜色说明
        color_info = QLabel(f"""
主要颜色：
• 主色调：{APPLE_COLORS['primary']} (科技蓝)
• 辅助色：{APPLE_COLORS['success']} (成功绿)  
• 强调色：{APPLE_COLORS['warning']} (活力橙)
        """)
        color_info.setStyleSheet(f"""
            color: {APPLE_COLORS['text_tertiary']};
            font-size: 12px;
            margin-top: 10px;
        """)
        layout.addWidget(color_info)
    
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {APPLE_COLORS['background']};
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            }}
        """)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建展示窗口
    showcase = LogoShowcase()
    showcase.show()
    
    # 居中显示
    screen = app.primaryScreen().availableGeometry()
    x = (screen.width() - showcase.width()) // 2
    y = (screen.height() - showcase.height()) // 2
    showcase.move(x, y)
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
