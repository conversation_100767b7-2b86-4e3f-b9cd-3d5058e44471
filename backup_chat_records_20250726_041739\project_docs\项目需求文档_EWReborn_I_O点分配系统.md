# I/O点分配系统 (EWReborn) - 完整项目需求文档

## 文档信息
- **项目名称**: I/O点分配系统 (EWReborn)
- **文档版本**: v1.0
- **创建日期**: 2025-07-21
- **文档类型**: 完整项目需求规范
- **适用范围**: AI重新实现参考文档

---

## 1. 项目概述

### 1.1 项目简介
**I/O点分配系统 (EWReborn)** 是一个基于物理空间约束的智能I/O点分配系统，用于自动化工程设计中的I/O点到机柜导轨、端子排和卡件槽位的智能分配。

### 1.2 核心价值
- **自动化设计**: 替代手工I/O点分配，提高设计效率
- **物理约束验证**: 确保分配结果符合实际物理空间限制
- **智能优化**: 多种分配策略，优化空间利用率和系统性能
- **错误预防**: 提前发现设计冲突，减少现场问题

### 1.3 技术架构
```
┌─────────────────────────────────────────────────────────┐
│                    GUI层 (PySide6)                      │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────┐ │
│  │  主界面窗口      │ │  分配设置界面    │ │  结果显示    │ │
│  │ (main_window)   │ │(allocation_widget)│ │  (results)  │ │
│  └─────────────────┘ └─────────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                   业务逻辑层                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────┐ │
│  │   分配器引擎     │ │  验证器组件      │ │  管理器组件  │ │
│  │  (allocator)    │ │ (validators)    │ │ (managers)  │ │
│  └─────────────────┘ └─────────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                   数据访问层                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────┐ │
│  │   数据加载器     │ │   Excel工具     │ │  配置管理    │ │
│  │ (data_loader)   │ │ (excel_utils)   │ │ (config)    │ │
│  └─────────────────┘ └─────────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                    数据存储层                            │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────┐ │
│  │   IODB数据      │ │   机柜配置       │ │  典型回路    │ │
│  │  (Excel文件)    │ │  (XML文件)      │ │ (XML文件)   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 1.4 核心功能模块
1. **物理空间验证模块**: 基于典型回路XML解析器件尺寸，验证导轨容量
2. **端子排分配模块**: 支持ETP形式和电缆形式两种分配策略
3. **卡件槽位管理模块**: 智能分配I/O卡件到机架槽位
4. **电缆完整性约束模块**: 严格执行电缆不跨柜分配的核心规则
5. **数据加载模块**: 解析IODB Excel文件、机柜配置XML、典型回路XML
6. **GUI交互模块**: 提供直观的配置界面和结果展示

---

## 2. 核心约束规则

### 2.1 电缆完整性约束（最高优先级规则）

**规则定义**: 每个电缆（Cable）的所有I/O点必须分配到同一个机柜中，绝对不允许跨柜分配。

**重要性**: 这是系统的最高优先级约束，任何分配算法都必须严格遵守此规则。

**技术实现要求**:
```python
# 核心约束验证
def validate_cable_integrity_constraint(cables):
    """验证电缆完整性约束 - 系统核心规则"""
    violations = []

    for cable in cables:
        allocated_cabinets = set()
        for io_point in cable.io_points:
            if io_point.allocated_cabinet:
                allocated_cabinets.add(io_point.allocated_cabinet)

        # 检查是否违反跨柜约束
        if len(allocated_cabinets) > 1:
            violations.append({
                'cable_name': cable.name,
                'violation_type': 'CROSS_CABINET_ALLOCATION',
                'cabinets': list(allocated_cabinets),
                'severity': 'CRITICAL'
            })

    return violations

# 分配算法必须遵循的约束
def allocate_cable_with_integrity_constraint(cable, cabinets):
    """
    电缆分配算法 - 严格遵循完整性约束
    """
    for cabinet in cabinets:
        # 尝试将整个电缆分配到单个机柜
        if can_accommodate_entire_cable(cabinet, cable):
            # 原子性分配：要么全部成功，要么全部失败
            success = allocate_all_io_points_to_cabinet(cable.io_points, cabinet)
            if success:
                return True, cabinet.name

    return False, "无法找到能容纳整个电缆的机柜"
```

**约束影响范围**:
1. **空间验证**: 必须确保单个机柜能容纳电缆的所有I/O点
2. **端子排分配**: 电缆内所有I/O点的端子排必须在同一机柜
3. **卡件槽位分配**: 电缆内所有I/O点的卡件必须在同一机柜
4. **回滚机制**: 分配失败时必须完整回滚整个电缆的分配状态

### 2.2 约束执行机制

#### 2.2.1 分配前验证
- 检查机柜容量是否能容纳整个电缆
- 验证信号类型和本安类型兼容性
- 确认典型回路空间需求

#### 2.2.2 分配过程控制
- 使用事务性分配：原子性操作
- 实现分配状态快照和回滚机制
- 确保分配失败时不留下部分分配状态

#### 2.2.3 分配后验证
- 验证所有I/O点都分配到同一机柜
- 检查分配结果的完整性
- 生成约束违反报告

---

## 3. 详细功能需求

### 2.1 数据加载与解析功能

#### 2.1.1 IODB数据加载
**功能描述**: 从Excel文件加载I/O点数据
**输入文件**: `data/iodb/IODB.xlsx`
**数据结构**:
```
列名映射:
- tagname → tag (I/O点标签)
- cable name → cable_name (电缆名称)
- pair number → pair_number (对线号)
- signaltype → signal_type (信号类型: AI/AO/DI/DO)
- system → system (系统名称)
- location → location (位置)
- is → is_intrinsic (本安类型: IS/NIS)
- cable type → cable_type (电缆类型)
- wiring typical → wiring_typical (典型回路名称)
- description → description (描述)
```

**处理逻辑**:
1. 读取Excel文件所有工作表
2. 解析每行数据创建IOPoint对象
3. 按电缆名称分组创建Cable对象
4. 生成统计摘要信息

#### 2.1.2 机柜配置XML解析
**功能描述**: 解析机柜配置XML文件，提取导轨和机架信息
**输入文件**: `data/cabinet_profiles/*.xml`
**解析目标**:
```xml
<ProfileComponent Name="机柜名称" Type="Cabinet">
  <Components>
    <ProfileComponent Name="导轨名称" Mapping="Rail">
      <ProfileProperties>
        <ProfileProperty Name="Position" Value="导轨位置"/>
        <ProfileProperty Name="Length" Value="导轨长度(mm)"/>
        <ProfileProperty Name="IOType" Value="IO类型(Analog/Digital/Mixed)"/>
        <ProfileProperty Name="Intrinsic" Value="本安类型(IS/NIS/Mixed)"/>
        <ProfileProperty Name="ReservedFrom" Value="保留起始位置"/>
        <ProfileProperty Name="ReservedTo" Value="保留结束位置"/>
        <ProfileProperty Name="PartType01" Value="支持的器件类型1"/>
        <ProfileProperty Name="PartType02" Value="支持的器件类型2"/>
      </ProfileProperties>
    </ProfileComponent>
    <ProfileComponent Name="机架名称" Type="HardwarePart">
      <ProfileProperties>
        <ProfileProperty Name="HardwareType" Value="机架类型"/>
        <ProfileProperty Name="PartNumber" Value="部件编号"/>
        <ProfileProperty Name="SlotsAvailable" Value="可用槽位列表"/>
      </ProfileProperties>
    </ProfileComponent>
  </Components>
</ProfileComponent>
```

#### 2.1.3 典型回路XML解析
**功能描述**: 解析典型回路XML文件，提取器件空间需求和机架要求
**输入文件**: `data/wiring_typical/*.xml`
**关键解析内容**:

1. **信号类型和本安属性**:
```xml
<ProfileProperty Name="SignalType" Value="AI"/>
<ProfileSignature Name="Intrinsic" Value="IS"/>
```

2. **辅助接线柜器件** (第一层器件，用于空间计算):
```xml
<ProfileComponent Name="MarshallingCabinet">
  <Components>
    <ProfileComponent Name="FTB1" Type="HardwarePart">
      <ProfileProperties>
        <ProfileProperty Name="Width" Value="5.1"/>
        <ProfileProperty Name="HardwareType" Value="FieldTermIn"/>
        <ProfileProperty Name="MountingType" Value="Rail"/>
      </ProfileProperties>
    </ProfileComponent>
  </Components>
</ProfileComponent>
```

3. **系统柜机架要求**:
```xml
<ProfileComponent Name="SystemCabinet">
  <ProfileProperties>
    <ProfileProperty Name="RangeOfParts" Value="TCN_Main_Chassis,TCN_EXP_Chassis,TCN_RXM_Chassis"/>
  </ProfileProperties>
  <Components>
    <ProfileComponent Name="3721" Type="HardwarePart">
      <ProfileProperties>
        <ProfileProperty Name="SlotsApplicable" Value="3L,4L,5L,6L,7L"/>
        <ProfileProperty Name="ChannelCount" Value="32"/>
      </ProfileProperties>
    </ProfileComponent>
  </Components>
</ProfileComponent>
```

### 2.2 物理空间验证功能

#### 2.2.1 器件空间需求计算
**算法逻辑**:
```python
def calculate_space_requirements(typical_xml):
    rail_components = extract_rail_components(typical_xml)
    total_space = 0
    
    for component in rail_components:
        # 器件尺寸取Width或Height属性值
        width = float(component.get('Width', component.get('Height', 0)))
        total_space += width
    
    # 添加器件间距（每个器件间10mm）
    if len(rail_components) > 1:
        total_space += (len(rail_components) - 1) * 10
    
    return total_space, rail_components
```

#### 2.2.2 导轨容量检查
**验证规则**:
```python
def validate_rail_capacity(rail, space_requirement):
    # 计算可用长度
    available_length = rail.length - (rail.reserved_to - rail.reserved_from)
    
    # 空间检查
    if space_requirement.total_space > available_length:
        return False, f"导轨{rail.name}可用长度{available_length}mm不足，需要{space_requirement.total_space}mm"
    
    # 器件类型兼容性检查
    for part_type in space_requirement.part_types:
        if part_type not in rail.part_types:
            return False, f"导轨{rail.name}不支持器件类型{part_type}"
    
    return True, ""
```

### 2.3 端子排分配功能

#### 2.3.1 ETP形式分配策略
**分配原理**:
- 将使用相同ETP器件的I/O点分组
- 基于ETP的通道数确定容量（通常16通道）
- 一个ETP端子排容纳32片FTB（16通道×2片/通道）
- 优先填满一个ETP再分配下一个

**实现算法**:
```python
def allocate_by_etp_form(cables, wiring_typicals):
    # 按ETP类型分组
    etp_groups = group_cables_by_etp_type(cables, wiring_typicals)
    
    for etp_type, cable_list in etp_groups.items():
        io_points = []
        for cable in cable_list:
            io_points.extend(cable.io_points)
        
        # 按端子排容量分组
        while io_points:
            terminal_block = create_terminal_block(etp_type)
            points_to_allocate = io_points[:terminal_block.max_io_points]
            io_points = io_points[terminal_block.max_io_points:]
            terminal_block.allocate_io_points(points_to_allocate)
```

#### 2.3.2 电缆形式分配策略
**分配原理**:
- **多点电缆（≥2个I/O点）**: 将电缆内所有I/O点分配到同一端子排
- **单点电缆（1个I/O点）**: 将相同典型回路的单点电缆分组到同一端子排
- **单点电缆端子排最大容量**: 16个I/O点
- **确保电缆完整性**: 避免跨端子排分割

### 2.4 卡件槽位管理功能

#### 2.4.1 卡件通道配置规则
```python
CARD_CHANNEL_CONFIG = {
    SignalType.AO: 8,   # AO卡件：8通道/卡件
    SignalType.AI: 32,  # AI卡件：32通道/卡件
    SignalType.DI: 32,  # DI卡件：32通道/卡件
    SignalType.DO: 32   # DO卡件：32通道/卡件
}

CARD_ETP_CONFIG = {
    SignalType.AO: 1,   # AO卡件：1个卡件对应1个ETP端子排
    SignalType.AI: 2,   # AI卡件：1个卡件对应2个ETP端子排
    SignalType.DI: 2,   # DI卡件：1个卡件对应2个ETP端子排
    SignalType.DO: 2    # DO卡件：1个卡件对应2个ETP端子排
}
```

#### 2.4.2 机架槽位分配规则
**机架类型和槽位范围**:
```python
RACK_SLOT_RANGES = {
    RackType.MAIN: ["3L", "3R", "4L", "4R", "5L", "5R", "6L", "6R", "7L", "7R"],  # 3-7号槽位
    RackType.EXP: ["1L", "1R", "2L", "2R", "3L", "3R", "4L", "4R", "5L", "5R", "6L", "6R", "7L", "7R", "8L", "8R"],  # 1-8号槽位
    RackType.RXM: ["2L", "2R", "3L", "3R", "4L", "4R", "5L", "5R", "6L", "6R", "7L", "7R"]  # 2-7号槽位
}

RACK_PRIORITY = {
    RackType.MAIN: 1,  # 主机架优先级最高
    RackType.EXP: 2,   # 扩展机架次之
    RackType.RXM: 3    # RXM机架优先级最低
}
```

#### 2.4.3 卡件命名规则
**格式**: `[前缀][机架号]S[槽位号]`
**示例**: `R3S1` 表示第3个机架的1号槽位
**前缀选项**: R（Rack）或C（Chassis），可在GUI中设定

---

## 3. 技术实现规范

### 3.1 核心数据模型

#### 3.1.1 IOPoint数据模型
```python
@dataclass
class IOPoint:
    """I/O点数据模型"""
    tag: str                  # 标签名
    cable_name: str          # 电缆名称
    pair_number: int         # 对线号
    signal_type: SignalType  # 信号类型
    system: str              # 系统
    location: str            # 位置
    is_intrinsic: bool       # 是否本安
    cable_type: str = ""     # 电缆类型
    wiring_typical: str = "" # 典型回路
    description: str = ""    # 描述
    
    # 分配结果属性
    allocated_cabinet: Optional[str] = None    # 分配的机柜
    allocated_rail: Optional[str] = None       # 分配的导轨
    allocated_rack: Optional[str] = None       # 分配的机架
    allocated_slot: Optional[str] = None       # 分配的槽位
    allocated_card: Optional[str] = None       # 分配的卡件
    allocated_terminal_block: Optional[str] = None  # 分配的端子排
    allocated_channel: Optional[int] = None    # 分配的通道
    allocated_position: Optional[float] = None # 在导轨上的位置
    allocation_status: str = "未分配"          # 分配状态
```

#### 3.1.2 IOCard数据模型
```python
@dataclass
class IOCard:
    """I/O卡件数据模型"""
    name: str                           # 卡件名称
    part_number: str                    # 部件编号
    hardware_type: str                  # 硬件类型
    signal_type: SignalType            # 支持的信号类型
    channel_count: int                 # 通道数量
    slots_applicable: List[str]        # 可插入的槽位
    rack_types: List[RackType]         # 支持的机架类型
    
    # 分配信息
    allocated_rack: Optional[str] = None        # 分配的机架
    allocated_slot: Optional[str] = None        # 分配的槽位
    allocated_io_points: List[IOPoint]          # 分配的I/O点
    
    # ETP端子排对应关系
    etp_count: int = 2                          # 对应的ETP数量
    etp_upper_suffix: str = "U"                 # 上ETP后缀
    etp_lower_suffix: str = "L"                 # 下ETP后缀
    
    @property
    def card_identifier(self) -> str:
        """获取卡件标识符（如R3S1）"""
        if self.allocated_rack and self.allocated_slot:
            rack_num = self.allocated_rack.split('_')[-1] if '_' in self.allocated_rack else "1"
            return f"R{rack_num}S{self.allocated_slot}"
        return ""
```

#### 3.1.3 其他核心数据模型
```python
@dataclass
class Cable:
    """电缆数据模型"""
    name: str
    pair_size: int
    signal_type: SignalType
    cable_type: str
    system: str
    location: str
    is_intrinsic: bool
    io_points: List[IOPoint] = field(default_factory=list)

@dataclass
class Rail:
    """导轨数据模型"""
    name: str
    position: str
    length: int                    # 总长度(mm)
    io_type: IOType               # IO类型
    intrinsic: IntrinsicType      # 本安类型
    reserved_from: int = 0        # 保留起始位置
    reserved_to: int = 0          # 保留结束位置
    part_types: List[str] = field(default_factory=list)  # 支持的部件类型
    
    @property
    def available_length(self) -> int:
        """可用长度"""
        return self.length - (self.reserved_to - self.reserved_from)

@dataclass
class Rack:
    """机架数据模型"""
    name: str
    rack_type: str
    position: str
    part_type: str
    part_number: str
    available_slots: List[str] = field(default_factory=list)
    allocated_slots: Dict[str, Dict] = field(default_factory=dict)
    connectors: Dict[str, List[str]] = field(default_factory=dict)
    rack_type_enum: Optional[RackType] = None
```

### 3.2 核心算法实现

#### 3.2.1 空间验证算法
```python
class SpaceValidator:
    def validate_and_allocate(self, io_points, cabinets, wiring_typicals):
        # 1. 初始化导轨分配
        self._initialize_rail_allocations(cabinets)
        
        # 2. 计算每个I/O点的空间需求
        space_requirements = self._calculate_space_requirements(io_points, wiring_typicals)
        
        # 3. 执行空间分配
        success = self._allocate_space_requirements(space_requirements)
        
        # 4. 生成分配摘要
        summary = self._generate_allocation_summary()
        
        return success, errors, summary
```

#### 3.2.2 端子排分配算法
```python
class TerminalBlockManager:
    def allocate_cables(self, cables, wiring_typicals):
        if self.strategy == TerminalBlockStrategy.ETP_FORM:
            return self._allocate_by_etp_form(cables, wiring_typicals)
        elif self.strategy == TerminalBlockStrategy.CABLE_FORM:
            return self._allocate_by_cable_form(cables, wiring_typicals)
```

#### 3.2.3 卡件槽位分配算法
```python
class CardSlotManager:
    def allocate_io_points(self, io_points, wiring_typicals):
        # 1. 按典型回路和信号类型分组
        io_groups = self._group_io_points(io_points)
        
        # 2. 创建卡件
        self._create_cards_from_io_groups(io_groups, wiring_typicals)
        
        # 3. 分配卡件到机架槽位
        success = self._allocate_cards_to_racks(wiring_typicals)
        
        return success, errors, summary
```

---

## 4. GUI界面规范

### 4.1 主界面布局
```python
class AllocationWidget(QWidget):
    def __init__(self):
        # 创建主布局
        main_layout = QVBoxLayout()
        
        # 1. 数据加载区域
        data_group = self._create_data_group()
        main_layout.addWidget(data_group)
        
        # 2. 分配设置区域
        settings_group = self._create_settings_group()
        main_layout.addWidget(settings_group)
        
        # 3. 控制按钮区域
        control_layout = self._create_control_layout()
        main_layout.addLayout(control_layout)
        
        # 4. 状态显示区域
        status_layout = self._create_status_layout()
        main_layout.addLayout(status_layout)
```

### 4.2 分配设置界面
```python
def _create_settings_group(self):
    settings_group = QGroupBox("分配设置")
    settings_layout = QVBoxLayout(settings_group)
    
    # 分配顺序选择
    self.allocation_order_combo = QComboBox()
    self.allocation_order_combo.addItems([
        "电缆名称和Pair升序",
        "电缆名称和Pair降序", 
        "系统分组"
    ])
    
    # 端子排分配策略选择
    self.terminal_block_strategy_combo = QComboBox()
    self.terminal_block_strategy_combo.addItems([
        "ETP形式分配",
        "电缆形式分配"
    ])
    
    # 卡件配置组
    card_group = QGroupBox("卡件配置")
    
    # 机架前缀选择
    self.rack_prefix_combo = QComboBox()
    self.rack_prefix_combo.addItems(["R (Rack)", "C (Chassis)"])
    
    # ETP后缀设置
    self.etp_upper_suffix_edit = QLineEdit("U")
    self.etp_lower_suffix_edit = QLineEdit("L")
    
    # 卡件分配策略
    self.card_allocation_strategy_combo = QComboBox()
    self.card_allocation_strategy_combo.addItems([
        "优先级分配",
        "负载均衡分配"
    ])
```

### 4.3 结果显示和错误反馈
```python
def _create_status_layout(self):
    layout = QHBoxLayout()
    
    # 状态标签
    self.status_label = QLabel("就绪")
    layout.addWidget(self.status_label)
    
    # 弹性空间
    layout.addStretch()
    
    # 失败原因显示标签
    self.failure_reason_label = QLabel("")
    self.failure_reason_label.setStyleSheet("color: #FF3B30; font-weight: bold;")
    self.failure_reason_label.setVisible(False)
    layout.addWidget(self.failure_reason_label)
    
    # 进度条
    self.progress_bar = QProgressBar()
    self.progress_bar.setVisible(False)
    layout.addWidget(self.progress_bar)
    
    return layout
```

---

## 5. 配置参数规范

### 5.1 系统配置参数
```python
# 卡件通道配置
CARD_CHANNEL_CONFIG = {
    SignalType.AO: 8,   # AO卡件：8通道/卡件
    SignalType.AI: 32,  # AI卡件：32通道/卡件
    SignalType.DI: 32,  # DI卡件：32通道/卡件
    SignalType.DO: 32   # DO卡件：32通道/卡件
}

# 卡件ETP对应关系
CARD_ETP_CONFIG = {
    SignalType.AO: 1,   # AO卡件：1个卡件对应1个ETP端子排
    SignalType.AI: 2,   # AI卡件：1个卡件对应2个ETP端子排
    SignalType.DI: 2,   # DI卡件：1个卡件对应2个ETP端子排
    SignalType.DO: 2    # DO卡件：1个卡件对应2个ETP端子排
}

# 机架槽位范围
RACK_SLOT_RANGES = {
    RackType.MAIN: ["3L", "3R", "4L", "4R", "5L", "5R", "6L", "6R", "7L", "7R"],
    RackType.EXP: ["1L", "1R", "2L", "2R", "3L", "3R", "4L", "4R", "5L", "5R", "6L", "6R", "7L", "7R", "8L", "8R"],
    RackType.RXM: ["2L", "2R", "3L", "3R", "4L", "4R", "5L", "5R", "6L", "6R", "7L", "7R"]
}

# 机架优先级
RACK_PRIORITY = {
    RackType.MAIN: 1,  # 主机架优先级最高
    RackType.EXP: 2,   # 扩展机架次之
    RackType.RXM: 3    # RXM机架优先级最低
}
```

### 5.2 GUI配置选项
```python
# 默认配置
DEFAULT_CONFIG = {
    'rack_prefix': 'R',           # 机架前缀 (R/C)
    'etp_upper_suffix': 'U',      # 上ETP后缀
    'etp_lower_suffix': 'L',      # 下ETP后缀
    'allocation_strategy': 'PRIORITY',  # 卡件分配策略
    'terminal_block_strategy': 'ETP_FORM',  # 端子排分配策略
    'device_spacing': 10,         # 器件间距(mm)
    'enable_parttype_matching': True,  # 启用部件类型匹配
    'max_allocation_attempts': 1000    # 最大分配尝试次数
}
```

---

## 6. 文件结构和依赖

### 6.1 项目文件结构
```
EWReborn/
├── main.py                          # 主程序入口
├── core/                            # 核心业务逻辑
│   ├── __init__.py
│   ├── allocator.py                 # 分配器引擎
│   ├── data_models.py               # 数据模型定义
│   ├── terminal_block_manager.py    # 端子排管理器
│   ├── space_validator.py           # 空间验证器
│   ├── card_slot_manager.py         # 卡件槽位管理器
│   ├── validator.py                 # 数据验证器
│   ├── data_loader_simple.py        # 数据加载器
│   └── logger.py                    # 日志管理
├── gui/                             # GUI界面
│   ├── __init__.py
│   ├── allocation_widget.py         # 分配界面组件
│   ├── icons.py                     # 图标资源
│   └── styles.py                    # 样式定义
├── utils/                           # 工具模块
│   ├── __init__.py
│   ├── excel_utils_simple.py        # Excel工具
│   └── config_manager_simple.py     # 配置管理
├── data/                            # 数据文件
│   ├── iodb/
│   │   └── IODB.xlsx                # I/O数据库
│   ├── cabinet_profiles/            # 机柜配置
│   │   ├── PPG BAR.xml
│   │   └── PPG SYS.xml
│   └── wiring_typical/              # 典型回路
│       ├── AI IS BABP.xml
│       └── AI NIS ISL 3WIRE 2OUT.xml
├── docs/                            # 文档
│   └── 项目需求文档_EWReborn_I_O点分配系统.md
└── requirements.txt                 # 依赖包列表
```

### 6.2 核心依赖包
```txt
PySide6>=6.5.0          # GUI框架
pandas>=2.0.0           # 数据处理
openpyxl>=3.1.0         # Excel文件处理
lxml>=4.9.0             # XML文件处理
```

### 6.3 模块依赖关系
```python
# 依赖层次结构
main.py
├── gui.allocation_widget
│   ├── core.allocator
│   │   ├── core.terminal_block_manager
│   │   ├── core.space_validator
│   │   ├── core.card_slot_manager
│   │   └── core.data_models
│   ├── core.data_loader_simple
│   │   ├── utils.excel_utils_simple
│   │   └── core.data_models
│   └── utils.config_manager_simple
└── gui.styles
```

---

## 7. 测试验证规范

### 7.1 单元测试要求
```python
# 测试覆盖范围
test_modules = [
    'test_data_models.py',           # 数据模型测试
    'test_space_validator.py',       # 空间验证测试
    'test_terminal_block_manager.py', # 端子排管理测试
    'test_card_slot_manager.py',     # 卡件槽位管理测试
    'test_allocator.py',             # 分配器测试
    'test_data_loader.py',           # 数据加载测试
    'test_excel_utils.py',           # Excel工具测试
]

# 测试数据要求
test_data = {
    'iodb_sample.xlsx': '包含各种信号类型的I/O点数据',
    'cabinet_sample.xml': '包含导轨和机架的机柜配置',
    'typical_sample.xml': '包含完整器件信息的典型回路'
}
```

### 7.2 集成测试场景
```python
integration_tests = [
    {
        'name': '完整分配流程测试',
        'steps': [
            '加载IODB数据',
            '加载机柜配置',
            '加载典型回路',
            '执行空间验证',
            '执行端子排分配',
            '执行卡件槽位分配',
            '验证分配结果'
        ],
        'expected_result': '所有I/O点成功分配，无错误'
    },
    {
        'name': '不同策略对比测试',
        'steps': [
            '使用ETP形式分配策略',
            '使用电缆形式分配策略',
            '对比分配结果',
            '验证策略差异'
        ],
        'expected_result': '两种策略都能成功分配，结果有差异'
    }
]
```

### 7.3 性能测试要求
```python
performance_requirements = {
    'data_loading': {
        'max_time': '10秒',
        'max_memory': '500MB',
        'test_data_size': '1000个I/O点'
    },
    'allocation_process': {
        'max_time': '30秒',
        'max_memory': '1GB',
        'test_data_size': '1000个I/O点'
    },
    'gui_response': {
        'max_response_time': '1秒',
        'smooth_interaction': True
    }
}
```

---

## 8. 部署和维护

### 8.1 部署要求
```python
deployment_requirements = {
    'operating_system': 'Windows 10/11',
    'python_version': '3.8+',
    'memory': '4GB RAM minimum, 8GB recommended',
    'storage': '1GB available space',
    'display': '1920x1080 minimum resolution'
}
```

### 8.2 配置文件管理
```python
# config.json 示例
{
    "allocation_settings": {
        "enable_parttype_matching": true,
        "max_allocation_attempts": 1000,
        "device_spacing": 10
    },
    "card_settings": {
        "rack_prefix": "R",
        "etp_upper_suffix": "U",
        "etp_lower_suffix": "L",
        "allocation_strategy": "PRIORITY"
    },
    "gui_settings": {
        "window_size": [1200, 800],
        "theme": "light"
    }
}
```

### 8.3 日志和监控
```python
logging_config = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'handlers': [
        'console_handler',
        'file_handler'
    ],
    'log_files': [
        'logs/application.log',
        'logs/allocation.log',
        'logs/error.log'
    ]
}
```

---

## 9. 扩展和维护指南

### 9.1 功能扩展点
1. **新增信号类型支持**: 在SignalType枚举中添加新类型
2. **新增机架类型支持**: 在RackType枚举中添加新类型
3. **新增分配策略**: 实现新的分配算法类
4. **新增数据源支持**: 扩展数据加载器支持其他格式

### 9.2 维护注意事项
1. **数据模型变更**: 需要同步更新所有相关的处理逻辑
2. **算法优化**: 注意保持向后兼容性
3. **GUI更新**: 确保界面响应性和用户体验
4. **测试覆盖**: 新功能必须包含完整的测试用例

---

## 10. 总结

本文档详细描述了I/O点分配系统(EWReborn)的完整技术需求和实现规范。该系统通过智能算法实现了基于物理空间约束的I/O点自动分配，大大提高了工程设计效率。

**核心特性**:
- ✅ 物理空间约束验证
- ✅ 多种端子排分配策略
- ✅ 智能卡件槽位管理
- ✅ 直观的GUI配置界面
- ✅ 详细的错误反馈机制
- ✅ 灵活的配置参数系统

该文档为AI重新实现该项目提供了完整的技术指导，包含了所有必要的实现细节、配置参数和测试要求。

---

**文档版本**: v1.0  
**最后更新**: 2025-07-21  
**文档状态**: ✅ 完整  
**适用范围**: AI重新实现参考
