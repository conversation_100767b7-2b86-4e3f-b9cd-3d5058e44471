2025-07-16 15:46:11 - core.logger - INFO - 日志系统初始化完成
2025-07-16 15:46:11 - core.logger - INFO - 日志级别: INFO
2025-07-16 15:46:11 - core.logger - INFO - 日志文件: C:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-16 15:46:11 - __main__ - INFO - EWReborn应用程序启动
2025-07-16 15:46:11 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-16 15:46:11 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-16 15:46:11 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-16 15:46:11 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-16 15:46:11 - gui.main_window - INFO - 主窗口初始化完成
2025-07-16 15:46:11 - __main__ - INFO - 主窗口创建成功
2025-07-16 15:49:14 - core.logger - INFO - 日志系统初始化完成
2025-07-16 15:49:14 - core.logger - INFO - 日志级别: INFO
2025-07-16 15:49:14 - core.logger - INFO - 日志文件: C:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-16 15:49:14 - __main__ - INFO - 测试日志消息
2025-07-16 19:42:09 - core.logger - INFO - 日志系统初始化完成
2025-07-16 19:42:09 - core.logger - INFO - 日志级别: INFO
2025-07-16 19:42:09 - core.logger - INFO - 日志文件: C:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-16 19:42:09 - __main__ - INFO - 系统测试开始
2025-07-16 19:42:09 - core.data_loader_simple - INFO - 开始加载所有数据
2025-07-16 19:42:09 - core.data_loader_simple - INFO - 加载机柜配置: C:\Users\<USER>\Desktop\Python\EWReborn\data\cabinet_profiles
2025-07-16 19:42:09 - core.data_loader_simple - INFO - 找到2个机柜配置文件
2025-07-16 19:42:09 - core.data_loader_simple - INFO - 机柜配置文件加载成功
2025-07-16 19:42:09 - core.data_loader_simple - INFO - 加载典型回路: C:\Users\<USER>\Desktop\Python\EWReborn\data\wiring_typical
2025-07-16 19:42:09 - core.data_loader_simple - INFO - 找到2个典型回路文件
2025-07-16 19:42:09 - core.data_loader_simple - INFO - 典型回路文件加载成功
2025-07-16 19:42:09 - core.data_loader_simple - INFO - 数据加载完成
2025-07-16 19:42:09 - core.data_loader_simple - INFO - 创建数据模型对象
2025-07-16 19:42:09 - core.data_loader_simple - INFO - 数据模型创建完成: 2个I/O点, 1条电缆, 2个机柜, 2个典型回路
2025-07-16 19:42:09 - core.validator - INFO - 开始IODB数据验证
2025-07-16 19:42:09 - validation - INFO - 开始I/O点分配过程，总计 1 条电缆
2025-07-16 19:42:09 - core.validator - INFO - 验证2个I/O点和1条电缆
2025-07-16 19:42:09 - core.validator - INFO - Tag唯一性验证通过
2025-07-16 19:42:09 - core.validator - INFO - Cable配对验证通过
2025-07-16 19:42:09 - core.validator - INFO - Cable属性一致性验证通过
2025-07-16 19:42:09 - core.validator - INFO - 数据完整性检查通过
2025-07-16 19:42:09 - core.validator - INFO - IODB数据验证通过
2025-07-16 19:42:09 - validation - INFO - I/O点分配过程完成，耗时: 0:00:00.001372
2025-07-16 19:42:09 - validation - INFO - 分配统计:
2025-07-16 19:42:09 - validation - INFO -   总电缆数: 1
2025-07-16 19:42:09 - validation - INFO -   已处理: 0
2025-07-16 19:42:09 - validation - INFO -   成功分配: 0
2025-07-16 19:42:09 - validation - INFO -   分配失败: 0
2025-07-16 19:42:09 - validation - INFO -   警告数: 0
2025-07-16 19:42:09 - validation - INFO -   错误数: 0
2025-07-16 19:42:09 - validation - INFO -   成功率: 0.0%
2025-07-16 19:42:09 - core.validator - INFO - IODB数据验证完成
2025-07-16 19:42:09 - core.allocator - INFO - 开始I/O点分配
2025-07-16 19:42:09 - allocation - INFO - 开始I/O点分配过程，总计 1 条电缆
2025-07-16 19:42:09 - allocation - INFO - 开始处理电缆: CABLE_001 (2个点, AI, IS)
2025-07-16 19:42:09 - allocation - ERROR -   ✗ CABLE_001 分配失败: 无法找到合适的机柜进行分配
2025-07-16 19:42:09 - allocation - ERROR - 电缆 CABLE_001 处理失败: 无法找到合适的机柜进行分配
2025-07-16 19:42:09 - core.allocator - WARNING - I/O点分配完成，但有2个点分配失败
2025-07-16 19:42:09 - allocation - INFO - I/O点分配过程完成，耗时: 0:00:00.001272
2025-07-16 19:42:09 - allocation - INFO - 分配统计:
2025-07-16 19:42:09 - allocation - INFO -   总电缆数: 1
2025-07-16 19:42:09 - allocation - INFO -   已处理: 1
2025-07-16 19:42:09 - allocation - INFO -   成功分配: 0
2025-07-16 19:42:09 - allocation - INFO -   分配失败: 1
2025-07-16 19:42:09 - allocation - INFO -   警告数: 0
2025-07-16 19:42:09 - allocation - INFO -   错误数: 1
2025-07-16 19:42:09 - allocation - INFO -   成功率: 0.0%
2025-07-16 19:42:09 - core.allocator - INFO - I/O点分配完成
2025-07-16 19:42:09 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-16 19:42:09 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-16 19:42:09 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-16 19:42:09 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-16 19:42:09 - gui.main_window - INFO - 主窗口初始化完成
2025-07-16 19:42:09 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-16 19:42:09 - gui.progress_widget - INFO - 进度显示组件初始化完成
2025-07-16 19:42:20 - core.logger - INFO - 日志系统初始化完成
2025-07-16 19:42:20 - core.logger - INFO - 日志级别: INFO
2025-07-16 19:42:20 - core.logger - INFO - 日志文件: C:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-16 19:42:20 - __main__ - INFO - EWReborn应用程序启动
2025-07-16 19:42:20 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-16 19:42:20 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-16 19:42:20 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-16 19:42:20 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-16 19:42:20 - gui.main_window - INFO - 主窗口初始化完成
2025-07-16 19:42:20 - __main__ - INFO - 主窗口创建成功
2025-07-17 11:16:14 - core.logger - INFO - 日志系统初始化完成
2025-07-17 11:16:14 - core.logger - INFO - 日志级别: INFO
2025-07-17 11:16:14 - core.logger - INFO - 日志文件: c:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-17 11:16:14 - __main__ - INFO - EWReborn应用程序启动
2025-07-17 11:16:14 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-17 11:16:14 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-17 11:16:14 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-17 11:16:14 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-17 11:16:14 - gui.main_window - INFO - 主窗口初始化完成
2025-07-17 11:16:14 - __main__ - INFO - 主窗口创建成功
2025-07-17 11:16:20 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-17 11:16:20 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-17 11:32:55 - core.logger - INFO - 日志系统初始化完成
2025-07-17 11:32:55 - core.logger - INFO - 日志级别: INFO
2025-07-17 11:32:55 - core.logger - INFO - 日志文件: C:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-17 11:32:55 - __main__ - INFO - EWReborn应用程序启动
2025-07-17 11:32:56 - gui.main_window - ERROR - 创建I/O分配选项卡失败: name 'create_icon_from_svg' is not defined
2025-07-17 11:32:56 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-17 11:32:56 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-17 11:32:56 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-17 11:32:56 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-17 11:32:56 - gui.main_window - INFO - 主窗口初始化完成
2025-07-17 11:32:56 - __main__ - INFO - 主窗口创建成功
2025-07-17 11:33:02 - __main__ - INFO - 🍎 主窗口显示完成
2025-07-17 11:33:40 - core.logger - INFO - 日志系统初始化完成
2025-07-17 11:33:40 - core.logger - INFO - 日志级别: INFO
2025-07-17 11:33:40 - core.logger - INFO - 日志文件: c:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-17 11:33:40 - __main__ - INFO - EWReborn应用程序启动
2025-07-17 11:33:41 - gui.main_window - ERROR - 创建I/O分配选项卡失败: name 'create_icon_from_svg' is not defined
2025-07-17 11:33:41 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-17 11:33:41 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-17 11:33:41 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-17 11:33:41 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-17 11:33:41 - gui.main_window - INFO - 主窗口初始化完成
2025-07-17 11:33:41 - __main__ - INFO - 主窗口创建成功
2025-07-17 11:33:47 - __main__ - INFO - 🍎 主窗口显示完成
2025-07-17 11:34:05 - gui.main_window - INFO - 新建项目
2025-07-17 11:34:12 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-17 11:34:12 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-17 11:38:31 - core.logger - INFO - 日志系统初始化完成
2025-07-17 11:38:31 - core.logger - INFO - 日志级别: INFO
2025-07-17 11:38:31 - core.logger - INFO - 日志文件: C:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-17 11:38:31 - __main__ - INFO - EWReborn应用程序启动
2025-07-17 11:38:33 - gui.main_window - ERROR - 创建I/O分配选项卡失败: name 'create_icon_from_svg' is not defined
2025-07-17 11:38:33 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-17 11:38:33 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-17 11:38:33 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-17 11:38:33 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-17 11:38:33 - gui.main_window - INFO - 主窗口初始化完成
2025-07-17 11:38:33 - __main__ - INFO - 主窗口创建成功
2025-07-17 11:38:39 - __main__ - INFO - 🍎 主窗口显示完成
2025-07-17 11:38:45 - gui.main_window - INFO - 新建项目
2025-07-17 11:38:49 - gui.main_window - INFO - 开始数据验证
2025-07-17 11:38:54 - gui.main_window - INFO - 打开项目
2025-07-17 11:41:16 - core.logger - INFO - 日志系统初始化完成
2025-07-17 11:41:16 - core.logger - INFO - 日志级别: INFO
2025-07-17 11:41:16 - core.logger - INFO - 日志文件: c:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-17 11:41:16 - __main__ - INFO - EWReborn应用程序启动
2025-07-17 11:41:17 - gui.main_window - ERROR - 创建I/O分配选项卡失败: name 'create_icon_from_svg' is not defined
2025-07-17 11:41:17 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-17 11:41:17 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-17 11:41:17 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-17 11:41:17 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-17 11:41:17 - gui.main_window - INFO - 主窗口初始化完成
2025-07-17 11:41:17 - __main__ - INFO - 主窗口创建成功
2025-07-17 11:41:23 - __main__ - INFO - 🍎 主窗口显示完成
2025-07-17 11:41:25 - gui.main_window - INFO - 新建项目
2025-07-17 11:41:29 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-17 11:41:29 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-17 11:51:02 - core.logger - INFO - 日志系统初始化完成
2025-07-17 11:51:02 - core.logger - INFO - 日志级别: INFO
2025-07-17 11:51:02 - core.logger - INFO - 日志文件: C:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-17 11:51:02 - __main__ - INFO - EWReborn应用程序启动
2025-07-17 11:51:03 - gui.main_window - ERROR - 创建I/O分配选项卡失败: name 'create_icon_from_svg' is not defined
2025-07-17 11:51:03 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-17 11:51:03 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-17 11:51:03 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-17 11:51:03 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-17 11:51:03 - gui.main_window - INFO - 主窗口初始化完成
2025-07-17 11:51:03 - __main__ - INFO - 主窗口创建成功
2025-07-17 11:51:09 - __main__ - INFO - 🍎 主窗口显示完成
2025-07-17 12:02:06 - core.logger - INFO - 日志系统初始化完成
2025-07-17 12:02:06 - core.logger - INFO - 日志级别: INFO
2025-07-17 12:02:06 - core.logger - INFO - 日志文件: c:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-17 12:02:06 - __main__ - INFO - EWReborn应用程序启动
2025-07-17 12:02:07 - gui.main_window - ERROR - 创建I/O分配选项卡失败: name 'create_icon_from_svg' is not defined
2025-07-17 12:02:07 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-17 12:02:07 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-17 12:02:07 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-17 12:02:07 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-17 12:02:07 - gui.main_window - INFO - 主窗口初始化完成
2025-07-17 12:02:07 - __main__ - INFO - 主窗口创建成功
2025-07-17 12:02:13 - __main__ - INFO - 🍎 主窗口显示完成
2025-07-17 12:02:17 - gui.main_window - INFO - 新建项目
2025-07-17 12:25:42 - gui.main_window - ERROR - 创建项目失败: name 'create_icon_from_svg' is not defined
2025-07-17 12:25:44 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-17 12:25:44 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-17 12:25:45 - core.logger - INFO - 日志系统初始化完成
2025-07-17 12:25:45 - core.logger - INFO - 日志级别: INFO
2025-07-17 12:25:45 - core.logger - INFO - 日志文件: c:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-17 12:25:45 - __main__ - INFO - EWReborn应用程序启动
2025-07-17 12:25:47 - gui.allocation_widget - INFO - 苹果风格增强应用成功
2025-07-17 12:25:47 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-07-17 12:25:47 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-17 12:25:47 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-17 12:25:47 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-17 12:25:47 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-17 12:25:47 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-17 12:25:47 - gui.main_window - INFO - 主窗口初始化完成
2025-07-17 12:25:47 - __main__ - INFO - 主窗口创建成功
2025-07-17 12:25:52 - __main__ - INFO - 🍎 主窗口显示完成
2025-07-17 12:26:25 - gui.xml_editor_widget - INFO - 启动独立XML编辑器
2025-07-17 12:26:25 - gui.xml_editor_widget - INFO - 独立XML编辑器启动成功
2025-07-17 12:27:13 - gui.allocation_widget - INFO - 开始数据验证
2025-07-17 12:27:13 - core.validator - INFO - 开始IODB数据验证
2025-07-17 12:27:13 - validation - INFO - 开始I/O点分配过程，总计 1 条电缆
2025-07-17 12:27:13 - validation - INFO - I/O点分配过程完成，耗时: 0:00:00.000372
2025-07-17 12:27:13 - validation - INFO - 分配统计:
2025-07-17 12:27:13 - validation - INFO -   总电缆数: 1
2025-07-17 12:27:13 - validation - INFO -   已处理: 0
2025-07-17 12:27:13 - validation - INFO -   成功分配: 0
2025-07-17 12:27:13 - validation - INFO -   分配失败: 0
2025-07-17 12:27:13 - validation - INFO -   警告数: 0
2025-07-17 12:27:13 - validation - INFO -   错误数: 0
2025-07-17 12:27:13 - validation - INFO -   成功率: 0.0%
2025-07-17 12:27:14 - gui.allocation_widget - INFO - 开始加载数据
2025-07-17 12:27:14 - core.data_loader_simple - INFO - 开始加载所有数据
2025-07-17 12:27:14 - core.data_loader_simple - INFO - 加载机柜配置: c:\Users\<USER>\Desktop\Python\EWReborn\data\cabinet_profiles
2025-07-17 12:27:14 - core.data_loader_simple - INFO - 找到2个机柜配置文件
2025-07-17 12:27:14 - core.data_loader_simple - INFO - 机柜配置文件加载成功
2025-07-17 12:27:14 - core.data_loader_simple - INFO - 加载典型回路: c:\Users\<USER>\Desktop\Python\EWReborn\data\wiring_typical
2025-07-17 12:27:14 - core.data_loader_simple - INFO - 找到2个典型回路文件
2025-07-17 12:27:14 - core.data_loader_simple - INFO - 典型回路文件加载成功
2025-07-17 12:27:14 - core.data_loader_simple - INFO - 数据加载完成
2025-07-17 12:27:14 - core.data_loader_simple - INFO - 加载IODB数据: C:/Users/<USER>/Desktop/Python/EWReborn/data/iodb/IODB.xlsx
2025-07-17 12:27:14 - core.data_loader_simple - INFO - 加载PIDB数据: C:/Users/<USER>/Desktop/Python/EWReborn/data/pidb/PIDB.xlsx
2025-07-17 12:27:15 - gui.allocation_widget - INFO - 数据加载完成
2025-07-17 12:27:24 - gui.allocation_widget - INFO - 开始数据验证
2025-07-17 12:27:24 - core.validator - INFO - 开始IODB数据验证
2025-07-17 12:27:24 - validation - INFO - 开始I/O点分配过程，总计 1 条电缆
2025-07-17 12:27:24 - validation - INFO - I/O点分配过程完成，耗时: 0:00:00.000193
2025-07-17 12:27:24 - validation - INFO - 分配统计:
2025-07-17 12:27:24 - validation - INFO -   总电缆数: 1
2025-07-17 12:27:24 - validation - INFO -   已处理: 0
2025-07-17 12:27:24 - validation - INFO -   成功分配: 0
2025-07-17 12:27:24 - validation - INFO -   分配失败: 0
2025-07-17 12:27:24 - validation - INFO -   警告数: 0
2025-07-17 12:27:24 - validation - INFO -   错误数: 0
2025-07-17 12:27:24 - validation - INFO -   成功率: 0.0%
2025-07-17 12:27:37 - gui.allocation_widget - INFO - 开始加载数据
2025-07-17 12:27:37 - core.data_loader_simple - INFO - 开始加载所有数据
2025-07-17 12:27:37 - core.data_loader_simple - INFO - 加载机柜配置: c:\Users\<USER>\Desktop\Python\EWReborn\data\cabinet_profiles
2025-07-17 12:27:37 - core.data_loader_simple - INFO - 找到2个机柜配置文件
2025-07-17 12:27:37 - core.data_loader_simple - INFO - 机柜配置文件加载成功
2025-07-17 12:27:37 - core.data_loader_simple - INFO - 加载典型回路: c:\Users\<USER>\Desktop\Python\EWReborn\data\wiring_typical
2025-07-17 12:27:37 - core.data_loader_simple - INFO - 找到2个典型回路文件
2025-07-17 12:27:37 - core.data_loader_simple - INFO - 典型回路文件加载成功
2025-07-17 12:27:37 - core.data_loader_simple - INFO - 数据加载完成
2025-07-17 12:27:37 - core.data_loader_simple - INFO - 加载IODB数据: C:/Users/<USER>/Desktop/Python/EWReborn/data/iodb/IODB.xlsx
2025-07-17 12:27:37 - core.data_loader_simple - INFO - 加载PIDB数据: C:/Users/<USER>/Desktop/Python/EWReborn/data/pidb/PIDB.xlsx
2025-07-17 12:27:38 - gui.allocation_widget - INFO - 数据加载完成
2025-07-17 12:28:04 - gui.allocation_widget - INFO - 开始数据验证
2025-07-17 12:28:04 - core.validator - INFO - 开始IODB数据验证
2025-07-17 12:28:04 - validation - INFO - 开始I/O点分配过程，总计 1 条电缆
2025-07-17 12:28:04 - validation - INFO - I/O点分配过程完成，耗时: 0:00:00.000227
2025-07-17 12:28:04 - validation - INFO - 分配统计:
2025-07-17 12:28:04 - validation - INFO -   总电缆数: 1
2025-07-17 12:28:04 - validation - INFO -   已处理: 0
2025-07-17 12:28:04 - validation - INFO -   成功分配: 0
2025-07-17 12:28:04 - validation - INFO -   分配失败: 0
2025-07-17 12:28:04 - validation - INFO -   警告数: 0
2025-07-17 12:28:04 - validation - INFO -   错误数: 0
2025-07-17 12:28:04 - validation - INFO -   成功率: 0.0%
2025-07-17 16:04:21 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-17 16:04:21 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-17 16:04:34 - core.logger - INFO - 日志系统初始化完成
2025-07-17 16:04:34 - core.logger - INFO - 日志级别: INFO
2025-07-17 16:04:34 - core.logger - INFO - 日志文件: c:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-17 16:04:34 - __main__ - INFO - EWReborn应用程序启动
2025-07-17 16:04:36 - gui.allocation_widget - INFO - 苹果风格增强应用成功
2025-07-17 16:04:36 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-07-17 16:04:36 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-17 16:04:36 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-17 16:04:36 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-17 16:04:36 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-17 16:04:36 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-17 16:04:36 - gui.main_window - INFO - 主窗口初始化完成
2025-07-17 16:04:36 - __main__ - INFO - 主窗口创建成功
2025-07-17 16:04:42 - __main__ - INFO - 🍎 主窗口显示完成
2025-07-17 16:05:03 - gui.allocation_widget - INFO - 开始加载数据
2025-07-17 16:05:03 - core.data_loader_simple - INFO - 开始加载所有数据
2025-07-17 16:05:03 - core.data_loader_simple - INFO - 加载机柜配置: c:\Users\<USER>\Desktop\Python\EWReborn\data\cabinet_profiles
2025-07-17 16:05:03 - core.data_loader_simple - INFO - 找到2个机柜配置文件
2025-07-17 16:05:03 - core.data_loader_simple - INFO - 机柜配置文件加载成功
2025-07-17 16:05:03 - core.data_loader_simple - INFO - 加载典型回路: c:\Users\<USER>\Desktop\Python\EWReborn\data\wiring_typical
2025-07-17 16:05:03 - core.data_loader_simple - INFO - 找到2个典型回路文件
2025-07-17 16:05:03 - core.data_loader_simple - INFO - 典型回路文件加载成功
2025-07-17 16:05:03 - core.data_loader_simple - INFO - 数据加载完成
2025-07-17 16:05:03 - core.data_loader_simple - INFO - 加载IODB数据: C:/Users/<USER>/Desktop/Python/EWReborn/data/iodb/IODB.xlsx
2025-07-17 16:05:03 - core.data_loader_simple - INFO - 加载PIDB数据: C:/Users/<USER>/Desktop/Python/EWReborn/data/pidb/PIDB.xlsx
2025-07-17 16:05:03 - gui.allocation_widget - INFO - 数据加载完成
2025-07-17 16:05:09 - gui.allocation_widget - INFO - 开始数据验证
2025-07-17 16:05:09 - core.validator - INFO - 开始IODB数据验证
2025-07-17 16:05:09 - validation - INFO - 开始I/O点分配过程，总计 1 条电缆
2025-07-17 16:05:09 - validation - INFO - I/O点分配过程完成，耗时: 0:00:00.000252
2025-07-17 16:05:09 - validation - INFO - 分配统计:
2025-07-17 16:05:09 - validation - INFO -   总电缆数: 1
2025-07-17 16:05:09 - validation - INFO -   已处理: 0
2025-07-17 16:05:09 - validation - INFO -   成功分配: 0
2025-07-17 16:05:09 - validation - INFO -   分配失败: 0
2025-07-17 16:05:09 - validation - INFO -   警告数: 0
2025-07-17 16:05:09 - validation - INFO -   错误数: 0
2025-07-17 16:05:09 - validation - INFO -   成功率: 0.0%
2025-07-17 16:15:57 - core.logger - INFO - 日志系统初始化完成
2025-07-17 16:15:57 - core.logger - INFO - 日志级别: INFO
2025-07-17 16:15:57 - core.logger - INFO - 日志文件: C:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-17 16:15:57 - __main__ - INFO - EWReborn应用程序启动
2025-07-17 16:15:58 - gui.allocation_widget - INFO - 苹果风格增强应用成功
2025-07-17 16:15:58 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-07-17 16:15:58 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-17 16:15:58 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-17 16:15:58 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-17 16:15:58 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-17 16:15:59 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-17 16:15:59 - gui.main_window - INFO - 主窗口初始化完成
2025-07-17 16:15:59 - __main__ - INFO - 主窗口创建成功
2025-07-17 16:16:04 - __main__ - INFO - 🍎 主窗口显示完成
2025-07-18 12:53:27 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-18 12:53:27 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-18 12:53:29 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-18 12:53:29 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-18 12:53:32 - core.logger - INFO - 日志系统初始化完成
2025-07-18 12:53:32 - core.logger - INFO - 日志级别: INFO
2025-07-18 12:53:32 - core.logger - INFO - 日志文件: c:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-18 12:53:32 - __main__ - INFO - EWReborn应用程序启动
2025-07-18 12:53:33 - gui.allocation_widget - INFO - 苹果风格增强应用成功
2025-07-18 12:53:33 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-07-18 12:53:33 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-18 12:53:33 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-18 12:53:33 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-18 12:53:33 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-18 12:53:34 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-18 12:53:34 - gui.main_window - INFO - 主窗口初始化完成
2025-07-18 12:53:34 - __main__ - INFO - 主窗口创建成功
2025-07-18 12:53:39 - __main__ - INFO - 🍎 主窗口显示完成
2025-07-18 12:54:54 - gui.allocation_widget - INFO - 开始加载数据
2025-07-18 12:54:54 - core.data_loader_simple - INFO - 开始加载所有数据
2025-07-18 12:54:54 - core.data_loader_simple - INFO - 加载机柜配置: c:\Users\<USER>\Desktop\Python\EWReborn\data\cabinet_profiles
2025-07-18 12:54:54 - core.data_loader_simple - INFO - 找到2个机柜配置文件
2025-07-18 12:54:54 - core.data_loader_simple - INFO - 机柜配置文件加载成功
2025-07-18 12:54:54 - core.data_loader_simple - INFO - 加载典型回路: c:\Users\<USER>\Desktop\Python\EWReborn\data\wiring_typical
2025-07-18 12:54:54 - core.data_loader_simple - INFO - 找到2个典型回路文件
2025-07-18 12:54:54 - core.data_loader_simple - INFO - 典型回路文件加载成功
2025-07-18 12:54:54 - core.data_loader_simple - INFO - 数据加载完成
2025-07-18 12:54:54 - core.data_loader_simple - INFO - 加载IODB数据: C:/Users/<USER>/Desktop/Python/EWReborn/data/iodb/IODB.xlsx
2025-07-18 12:54:54 - utils.excel_utils_simple - INFO - 读取Excel文件: C:/Users/<USER>/Desktop/Python/EWReborn/data/iodb/IODB.xlsx
2025-07-18 12:54:54 - utils.excel_utils_simple - INFO - 成功读取Excel文件，包含工作表: ['PPG']
2025-07-18 12:54:54 - utils.excel_utils_simple - INFO - IODB工作表包含 219 行数据
2025-07-18 12:54:54 - utils.excel_utils_simple - INFO - 成功处理 219 个I/O点，43 条电缆
2025-07-18 12:54:54 - core.data_loader_simple - INFO - 成功加载IODB数据: 219个I/O点，43条电缆
2025-07-18 12:54:54 - core.data_loader_simple - INFO - 加载PIDB数据: C:/Users/<USER>/Desktop/Python/EWReborn/data/pidb/PIDB.xlsx
2025-07-18 12:54:54 - gui.allocation_widget - INFO - 数据加载完成
2025-07-18 12:54:59 - gui.allocation_widget - INFO - 开始数据验证
2025-07-18 12:54:59 - core.validator - INFO - 开始IODB数据验证
2025-07-18 12:54:59 - validation - INFO - 开始I/O点分配过程，总计 1 条电缆
2025-07-18 12:54:59 - core.validator - INFO - 验证219个I/O点和43条电缆
2025-07-18 12:54:59 - core.validator - INFO - Tag唯一性验证通过
2025-07-18 12:54:59 - core.validator - INFO - Cable配对验证通过
2025-07-18 12:54:59 - core.validator - INFO - Cable属性一致性验证通过
2025-07-18 12:54:59 - core.validator - INFO - 数据完整性检查通过
2025-07-18 12:54:59 - core.validator - INFO - IODB数据验证通过
2025-07-18 12:54:59 - validation - INFO - I/O点分配过程完成，耗时: 0:00:00.002323
2025-07-18 12:54:59 - validation - INFO - 分配统计:
2025-07-18 12:54:59 - validation - INFO -   总电缆数: 1
2025-07-18 12:54:59 - validation - INFO -   已处理: 0
2025-07-18 12:54:59 - validation - INFO -   成功分配: 0
2025-07-18 12:54:59 - validation - INFO -   分配失败: 0
2025-07-18 12:54:59 - validation - INFO -   警告数: 0
2025-07-18 12:54:59 - validation - INFO -   错误数: 0
2025-07-18 12:54:59 - validation - INFO -   成功率: 0.0%
2025-07-18 12:54:59 - core.validator - INFO - IODB数据验证完成
2025-07-18 12:54:59 - gui.allocation_widget - INFO - 数据验证完成
2025-07-18 12:55:14 - gui.allocation_widget - INFO - 开始I/O点分配
2025-07-18 12:55:14 - core.data_loader_simple - INFO - 创建数据模型对象
2025-07-18 12:55:14 - core.data_loader_simple - INFO - 数据模型创建完成: 219个I/O点, 43条电缆, 2个机柜, 2个典型回路
2025-07-18 12:55:14 - core.allocator - INFO - 开始I/O点分配
2025-07-18 12:55:14 - allocation - INFO - 开始I/O点分配过程，总计 43 条电缆
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-AUX-10001 (8个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-AUX-10001 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-AUX-10001 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-AUX-10002 (8个点, DO, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-AUX-10002 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-AUX-10002 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-AUX-10003 (4个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-AUX-10003 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-AUX-10003 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-AZC-10001 (8个点, AI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-AZC-10001 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-AZC-10001 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-DZC-10001 (8个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-DZC-10001 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-DZC-10001 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-HS-10503A-1 (1个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-HS-10503A-1 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-HS-10503A-1 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-HS-10503A-2 (1个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-HS-10503A-2 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-HS-10503A-2 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-HS-10503A-3 (1个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-HS-10503A-3 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-HS-10503A-3 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-HS-10503B-1 (1个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-HS-10503B-1 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-HS-10503B-1 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-HS-10503B-2 (1个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-HS-10503B-2 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-HS-10503B-2 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-HS-10503B-3 (1个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-HS-10503B-3 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-HS-10503B-3 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-HS-10503B-RESET (1个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-HS-10503B-RESET 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-HS-10503B-RESET 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-HZ-002-1 (1个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-HZ-002-1 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-HZ-002-1 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-HZ-002-2 (1个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-HZ-002-2 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-HZ-002-2 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-HZ-002-3 (1个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-HZ-002-3 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-HZ-002-3 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-HZ-002A-1 (1个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-HZ-002A-1 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-HZ-002A-1 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-HZ-002A-2 (1个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-HZ-002A-2 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-HZ-002A-2 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-HZ-002A-3 (1个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-HZ-002A-3 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-HZ-002A-3 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-HZ-003-1 (1个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-HZ-003-1 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-HZ-003-1 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-HZ-003-2 (1个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-HZ-003-2 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-HZ-003-2 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-HZ-003-3 (1个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-HZ-003-3 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-HZ-003-3 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-HZ-004-1 (1个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-HZ-004-1 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-HZ-004-1 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-HZ-004-2 (1个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-HZ-004-2 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-HZ-004-2 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-HZ-004-3 (1个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-HZ-004-3 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-HZ-004-3 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-KC-10001 (8个点, DO, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-KC-10001 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-KC-10001 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-KC-10002 (12个点, DO, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-KC-10002 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-KC-10002 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-KC-10003 (12个点, DO, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-KC-10003 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-KC-10003 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-KC-10004 (8个点, DO, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-KC-10004 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-KC-10004 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-KC-10005 (8个点, DO, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-KC-10005 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-KC-10005 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-SZC-10001 (8个点, DO, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-SZC-10001 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-SZC-10001 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-SZC-10002 (8个点, DO, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-SZC-10002 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-SZC-10002 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-SZC-10003 (12个点, DO, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-SZC-10003 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-SZC-10003 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-SZC-10004 (8个点, DO, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-SZC-10004 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-SZC-10004 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-YIZT-10001 (8个点, AI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-YIZT-10001 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-YIZT-10001 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-YLZ-10001 (8个点, DI, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-YLZ-10001 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-YLZ-10001 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-YSPZ-10001 (8个点, DO, NIS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-YSPZ-10001 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-YSPZ-10001 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-iAZC-10001 (8个点, AI, IS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-iAZC-10001 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-iAZC-10001 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-iAZC-10002 (8个点, AI, IS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-iAZC-10002 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-iAZC-10002 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-iAZC-10003 (8个点, AI, IS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-iAZC-10003 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-iAZC-10003 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-iAZC-10004 (8个点, AI, IS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-iAZC-10004 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-iAZC-10004 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-iAZC-10005 (8个点, AI, IS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-iAZC-10005 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-iAZC-10005 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-iAZC-10006 (8个点, AI, IS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-iAZC-10006 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-iAZC-10006 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - INFO - 开始处理电缆: 1105-iAZC-10007 (8个点, AI, IS)
2025-07-18 12:55:14 - allocation - ERROR -   ✗ 1105-iAZC-10007 分配失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - allocation - ERROR - 电缆 1105-iAZC-10007 处理失败: 无法找到合适的机柜进行分配
2025-07-18 12:55:14 - core.allocator - WARNING - I/O点分配完成，但有219个点分配失败
2025-07-18 12:55:14 - allocation - INFO - I/O点分配过程完成，耗时: 0:00:00.061446
2025-07-18 12:55:14 - allocation - INFO - 分配统计:
2025-07-18 12:55:14 - allocation - INFO -   总电缆数: 43
2025-07-18 12:55:14 - allocation - INFO -   已处理: 43
2025-07-18 12:55:14 - allocation - INFO -   成功分配: 0
2025-07-18 12:55:14 - allocation - INFO -   分配失败: 43
2025-07-18 12:55:14 - allocation - INFO -   警告数: 0
2025-07-18 12:55:14 - allocation - INFO -   错误数: 43
2025-07-18 12:55:14 - allocation - INFO -   成功率: 0.0%
2025-07-18 12:55:14 - core.allocator - INFO - I/O点分配完成
2025-07-18 16:26:27 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-18 16:26:27 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-18 16:26:29 - core.logger - INFO - 日志系统初始化完成
2025-07-18 16:26:29 - core.logger - INFO - 日志级别: INFO
2025-07-18 16:26:29 - core.logger - INFO - 日志文件: c:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-18 16:26:29 - __main__ - INFO - EWReborn应用程序启动
2025-07-18 16:26:30 - gui.allocation_widget - INFO - 苹果风格增强应用成功
2025-07-18 16:26:30 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-07-18 16:26:30 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-18 16:26:30 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-18 16:26:30 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-18 16:26:30 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-18 16:26:30 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-18 16:26:30 - gui.main_window - INFO - 主窗口初始化完成
2025-07-18 16:26:30 - __main__ - INFO - 主窗口创建成功
2025-07-18 16:26:36 - __main__ - INFO - 🍎 主窗口显示完成
2025-07-18 16:26:41 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-18 16:26:41 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-18 16:26:44 - core.logger - INFO - 日志系统初始化完成
2025-07-18 16:26:44 - core.logger - INFO - 日志级别: INFO
2025-07-18 16:26:44 - core.logger - INFO - 日志文件: c:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-18 16:26:44 - __main__ - INFO - EWReborn应用程序启动
2025-07-18 16:26:45 - gui.allocation_widget - INFO - 苹果风格增强应用成功
2025-07-18 16:26:45 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-07-18 16:26:45 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-18 16:26:45 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-18 16:26:45 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-18 16:26:45 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-18 16:26:46 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-18 16:26:46 - gui.main_window - INFO - 主窗口初始化完成
2025-07-18 16:26:46 - __main__ - INFO - 主窗口创建成功
2025-07-18 16:26:51 - __main__ - INFO - 🍎 主窗口显示完成
2025-07-21 12:07:22 - core.logger - INFO - 日志系统初始化完成
2025-07-21 12:07:22 - core.logger - INFO - 日志级别: INFO
2025-07-21 12:07:22 - core.logger - INFO - 日志文件: c:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-21 12:07:22 - __main__ - INFO - EWReborn应用程序启动
2025-07-21 12:07:23 - gui.allocation_widget - INFO - 苹果风格增强应用成功
2025-07-21 12:07:23 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-07-21 12:07:23 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-21 12:07:23 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-21 12:07:23 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-21 12:07:23 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-21 12:07:24 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-21 12:07:24 - gui.main_window - INFO - 主窗口初始化完成
2025-07-21 12:07:24 - __main__ - INFO - 主窗口创建成功
2025-07-21 12:07:29 - __main__ - INFO - 🍎 主窗口显示完成
2025-07-21 12:08:54 - gui.allocation_widget - INFO - 开始加载数据
2025-07-21 12:08:54 - core.data_loader_simple - INFO - 开始加载所有数据
2025-07-21 12:08:54 - core.data_loader_simple - INFO - 加载机柜配置: c:\Users\<USER>\Desktop\Python\EWReborn\data\cabinet_profiles
2025-07-21 12:08:54 - core.data_loader_simple - INFO - 找到2个机柜配置文件
2025-07-21 12:08:54 - core.data_loader_simple - INFO - 机柜配置文件加载成功
2025-07-21 12:08:54 - core.data_loader_simple - INFO - 加载典型回路: c:\Users\<USER>\Desktop\Python\EWReborn\data\wiring_typical
2025-07-21 12:08:54 - core.data_loader_simple - INFO - 找到2个典型回路文件
2025-07-21 12:08:54 - core.data_loader_simple - INFO - 典型回路文件加载成功
2025-07-21 12:08:54 - core.data_loader_simple - INFO - 数据加载完成
2025-07-21 12:08:54 - core.data_loader_simple - INFO - 加载IODB数据: C:/Users/<USER>/Desktop/Python/EWReborn/data/iodb/IODB.xlsx
2025-07-21 12:08:54 - utils.excel_utils_simple - INFO - 读取Excel文件: C:/Users/<USER>/Desktop/Python/EWReborn/data/iodb/IODB.xlsx
2025-07-21 12:08:54 - utils.excel_utils_simple - INFO - 成功读取Excel文件，包含工作表: ['PPG']
2025-07-21 12:08:54 - utils.excel_utils_simple - INFO - IODB工作表包含 219 行数据
2025-07-21 12:08:54 - utils.excel_utils_simple - INFO - 成功处理 219 个I/O点，43 条电缆
2025-07-21 12:08:54 - core.data_loader_simple - INFO - 成功加载IODB数据: 219个I/O点，43条电缆
2025-07-21 12:08:54 - core.data_loader_simple - INFO - 加载PIDB数据: C:/Users/<USER>/Desktop/Python/EWReborn/data/pidb/PIDB.xlsx
2025-07-21 12:08:55 - gui.allocation_widget - INFO - 数据加载完成
2025-07-21 12:09:03 - gui.allocation_widget - INFO - 开始数据验证
2025-07-21 12:09:03 - core.validator - INFO - 开始IODB数据验证
2025-07-21 12:09:03 - validation - INFO - 开始I/O点分配过程，总计 1 条电缆
2025-07-21 12:09:03 - core.validator - INFO - 验证219个I/O点和43条电缆
2025-07-21 12:09:03 - core.validator - INFO - Tag唯一性验证通过
2025-07-21 12:09:03 - core.validator - INFO - Cable配对验证通过
2025-07-21 12:09:03 - core.validator - INFO - Cable属性一致性验证通过
2025-07-21 12:09:03 - core.validator - INFO - 数据完整性检查通过
2025-07-21 12:09:03 - core.validator - INFO - IODB数据验证通过
2025-07-21 12:09:03 - validation - INFO - I/O点分配过程完成，耗时: 0:00:00.003707
2025-07-21 12:09:03 - validation - INFO - 分配统计:
2025-07-21 12:09:03 - validation - INFO -   总电缆数: 1
2025-07-21 12:09:03 - validation - INFO -   已处理: 0
2025-07-21 12:09:03 - validation - INFO -   成功分配: 0
2025-07-21 12:09:03 - validation - INFO -   分配失败: 0
2025-07-21 12:09:03 - validation - INFO -   警告数: 0
2025-07-21 12:09:03 - validation - INFO -   错误数: 0
2025-07-21 12:09:03 - validation - INFO -   成功率: 0.0%
2025-07-21 12:09:03 - core.validator - INFO - IODB数据验证完成
2025-07-21 12:09:04 - gui.allocation_widget - INFO - 数据验证完成
2025-07-21 12:09:05 - gui.allocation_widget - INFO - 开始I/O点分配
2025-07-21 12:09:05 - core.data_loader_simple - INFO - 创建数据模型对象
2025-07-21 12:09:05 - core.data_loader_simple - INFO - 机柜 PPG BAR 创建成功，包含 13 个导轨
2025-07-21 12:09:05 - core.data_loader_simple - INFO - 机柜 PPG SYS 创建成功，包含 0 个导轨
2025-07-21 12:09:05 - core.data_loader_simple - INFO - 数据模型创建完成: 219个I/O点, 43条电缆, 2个机柜, 2个典型回路
2025-07-21 12:09:05 - core.allocator - INFO - 开始I/O点分配
2025-07-21 12:09:05 - allocation - INFO - 开始I/O点分配过程，总计 43 条电缆
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-AUX-10001 (8个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-MOS-001 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-009A -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-009B -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-009C -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-009D -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_99 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_100 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_101 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-AUX-10001 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-AUX-10001 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-AUX-10002 (8个点, DO, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-TZALL-10501-1 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-FZAHH-10901-1 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-FZALL-10202-1 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-PZAHH-10501-1 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-MOS-001-1 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSO-10405-2 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-BZR-10001 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_103 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-AUX-10002 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-AUX-10002 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-AUX-10003 (4个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-ACK-10001 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-RST-10001 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-TEST-10001 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_104 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-AUX-10003 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-AUX-10003 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-AZC-10001 (8个点, AI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-FZT-10303 -> 机柜:PPG BAR, 导轨:Rail_A
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-FZT-10305 -> 机柜:PPG BAR, 导轨:Rail_A
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-FZT-10202 -> 机柜:PPG BAR, 导轨:Rail_A
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_49 -> 机柜:PPG BAR, 导轨:Rail_A
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_50 -> 机柜:PPG BAR, 导轨:Rail_A
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_51 -> 机柜:PPG BAR, 导轨:Rail_A
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_52 -> 机柜:PPG BAR, 导轨:Rail_A
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_53 -> 机柜:PPG BAR, 导轨:Rail_A
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-AZC-10001 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-AZC-10001 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-DZC-10001 (8个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSC-10403 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSO-10405 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_54 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_55 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_56 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_57 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_58 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_59 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-DZC-10001 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-DZC-10001 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-HS-10503A-1 (1个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HS-10503A-1 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-HS-10503A-1 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-HS-10503A-1 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-HS-10503A-2 (1个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HS-10503A-2 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-HS-10503A-2 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-HS-10503A-2 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-HS-10503A-3 (1个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HS-10503A-3 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-HS-10503A-3 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-HS-10503A-3 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-HS-10503B-1 (1个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HS-10503B-1 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-HS-10503B-1 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-HS-10503B-1 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-HS-10503B-2 (1个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HS-10503B-2 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-HS-10503B-2 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-HS-10503B-2 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-HS-10503B-3 (1个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HS-10503B-3 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-HS-10503B-3 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-HS-10503B-3 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-HS-10503B-RESET (1个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HS-10503B-RESET -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-HS-10503B-RESET -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-HS-10503B-RESET 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-HZ-002-1 (1个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-002-1 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-HZ-002-1 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-HZ-002-1 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-HZ-002-2 (1个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-002-2 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-HZ-002-2 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-HZ-002-2 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-HZ-002-3 (1个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-002-3 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-HZ-002-3 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-HZ-002-3 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-HZ-002A-1 (1个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-002A-1 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-HZ-002A-1 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-HZ-002A-1 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-HZ-002A-2 (1个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-002A-2 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-HZ-002A-2 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-HZ-002A-2 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-HZ-002A-3 (1个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-002A-3 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-HZ-002A-3 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-HZ-002A-3 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-HZ-003-1 (1个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-003-1 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-HZ-003-1 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-HZ-003-1 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-HZ-003-2 (1个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-003-2 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-HZ-003-2 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-HZ-003-2 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-HZ-003-3 (1个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-003-3 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-HZ-003-3 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-HZ-003-3 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-HZ-004-1 (1个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-004-1 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-HZ-004-1 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-HZ-004-1 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-HZ-004-2 (1个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-004-2 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-HZ-004-2 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-HZ-004-2 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-HZ-004-3 (1个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-004-3 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-HZ-004-3 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-HZ-004-3 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-KC-10001 (8个点, DO, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HY-10503-1 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-TIC-10802-1 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-TIC-11001-1 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-PIC-10202-1 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HY-10502B-1 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-PIC-11902-1 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-FIC-10201-1 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_76 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-KC-10001 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-KC-10001 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-KC-10002 (12个点, DO, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-FIC-10302-1 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-FIC-10304-1 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-FIC-10306-1 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-FIC-10402-1 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-FIC-10801-1 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-FIC-11001-1 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-FIC-11003-1 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-FIC-11101-1 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-FIC-11202-1 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-FIC-11203-1 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_81 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_82 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-KC-10002 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-KC-10002 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-KC-10003 (12个点, DO, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XV-11001-1 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XV-11002-1 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XV-11101-1 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XV-11201-1 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XV-11202-1 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XV-11203-1 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XV-11204-1 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XV-11206-1 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XV-10407-1 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_83 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_84 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_85 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-KC-10003 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-KC-10003 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-KC-10004 (8个点, DO, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-UZ-200-HOLD-1 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-UZ-200-HOLD-2 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-UZ-200-HOLD-3 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-009A-1 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-009B-1 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-009C-1 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-HZ-009D-1 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_86 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-KC-10004 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-KC-10004 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-KC-10005 (8个点, DO, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-TDZAHH-10701 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-TDZAHH-10702 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-LZAHH-10501 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-LZALL-10502 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_91 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_92 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_93 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_94 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-KC-10005 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-KC-10005 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-SZC-10001 (8个点, DO, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSOV-10503 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSOV-10601B -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSOV-10601C -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSOV-10603 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSOV-10803 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_37 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_38 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_39 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-SZC-10001 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-SZC-10001 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-SZC-10002 (8个点, DO, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSOV-10601A -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSOV-10703 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_40 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_41 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_42 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_43 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_44 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_45 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-SZC-10002 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-SZC-10002 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-SZC-10003 (12个点, DO, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSOV-10302 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSOV-10402 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSOV-10403 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSOV-10404 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSOV-10405 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSOV-10701 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSOV-10502 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_32 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_33 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_34 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_35 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_36 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-SZC-10003 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-SZC-10003 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-SZC-10004 (8个点, DO, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSOV-10303 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSOV-10305 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSOV-10306 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSOV-10308 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-XZSOV-10202 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_46 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_47 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_48 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-SZC-10004 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-SZC-10004 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-YIZT-10001 (8个点, AI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-YIZT-P1002A -> 机柜:PPG BAR, 导轨:Rail_A
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-YIZT-P1002B -> 机柜:PPG BAR, 导轨:Rail_A
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_70 -> 机柜:PPG BAR, 导轨:Rail_A
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_71 -> 机柜:PPG BAR, 导轨:Rail_A
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_72 -> 机柜:PPG BAR, 导轨:Rail_A
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_73 -> 机柜:PPG BAR, 导轨:Rail_A
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_74 -> 机柜:PPG BAR, 导轨:Rail_A
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_75 -> 机柜:PPG BAR, 导轨:Rail_A
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-YIZT-10001 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-YIZT-10001 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-YLZ-10001 (8个点, DI, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-YLZ-P1002A -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-YLZ-P1002B -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_60 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_61 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_62 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_63 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_64 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_65 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-YLZ-10001 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-YLZ-10001 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-YSPZ-10001 (8个点, DO, NIS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-YSPZ-P1002A -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-YSPZ-P1002B -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-YSPZ-P1022A -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-YSPZ-P1022B -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_66 -> 机柜:PPG BAR, 导轨:Rail_C2
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_67 -> 机柜:PPG BAR, 导轨:Rail_XX
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_68 -> 机柜:PPG BAR, 导轨:Rail_ISL
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_69 -> 机柜:PPG BAR, 导轨:Rail_B2
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-YSPZ-10001 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-YSPZ-10001 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-iAZC-10001 (8个点, AI, IS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-PZT-10307 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-PZT-10308 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_1 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_2 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_3 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_4 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_5 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_6 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-iAZC-10001 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-iAZC-10001 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-iAZC-10002 (8个点, AI, IS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-PDZT-10305 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-PDZT-10306 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-PDZT-10203 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-PZT-10407C -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-PZT-10311C -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-PZT-10312 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_7 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_8 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-iAZC-10002 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-iAZC-10002 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-iAZC-10003 (8个点, AI, IS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-TZT-10702 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-TZT-10501B -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-FZT-10901C -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_9 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_10 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_11 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_12 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_13 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-iAZC-10003 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-iAZC-10003 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-iAZC-10004 (8个点, AI, IS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-PZT-10501A -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-FZT-10901A -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-LZT-10501 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-TZT-10703 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-TZT-10704 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-LZT-10502 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_14 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_15 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-iAZC-10004 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-iAZC-10004 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-iAZC-10005 (8个点, AI, IS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-FZT-10901B -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-PZT-10501B -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-TZT-10501A -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_16 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_17 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_18 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_19 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_20 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-iAZC-10005 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-iAZC-10005 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-iAZC-10006 (8个点, AI, IS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-PZT-10407A -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-PZT-10311A -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-PDZT-10406 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_21 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_22 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_23 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_24 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_25 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-iAZC-10006 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-iAZC-10006 处理成功
2025-07-21 12:09:05 - allocation - INFO - 开始处理电缆: 1105-iAZC-10007 (8个点, AI, IS)
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-PZT-10407B -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 1105-PZT-10311B -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_26 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_27 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_28 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_29 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_30 -> 机柜:PPG BAR, 导轨:Rail_B1
2025-07-21 12:09:05 - allocation - INFO -   ✓ SPARE_31 -> 机柜:PPG BAR, 导轨:Rail_C1
2025-07-21 12:09:05 - allocation - INFO -   ✓ 电缆 1105-iAZC-10007 -> 机柜:PPG BAR, 导轨:导轨分配, 机架:机架分配
2025-07-21 12:09:05 - allocation - INFO - 电缆 1105-iAZC-10007 处理成功
2025-07-21 12:09:05 - core.allocator - INFO - I/O点分配全部成功完成
2025-07-21 12:09:05 - allocation - INFO - I/O点分配过程完成，耗时: 0:00:00.211149
2025-07-21 12:09:05 - allocation - INFO - 分配统计:
2025-07-21 12:09:05 - allocation - INFO -   总电缆数: 43
2025-07-21 12:09:05 - allocation - INFO -   已处理: 43
2025-07-21 12:09:05 - allocation - INFO -   成功分配: 262
2025-07-21 12:09:05 - allocation - INFO -   分配失败: 0
2025-07-21 12:09:05 - allocation - INFO -   警告数: 0
2025-07-21 12:09:05 - allocation - INFO -   错误数: 0
2025-07-21 12:09:05 - allocation - INFO -   成功率: 100.0%
2025-07-21 12:09:05 - core.allocator - INFO - I/O点分配完成
2025-07-21 12:09:06 - gui.allocation_widget - INFO - I/O点分配完成
2025-07-21 12:09:35 - gui.allocation_widget - INFO - 导出分配结果
2025-07-21 12:09:44 - gui.allocation_widget - INFO - 分配结果导出完成: C:/Users/<USER>/Desktop/Python/EWReborn/output/allocation_results_20250721_120935.xlsx
2025-07-21 13:44:30 - gui.xml_editor_widget - INFO - 打开典型回路文件
2025-07-21 13:44:49 - gui.xml_editor_widget - INFO - 启动独立XML编辑器
2025-07-21 13:44:49 - gui.xml_editor_widget - INFO - 独立XML编辑器启动成功
2025-07-21 14:07:58 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-21 14:07:58 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-21 14:18:50 - core.logger - INFO - 日志系统初始化完成
2025-07-21 14:18:50 - core.logger - INFO - 日志级别: INFO
2025-07-21 14:18:50 - core.logger - INFO - 日志文件: C:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-21 14:18:50 - __main__ - INFO - EWReborn应用程序启动
2025-07-21 14:18:51 - gui.allocation_widget - INFO - 苹果风格增强应用成功
2025-07-21 14:18:51 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-07-21 14:18:51 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-21 14:18:51 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-21 14:18:51 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-21 14:18:51 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-21 14:18:52 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-21 14:18:52 - gui.main_window - INFO - 主窗口初始化完成
2025-07-21 14:18:52 - __main__ - INFO - 主窗口创建成功
2025-07-21 14:18:57 - __main__ - INFO - 🍎 主窗口显示完成
2025-07-21 14:26:14 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-21 14:26:14 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-21 14:28:36 - core.logger - INFO - 日志系统初始化完成
2025-07-21 14:28:36 - core.logger - INFO - 日志级别: INFO
2025-07-21 14:28:36 - core.logger - INFO - 日志文件: c:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-21 14:28:36 - __main__ - INFO - EWReborn应用程序启动
2025-07-21 14:28:37 - gui.allocation_widget - INFO - 苹果风格增强应用成功
2025-07-21 14:28:37 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-07-21 14:28:37 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-21 14:28:37 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-21 14:28:37 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-21 14:28:37 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-21 14:28:38 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-21 14:28:38 - gui.main_window - INFO - 主窗口初始化完成
2025-07-21 14:28:38 - __main__ - INFO - 主窗口创建成功
2025-07-21 14:28:43 - __main__ - INFO - 🍎 主窗口显示完成
2025-07-21 14:29:23 - gui.xml_editor_widget - INFO - 打开机柜配置文件
2025-07-21 14:29:40 - gui.xml_editor_widget - INFO - 打开典型回路文件
2025-07-21 14:30:21 - gui.xml_editor_widget - INFO - 启动独立XML编辑器
2025-07-21 14:30:21 - gui.xml_editor_widget - INFO - 独立XML编辑器启动成功
2025-07-23 09:18:28 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-23 09:18:31 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-24 09:57:07 - core.logger - INFO - 日志系统初始化完成
2025-07-24 09:57:07 - core.logger - INFO - 日志级别: INFO
2025-07-24 09:57:07 - core.logger - INFO - 日志文件: c:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-24 09:57:07 - __main__ - INFO - EWReborn应用程序启动
2025-07-24 09:57:09 - gui.allocation_widget - INFO - 苹果风格增强应用成功
2025-07-24 09:57:09 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-07-24 09:57:09 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-24 09:57:09 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-24 09:57:09 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-24 09:57:09 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-24 09:57:10 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-24 09:57:10 - gui.main_window - INFO - 主窗口初始化完成
2025-07-24 09:57:10 - __main__ - INFO - 主窗口创建成功
2025-07-24 09:57:16 - __main__ - INFO - 主窗口显示完成
2025-07-24 10:44:10 - gui.xml_editor_widget - INFO - 启动独立XML编辑器
2025-07-24 10:44:10 - gui.xml_editor_widget - INFO - 独立XML编辑器启动成功
2025-07-24 15:03:09 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-24 15:03:14 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-24 15:03:16 - core.logger - INFO - 日志系统初始化完成
2025-07-24 15:03:16 - core.logger - INFO - 日志级别: INFO
2025-07-24 15:03:16 - core.logger - INFO - 日志文件: c:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-24 15:03:16 - __main__ - INFO - EWReborn应用程序启动
2025-07-24 15:03:17 - gui.allocation_widget - INFO - 苹果风格增强应用成功
2025-07-24 15:03:17 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-07-24 15:03:17 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-24 15:03:17 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-24 15:03:17 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-24 15:03:17 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-24 15:03:18 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-24 15:03:18 - gui.main_window - INFO - 主窗口初始化完成
2025-07-24 15:03:18 - __main__ - INFO - 主窗口创建成功
2025-07-24 15:03:23 - __main__ - INFO - 主窗口显示完成
2025-07-24 15:14:45 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-24 15:14:46 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-24 15:22:56 - core.logger - INFO - 日志系统初始化完成
2025-07-24 15:22:56 - core.logger - INFO - 日志级别: INFO
2025-07-24 15:22:56 - core.logger - INFO - 日志文件: c:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-24 15:22:56 - __main__ - INFO - EWReborn应用程序启动
2025-07-24 15:22:57 - gui.allocation_widget - INFO - 苹果风格增强应用成功
2025-07-24 15:22:57 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-07-24 15:22:57 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-24 15:22:57 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-24 15:22:57 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-24 15:22:57 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-24 15:22:57 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-24 15:22:58 - gui.main_window - INFO - 主窗口初始化完成
2025-07-24 15:22:58 - __main__ - INFO - 主窗口创建成功
2025-07-24 15:23:03 - __main__ - INFO - 主窗口显示完成
2025-07-24 15:23:12 - gui.allocation_widget - INFO - 打开命名规则配置对话框
2025-07-24 15:23:12 - core.naming_engine - INFO - 添加命名规则: 默认安全栅命名 (barrier)
2025-07-24 15:23:12 - core.naming_engine - INFO - 添加命名规则: 默认继电器命名 (relay)
2025-07-24 15:23:12 - core.naming_engine - INFO - 添加命名规则: 默认隔离器命名 (isolator)
2025-07-24 15:23:12 - core.naming_engine - INFO - 添加命名规则: 默认防雷栅命名 (surge_protector)
2025-07-24 15:23:12 - core.naming_engine - INFO - 添加命名规则: 默认端子排命名 (terminal_block)
2025-07-24 15:23:12 - core.naming_engine - INFO - 添加命名规则: 默认TR端子排命名 (tr_terminal)
2025-07-24 15:23:12 - gui.allocation_widget - ERROR - 打开命名规则配置对话框失败: 'text'
2025-07-24 15:23:48 - gui.allocation_widget - INFO - 打开命名规则配置对话框
2025-07-24 15:23:48 - core.naming_engine - INFO - 添加命名规则: 默认安全栅命名 (barrier)
2025-07-24 15:23:48 - core.naming_engine - INFO - 添加命名规则: 默认继电器命名 (relay)
2025-07-24 15:23:48 - core.naming_engine - INFO - 添加命名规则: 默认隔离器命名 (isolator)
2025-07-24 15:23:48 - core.naming_engine - INFO - 添加命名规则: 默认防雷栅命名 (surge_protector)
2025-07-24 15:23:48 - core.naming_engine - INFO - 添加命名规则: 默认端子排命名 (terminal_block)
2025-07-24 15:23:48 - core.naming_engine - INFO - 添加命名规则: 默认TR端子排命名 (tr_terminal)
2025-07-24 15:23:48 - gui.allocation_widget - ERROR - 打开命名规则配置对话框失败: 'text'
2025-07-24 15:32:49 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-24 15:32:49 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-24 15:37:48 - core.logger - INFO - 日志系统初始化完成
2025-07-24 15:37:48 - core.logger - INFO - 日志级别: INFO
2025-07-24 15:37:48 - core.logger - INFO - 日志文件: c:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-24 15:37:48 - __main__ - INFO - EWReborn应用程序启动
2025-07-24 15:37:50 - gui.allocation_widget - INFO - 苹果风格增强应用成功
2025-07-24 15:37:50 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-07-24 15:37:50 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-24 15:37:50 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-24 15:37:50 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-24 15:37:50 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-24 15:37:50 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-24 15:37:50 - gui.main_window - INFO - 主窗口初始化完成
2025-07-24 15:37:50 - __main__ - INFO - 主窗口创建成功
2025-07-24 15:37:56 - __main__ - INFO - 主窗口显示完成
2025-07-24 15:38:00 - gui.allocation_widget - INFO - 打开命名规则配置对话框
2025-07-24 16:06:40 - gui.allocation_widget - ERROR - 打开命名规则配置对话框失败: 'SimpleNamingDialog' object has no attribute 'Accepted'
2025-07-24 16:06:45 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-24 16:06:45 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-25 13:02:37 - core.logger - INFO - 日志系统初始化完成
2025-07-25 13:02:37 - core.logger - INFO - 日志级别: INFO
2025-07-25 13:02:37 - core.logger - INFO - 日志文件: c:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-25 13:02:37 - __main__ - INFO - EWReborn应用程序启动
2025-07-25 13:02:39 - gui.allocation_widget - INFO - 苹果风格增强应用成功
2025-07-25 13:02:39 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-07-25 13:02:39 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-25 13:02:39 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-25 13:02:39 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-25 13:02:39 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-25 13:02:41 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-25 13:02:41 - gui.main_window - INFO - 主窗口初始化完成
2025-07-25 13:02:41 - __main__ - INFO - 主窗口创建成功
2025-07-25 13:02:46 - __main__ - INFO - 主窗口显示完成
2025-07-25 13:27:18 - gui.allocation_widget - INFO - 打开命名规则配置对话框
2025-07-25 14:01:17 - gui.allocation_widget - ERROR - 打开命名规则配置对话框失败: 'FlexibleNamingDialog' object has no attribute 'Accepted'
2025-07-25 14:01:20 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-25 14:01:20 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-25 14:01:22 - core.logger - INFO - 日志系统初始化完成
2025-07-25 14:01:22 - core.logger - INFO - 日志级别: INFO
2025-07-25 14:01:22 - core.logger - INFO - 日志文件: c:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-25 14:01:22 - __main__ - INFO - EWReborn应用程序启动
2025-07-25 14:01:24 - gui.allocation_widget - INFO - 苹果风格增强应用成功
2025-07-25 14:01:24 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-07-25 14:01:24 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-25 14:01:24 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-25 14:01:24 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-25 14:01:24 - gui.config_widget - INFO - 配置界面初始化完成
2025-07-25 14:01:25 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-25 14:01:25 - gui.main_window - INFO - 主窗口初始化完成
2025-07-25 14:01:25 - __main__ - INFO - 主窗口创建成功
2025-07-25 14:01:30 - __main__ - INFO - 主窗口显示完成
2025-07-25 14:18:30 - gui.allocation_widget - INFO - 打开命名规则配置对话框
2025-07-25 14:19:30 - gui.allocation_widget - ERROR - 保存命名配置失败: name 'os' is not defined
2025-07-25 14:19:30 - gui.allocation_widget - ERROR - 打开命名规则配置对话框失败: 'FlexibleNamingDialog' object has no attribute 'Accepted'
2025-07-25 14:27:55 - gui.xml_editor_widget - INFO - 启动独立XML编辑器
2025-07-25 14:27:55 - gui.xml_editor_widget - INFO - 独立XML编辑器启动成功
2025-07-25 16:10:01 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-25 16:10:01 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-25 16:39:46 - core.logger - INFO - 日志系统初始化完成
2025-07-25 16:39:46 - core.logger - INFO - 日志级别: INFO
2025-07-25 16:39:46 - core.logger - INFO - 日志文件: c:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-25 16:39:46 - __main__ - INFO - EWReborn应用程序启动 - 使用传统PySide6界面
2025-07-25 16:39:46 - __main__ - WARNING - SiliconUI框架未安装，建议运行install_siliconui.py获得更好的界面体验
2025-07-25 16:39:47 - gui.allocation_widget - INFO - 苹果风格增强应用成功
2025-07-25 16:39:47 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-07-25 16:39:47 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-25 16:39:47 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-25 16:39:47 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-25 16:39:47 - gui.main_window - ERROR - 创建配置选项卡失败: name 'QTableWidgetItem' is not defined
2025-07-25 16:39:47 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-25 16:39:47 - gui.main_window - INFO - 主窗口初始化完成
2025-07-25 16:39:47 - __main__ - INFO - 主窗口创建成功
2025-07-25 16:39:53 - __main__ - INFO - 主窗口显示完成
2025-07-25 16:40:00 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-25 16:40:00 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-25 16:48:49 - core.logger - INFO - 日志系统初始化完成
2025-07-25 16:48:49 - core.logger - INFO - 日志级别: INFO
2025-07-25 16:48:49 - core.logger - INFO - 日志文件: C:\Users\<USER>\Desktop\Python\EWReborn\logs\ewreborn.log
2025-07-25 16:48:49 - __main__ - INFO - EWReborn应用程序启动 - 使用SiliconUI现代化界面
2025-07-25 16:52:49,224 - __main__ - INFO - EWReborn应用程序启动 - 使用SiliconUI现代化界面
2025-07-25 16:53:47,458 - __main__ - INFO - EWReborn应用程序启动 - 使用SiliconUI现代化界面
2025-07-25 16:55:40,317 - __main__ - INFO - EWReborn应用程序启动 - 使用SiliconUI现代化界面
2025-07-25 16:57:25,995 - __main__ - INFO - EWReborn应用程序启动 - 使用SiliconUI现代化界面
2025-07-25 16:57:26,020 - gui.silicon_splash - INFO - SiliconUI启动画面初始化完成
2025-07-25 16:57:26,371 - gui.silicon_main_window - INFO - SiliconUI主题应用成功
2025-07-25 16:57:26,371 - gui.silicon_main_window - INFO - SiliconUI主窗口初始化完成
2025-07-25 16:57:26,372 - __main__ - INFO - 主窗口创建成功
2025-07-25 16:57:29,406 - gui.silicon_splash - INFO - 应用程序加载完成
2025-07-25 16:57:30,172 - __main__ - INFO - 主窗口显示完成
2025-07-25 16:58:51,081 - __main__ - INFO - EWReborn应用程序启动 - 使用SiliconUI现代化界面
2025-07-25 16:58:51,100 - gui.silicon_splash - INFO - SiliconUI启动画面初始化完成
2025-07-25 16:58:51,849 - gui.silicon_main_window - INFO - SiliconUI主题应用成功
2025-07-25 16:58:51,849 - gui.silicon_main_window - INFO - SiliconUI主窗口初始化完成
2025-07-25 16:58:51,850 - __main__ - INFO - 主窗口创建成功
2025-07-25 16:58:54,932 - gui.silicon_splash - INFO - 应用程序加载完成
2025-07-25 16:58:55,978 - __main__ - INFO - 主窗口显示完成
2025-07-25 17:00:35,131 - gui.silicon_main_window - INFO - 主题已切换到: dark
2025-07-25 17:00:39,012 - gui.silicon_main_window - INFO - 用户确认退出应用程序
2025-07-25 17:00:39,151 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-25 17:00:41,600 - gui.silicon_main_window - INFO - 用户确认退出应用程序
2025-07-25 17:00:41,767 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-25 17:06:25,400 - __main__ - INFO - EWReborn应用程序启动
2025-07-25 17:06:26,604 - gui.allocation_widget - INFO - 苹果风格增强应用成功
2025-07-25 17:06:26,650 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-07-25 17:06:26,651 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-25 17:06:26,681 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-25 17:06:26,695 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-25 17:06:26,715 - gui.main_window - ERROR - 创建配置选项卡失败: name 'QTableWidgetItem' is not defined
2025-07-25 17:06:26,844 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-25 17:06:26,846 - gui.main_window - INFO - 主窗口初始化完成
2025-07-25 17:06:26,851 - __main__ - INFO - 主窗口创建成功
2025-07-25 17:06:32,734 - __main__ - INFO - 主窗口显示完成
2025-07-25 17:06:51,160 - gui.main_window - INFO - 用户确认退出应用程序
2025-07-25 17:06:51,448 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-25 17:07:13,619 - __main__ - INFO - EWReborn应用程序启动
2025-07-25 17:07:14,754 - gui.allocation_widget - INFO - 苹果风格增强应用成功
2025-07-25 17:07:14,758 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-07-25 17:07:14,758 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-07-25 17:07:14,760 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-07-25 17:07:14,760 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-07-25 17:07:14,776 - gui.main_window - ERROR - 创建配置选项卡失败: name 'QTableWidgetItem' is not defined
2025-07-25 17:07:14,980 - gui.main_window - INFO - 苹果风格样式应用成功
2025-07-25 17:07:14,980 - gui.main_window - INFO - 主窗口初始化完成
2025-07-25 17:07:14,981 - __main__ - INFO - 主窗口创建成功
2025-07-25 17:07:20,883 - __main__ - INFO - 主窗口显示完成
2025-08-05 10:40:28,844 - __main__ - INFO - EWReborn应用程序启动
2025-08-05 10:40:30,011 - gui.material_theme - INFO - Material主题管理器初始化完成
2025-08-05 10:40:33,055 - gui.allocation_widget - INFO - Material Design增强应用成功
2025-08-05 10:40:33,057 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-08-05 10:40:33,058 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-08-05 10:40:33,061 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-08-05 10:40:33,062 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-08-05 10:40:33,078 - gui.config_widget - INFO - 配置界面初始化完成
2025-08-05 10:40:34,879 - gui.main_window - INFO - 主题已更改为: dark_teal.xml
2025-08-05 10:40:34,879 - gui.material_theme - INFO - 主题应用成功: dark_teal.xml
2025-08-05 10:40:34,880 - gui.main_window - INFO - Material主题应用成功: dark_teal.xml
2025-08-05 10:40:34,882 - gui.main_window - INFO - 主窗口初始化完成
2025-08-05 10:40:34,887 - __main__ - INFO - 主窗口创建成功
2025-08-05 10:40:41,114 - __main__ - INFO - 主窗口显示完成
2025-08-05 10:40:57,269 - gui.main_window - INFO - 主题已更改为: light_cyan_500.xml
2025-08-05 10:40:57,269 - gui.material_theme - INFO - 主题应用成功: light_cyan_500.xml
2025-08-05 10:40:57,270 - gui.material_theme - INFO - 主题配置已更新: light_cyan_500.xml
2025-08-05 10:40:57,271 - gui.main_window - INFO - 主题切换成功: light_cyan_500.xml
2025-08-05 10:41:06,438 - gui.main_window - INFO - 主题已更改为: light_teal_500.xml
2025-08-05 10:41:06,439 - gui.material_theme - INFO - 主题应用成功: light_teal_500.xml
2025-08-05 10:41:06,439 - gui.material_theme - INFO - 主题配置已更新: light_teal_500.xml
2025-08-05 10:41:06,440 - gui.main_window - INFO - 主题切换成功: light_teal_500.xml
2025-08-05 10:58:47,229 - gui.main_window - INFO - 主题已更改为: dark_blue.xml
2025-08-05 10:58:47,230 - gui.material_theme - INFO - 主题应用成功: dark_blue.xml
2025-08-05 10:58:47,230 - gui.material_theme - INFO - 主题配置已更新: dark_blue.xml
2025-08-05 10:58:47,231 - gui.main_window - INFO - 主题切换成功: dark_blue.xml
2025-08-05 10:59:02,336 - gui.main_window - INFO - 主题已更改为: dark_cyan.xml
2025-08-05 10:59:02,336 - gui.material_theme - INFO - 主题应用成功: dark_cyan.xml
2025-08-05 10:59:02,337 - gui.material_theme - INFO - 主题配置已更新: dark_cyan.xml
2025-08-05 10:59:02,337 - gui.main_window - INFO - 主题切换成功: dark_cyan.xml
2025-08-05 10:59:26,628 - gui.allocation_widget - INFO - 开始加载数据
2025-08-05 10:59:26,637 - gui.allocation_widget - ERROR - 加载数据失败: No module named 'core.data_loader_simple'
2025-08-05 10:59:29,167 - gui.allocation_widget - INFO - 开始加载数据
2025-08-05 10:59:29,168 - gui.allocation_widget - ERROR - 加载数据失败: No module named 'core.data_loader_simple'
2025-08-05 10:59:36,301 - gui.allocation_widget - INFO - 开始加载数据
2025-08-05 10:59:36,302 - gui.allocation_widget - ERROR - 加载数据失败: No module named 'core.data_loader_simple'
2025-08-05 11:42:34,942 - __main__ - INFO - 测试日志消息
2025-08-05 11:43:27,401 - __main__ - INFO - 测试日志消息
2025-08-05 11:43:53,430 - __main__ - INFO - 测试日志消息
2025-08-05 11:43:53,432 - core.data_loader_simple - INFO - 开始加载所有数据
2025-08-05 11:43:53,435 - core.data_loader_simple - INFO - 加载机柜配置: C:\Users\<USER>\Desktop\Python\EWReborn\data\cabinet_profiles
2025-08-05 11:43:53,437 - core.data_loader_simple - INFO - 找到0个机柜配置文件
2025-08-05 11:43:53,437 - core.data_loader_simple - INFO - 机柜配置文件加载成功
2025-08-05 11:43:53,438 - core.data_loader_simple - INFO - 加载典型回路: C:\Users\<USER>\Desktop\Python\EWReborn\data\wiring_typical
2025-08-05 11:43:53,457 - core.data_loader_simple - INFO - 找到2个典型回路文件
2025-08-05 11:43:53,458 - core.data_loader_simple - INFO - 典型回路文件加载成功
2025-08-05 11:43:53,458 - core.data_loader_simple - INFO - 数据加载完成
2025-08-05 11:44:02,352 - __main__ - INFO - 系统测试开始
2025-08-05 11:44:03,742 - core.data_loader_simple - INFO - 开始加载所有数据
2025-08-05 11:44:03,742 - core.data_loader_simple - INFO - 加载机柜配置: C:\Users\<USER>\Desktop\Python\EWReborn\data\cabinet_profiles
2025-08-05 11:44:03,746 - core.data_loader_simple - INFO - 找到0个机柜配置文件
2025-08-05 11:44:03,747 - core.data_loader_simple - INFO - 机柜配置文件加载成功
2025-08-05 11:44:03,750 - core.data_loader_simple - INFO - 加载典型回路: C:\Users\<USER>\Desktop\Python\EWReborn\data\wiring_typical
2025-08-05 11:44:03,753 - core.data_loader_simple - INFO - 找到2个典型回路文件
2025-08-05 11:44:03,754 - core.data_loader_simple - INFO - 典型回路文件加载成功
2025-08-05 11:44:03,757 - core.data_loader_simple - INFO - 数据加载完成
2025-08-05 11:44:03,758 - core.data_loader_simple - INFO - 创建数据模型对象
2025-08-05 11:44:03,762 - core.data_loader_simple - INFO - 数据模型创建完成: 0个I/O点, 0条电缆, 0个机柜, 2个典型回路
2025-08-05 11:44:03,807 - core.validator - INFO - 开始IODB数据验证
2025-08-05 11:44:03,807 - core.validator - INFO - 验证0个I/O点和0条电缆
2025-08-05 11:44:03,808 - core.validator - INFO - Tag唯一性验证通过
2025-08-05 11:44:03,809 - core.validator - INFO - Cable配对验证通过
2025-08-05 11:44:03,809 - core.validator - INFO - Cable属性一致性验证通过
2025-08-05 11:44:03,810 - core.validator - INFO - IODB数据验证通过
2025-08-05 11:44:04,241 - gui.material_theme - INFO - Material主题管理器初始化完成
2025-08-05 11:44:04,351 - gui.allocation_widget - INFO - Material Design增强应用成功
2025-08-05 11:44:04,352 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-08-05 11:44:04,352 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-08-05 11:44:04,355 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-08-05 11:44:04,355 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-08-05 11:44:04,364 - gui.config_widget - INFO - 配置界面初始化完成
2025-08-05 11:44:06,197 - gui.main_window - INFO - 主题已更改为: dark_teal.xml
2025-08-05 11:44:06,198 - gui.material_theme - INFO - 主题应用成功: dark_teal.xml
2025-08-05 11:44:06,198 - gui.main_window - INFO - Material主题应用成功: dark_teal.xml
2025-08-05 11:44:06,199 - gui.main_window - INFO - 主窗口初始化完成
2025-08-05 11:44:06,239 - gui.allocation_widget - INFO - Material Design增强应用成功
2025-08-05 11:44:06,241 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-08-05 11:44:06,242 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-08-05 11:44:06,271 - gui.progress_widget - INFO - 进度组件苹果风格增强应用成功
2025-08-05 11:44:06,272 - gui.progress_widget - INFO - 进度显示组件初始化完成
2025-08-05 11:44:29,934 - __main__ - INFO - 系统测试开始
2025-08-05 11:44:31,448 - core.data_loader_simple - INFO - 开始加载所有数据
2025-08-05 11:44:31,449 - core.data_loader_simple - INFO - 加载机柜配置: C:\Users\<USER>\Desktop\Python\EWReborn\data\cabinet_profiles
2025-08-05 11:44:31,451 - core.data_loader_simple - INFO - 找到0个机柜配置文件
2025-08-05 11:44:31,451 - core.data_loader_simple - INFO - 机柜配置文件加载成功
2025-08-05 11:44:31,451 - core.data_loader_simple - INFO - 加载典型回路: C:\Users\<USER>\Desktop\Python\EWReborn\data\wiring_typical
2025-08-05 11:44:31,452 - core.data_loader_simple - INFO - 找到2个典型回路文件
2025-08-05 11:44:31,452 - core.data_loader_simple - INFO - 典型回路文件加载成功
2025-08-05 11:44:31,452 - core.data_loader_simple - INFO - 数据加载完成
2025-08-05 11:44:31,453 - core.data_loader_simple - INFO - 创建数据模型对象
2025-08-05 11:44:31,453 - core.data_loader_simple - INFO - 数据模型创建完成: 0个I/O点, 0条电缆, 0个机柜, 2个典型回路
2025-08-05 11:44:31,457 - core.validator - INFO - 开始IODB数据验证
2025-08-05 11:44:31,458 - core.validator - INFO - 验证0个I/O点和0条电缆
2025-08-05 11:44:31,458 - core.validator - INFO - Tag唯一性验证通过
2025-08-05 11:44:31,459 - core.validator - INFO - Cable配对验证通过
2025-08-05 11:44:31,460 - core.validator - INFO - Cable属性一致性验证通过
2025-08-05 11:44:31,461 - core.validator - INFO - IODB数据验证通过
2025-08-05 11:44:31,479 - core.allocator - INFO - 开始I/O点分配，电缆数: 0, 机柜数: 0
2025-08-05 11:44:31,484 - core.allocator - INFO - 总计需要分配 0 个I/O点
2025-08-05 11:44:31,485 - core.allocator - INFO - I/O点分配完成，成功率: 0.0%
2025-08-05 11:44:31,486 - core.allocator - INFO - 成功分配: 0, 失败: 0
2025-08-05 11:44:31,958 - gui.material_theme - INFO - Material主题管理器初始化完成
2025-08-05 11:44:32,248 - gui.allocation_widget - INFO - Material Design增强应用成功
2025-08-05 11:44:32,250 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-08-05 11:44:32,251 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-08-05 11:44:32,295 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-08-05 11:44:32,300 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-08-05 11:44:32,427 - gui.config_widget - INFO - 配置界面初始化完成
2025-08-05 11:44:32,840 - gui.main_window - INFO - 主题已更改为: dark_teal.xml
2025-08-05 11:44:32,841 - gui.material_theme - INFO - 主题应用成功: dark_teal.xml
2025-08-05 11:44:32,841 - gui.main_window - INFO - Material主题应用成功: dark_teal.xml
2025-08-05 11:44:32,841 - gui.main_window - INFO - 主窗口初始化完成
2025-08-05 11:44:32,893 - gui.allocation_widget - INFO - Material Design增强应用成功
2025-08-05 11:44:32,894 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-08-05 11:44:32,894 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-08-05 11:44:32,920 - gui.progress_widget - INFO - 进度组件苹果风格增强应用成功
2025-08-05 11:44:32,923 - gui.progress_widget - INFO - 进度显示组件初始化完成
2025-08-05 11:45:01,238 - __main__ - INFO - 系统测试开始
2025-08-05 11:45:02,322 - core.data_loader_simple - INFO - 开始加载所有数据
2025-08-05 11:45:02,324 - core.data_loader_simple - INFO - 加载机柜配置: C:\Users\<USER>\Desktop\Python\EWReborn\data\cabinet_profiles
2025-08-05 11:45:02,325 - core.data_loader_simple - INFO - 找到0个机柜配置文件
2025-08-05 11:45:02,326 - core.data_loader_simple - INFO - 机柜配置文件加载成功
2025-08-05 11:45:02,326 - core.data_loader_simple - INFO - 加载典型回路: C:\Users\<USER>\Desktop\Python\EWReborn\data\wiring_typical
2025-08-05 11:45:02,327 - core.data_loader_simple - INFO - 找到2个典型回路文件
2025-08-05 11:45:02,327 - core.data_loader_simple - INFO - 典型回路文件加载成功
2025-08-05 11:45:02,328 - core.data_loader_simple - INFO - 数据加载完成
2025-08-05 11:45:02,332 - core.data_loader_simple - INFO - 创建数据模型对象
2025-08-05 11:45:02,333 - core.data_loader_simple - INFO - 数据模型创建完成: 0个I/O点, 0条电缆, 0个机柜, 2个典型回路
2025-08-05 11:45:02,338 - core.validator - INFO - 开始IODB数据验证
2025-08-05 11:45:02,347 - core.validator - INFO - 验证0个I/O点和0条电缆
2025-08-05 11:45:02,449 - core.validator - INFO - Tag唯一性验证通过
2025-08-05 11:45:02,454 - core.validator - INFO - Cable配对验证通过
2025-08-05 11:45:02,458 - core.validator - INFO - Cable属性一致性验证通过
2025-08-05 11:45:02,510 - core.validator - INFO - IODB数据验证通过
2025-08-05 11:45:02,701 - core.allocator - INFO - 开始I/O点分配，电缆数: 0, 机柜数: 0
2025-08-05 11:45:02,718 - core.allocator - INFO - 总计需要分配 0 个I/O点
2025-08-05 11:45:02,746 - core.allocator - INFO - I/O点分配完成，成功率: 0.0%
2025-08-05 11:45:02,749 - core.allocator - INFO - 成功分配: 0, 失败: 0
2025-08-05 11:45:03,184 - gui.material_theme - INFO - Material主题管理器初始化完成
2025-08-05 11:45:03,271 - gui.allocation_widget - INFO - Material Design增强应用成功
2025-08-05 11:45:03,273 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-08-05 11:45:03,273 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-08-05 11:45:03,278 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-08-05 11:45:03,278 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-08-05 11:45:03,300 - gui.config_widget - INFO - 配置界面初始化完成
2025-08-05 11:45:03,970 - gui.main_window - INFO - 主题已更改为: dark_teal.xml
2025-08-05 11:45:03,970 - gui.material_theme - INFO - 主题应用成功: dark_teal.xml
2025-08-05 11:45:03,970 - gui.main_window - INFO - Material主题应用成功: dark_teal.xml
2025-08-05 11:45:03,971 - gui.main_window - INFO - 主窗口初始化完成
2025-08-05 11:45:04,001 - gui.allocation_widget - INFO - Material Design增强应用成功
2025-08-05 11:45:04,002 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-08-05 11:45:04,002 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-08-05 11:45:04,013 - gui.progress_widget - INFO - 进度组件苹果风格增强应用成功
2025-08-05 11:45:04,018 - gui.progress_widget - INFO - 进度显示组件初始化完成
2025-08-05 11:49:24,445 - __main__ - INFO - EWReborn应用程序启动
2025-08-05 11:49:25,718 - gui.material_theme - INFO - Material主题管理器初始化完成
2025-08-05 11:49:25,800 - gui.allocation_widget - INFO - Material Design增强应用成功
2025-08-05 11:49:25,803 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-08-05 11:49:25,803 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-08-05 11:49:25,806 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-08-05 11:49:25,807 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-08-05 11:49:25,820 - gui.config_widget - INFO - 配置界面初始化完成
2025-08-05 11:49:26,187 - gui.main_window - INFO - 主题已更改为: dark_teal.xml
2025-08-05 11:49:26,187 - gui.material_theme - INFO - 主题应用成功: dark_teal.xml
2025-08-05 11:49:26,187 - gui.main_window - INFO - Material主题应用成功: dark_teal.xml
2025-08-05 11:49:26,188 - gui.main_window - INFO - 主窗口初始化完成
2025-08-05 11:49:26,188 - __main__ - INFO - 主窗口创建成功
2025-08-05 11:49:31,888 - __main__ - INFO - 主窗口显示完成
2025-08-05 11:49:52,307 - __main__ - INFO - EWReborn应用程序启动
2025-08-05 11:49:53,463 - gui.material_theme - INFO - Material主题管理器初始化完成
2025-08-05 11:49:53,550 - gui.allocation_widget - INFO - Material Design增强应用成功
2025-08-05 11:49:53,774 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-08-05 11:49:53,774 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-08-05 11:49:53,886 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-08-05 11:49:53,902 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-08-05 11:49:53,921 - gui.config_widget - INFO - 配置界面初始化完成
2025-08-05 11:49:54,324 - gui.main_window - INFO - 主题已更改为: dark_teal.xml
2025-08-05 11:49:54,325 - gui.material_theme - INFO - 主题应用成功: dark_teal.xml
2025-08-05 11:49:54,325 - gui.main_window - INFO - Material主题应用成功: dark_teal.xml
2025-08-05 11:49:54,325 - gui.main_window - INFO - 主窗口初始化完成
2025-08-05 11:49:54,325 - __main__ - INFO - 主窗口创建成功
2025-08-05 11:50:00,266 - __main__ - INFO - 主窗口显示完成
2025-08-05 11:50:40,961 - gui.main_window - INFO - 用户确认退出应用程序
2025-08-05 11:50:41,082 - __main__ - INFO - 应用程序退出，退出码: 0
2025-08-05 11:50:43,593 - gui.main_window - INFO - 用户确认退出应用程序
2025-08-05 11:50:43,944 - __main__ - INFO - 应用程序退出，退出码: 0
2025-08-05 11:51:04,618 - __main__ - INFO - EWReborn应用程序启动
2025-08-05 11:51:06,014 - gui.material_theme - INFO - Material主题管理器初始化完成
2025-08-05 11:51:06,132 - gui.allocation_widget - INFO - Material Design增强应用成功
2025-08-05 11:51:06,149 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-08-05 11:51:06,151 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-08-05 11:51:06,177 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-08-05 11:51:06,179 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-08-05 11:51:06,196 - gui.config_widget - INFO - 配置界面初始化完成
2025-08-05 11:51:06,564 - gui.main_window - INFO - 主题已更改为: dark_teal.xml
2025-08-05 11:51:06,565 - gui.material_theme - INFO - 主题应用成功: dark_teal.xml
2025-08-05 11:51:06,565 - gui.main_window - INFO - Material主题应用成功: dark_teal.xml
2025-08-05 11:51:06,565 - gui.main_window - INFO - 主窗口初始化完成
2025-08-05 11:51:06,566 - __main__ - INFO - 主窗口创建成功
2025-08-05 11:51:12,298 - __main__ - INFO - 主窗口显示完成
2025-08-05 11:51:36,633 - gui.allocation_widget - INFO - 开始加载数据
2025-08-05 11:51:36,649 - core.data_loader_simple - INFO - 开始加载所有数据
2025-08-05 11:51:36,650 - core.data_loader_simple - INFO - 加载机柜配置: c:\Users\<USER>\Desktop\Python\EWReborn\data\cabinet_profiles
2025-08-05 11:51:36,651 - core.data_loader_simple - INFO - 找到0个机柜配置文件
2025-08-05 11:51:36,651 - core.data_loader_simple - INFO - 机柜配置文件加载成功
2025-08-05 11:51:36,651 - core.data_loader_simple - INFO - 加载典型回路: c:\Users\<USER>\Desktop\Python\EWReborn\data\wiring_typical
2025-08-05 11:51:36,652 - core.data_loader_simple - INFO - 找到2个典型回路文件
2025-08-05 11:51:36,653 - core.data_loader_simple - INFO - 典型回路文件加载成功
2025-08-05 11:51:36,653 - core.data_loader_simple - INFO - 数据加载完成
2025-08-05 11:51:36,654 - core.data_loader_simple - INFO - 加载IODB数据: C:/Users/<USER>/Desktop/Python/EWReborn/data/iodb/IODB_nospare.xlsx
2025-08-05 11:51:36,654 - utils.excel_utils_simple - INFO - 读取Excel文件: C:/Users/<USER>/Desktop/Python/EWReborn/data/iodb/IODB_nospare.xlsx
2025-08-05 11:51:37,036 - utils.excel_utils_simple - INFO - 成功读取Excel文件，包含工作表: ['PPG']
2025-08-05 11:51:37,036 - core.data_loader_simple - INFO - IODB工作表包含 219 行数据
2025-08-05 11:51:37,057 - core.data_loader_simple - INFO - 成功处理 219 个I/O点，0 条电缆
2025-08-05 11:51:37,058 - core.data_loader_simple - INFO - 成功加载IODB数据: 219个I/O点，0条电缆
2025-08-05 11:51:37,060 - core.data_loader_simple - INFO - 加载PIDB数据: C:/Users/<USER>/Desktop/Python/EWReborn/data/pidb/PIDB PPG.xlsx
2025-08-05 11:51:37,061 - utils.excel_utils_simple - INFO - 读取Excel文件: C:/Users/<USER>/Desktop/Python/EWReborn/data/pidb/PIDB PPG.xlsx
2025-08-05 11:51:37,078 - utils.excel_utils_simple - INFO - 成功读取Excel文件，包含工作表: ['project', 'chassis', 'cabinet', 'component']
2025-08-05 11:51:37,579 - gui.allocation_widget - INFO - 数据加载完成
2025-08-05 11:56:56,676 - gui.main_window - INFO - 配置已更新
2025-08-05 11:56:59,091 - gui.main_window - INFO - 配置已更新
2025-08-05 11:57:17,826 - gui.allocation_widget - INFO - 开始加载数据
2025-08-05 11:57:17,828 - core.data_loader_simple - INFO - 开始加载所有数据
2025-08-05 11:57:17,828 - core.data_loader_simple - INFO - 加载机柜配置: c:\Users\<USER>\Desktop\Python\EWReborn\data\cabinet_profiles
2025-08-05 11:57:17,829 - core.data_loader_simple - INFO - 找到0个机柜配置文件
2025-08-05 11:57:17,830 - core.data_loader_simple - INFO - 机柜配置文件加载成功
2025-08-05 11:57:17,830 - core.data_loader_simple - INFO - 加载典型回路: c:\Users\<USER>\Desktop\Python\EWReborn\data\wiring_typical
2025-08-05 11:57:17,830 - core.data_loader_simple - INFO - 找到2个典型回路文件
2025-08-05 11:57:17,831 - core.data_loader_simple - INFO - 典型回路文件加载成功
2025-08-05 11:57:17,832 - core.data_loader_simple - INFO - 数据加载完成
2025-08-05 11:57:17,832 - core.data_loader_simple - INFO - 加载IODB数据: C:/Users/<USER>/Desktop/Python/EWReborn/data/iodb/IODB_nospare.xlsx
2025-08-05 11:57:17,833 - utils.excel_utils_simple - INFO - 读取Excel文件: C:/Users/<USER>/Desktop/Python/EWReborn/data/iodb/IODB_nospare.xlsx
2025-08-05 11:57:17,881 - utils.excel_utils_simple - INFO - 成功读取Excel文件，包含工作表: ['PPG']
2025-08-05 11:57:17,882 - core.data_loader_simple - INFO - IODB工作表包含 219 行数据
2025-08-05 11:57:17,895 - core.data_loader_simple - INFO - 成功处理 219 个I/O点，0 条电缆
2025-08-05 11:57:17,896 - core.data_loader_simple - INFO - 成功加载IODB数据: 219个I/O点，0条电缆
2025-08-05 11:57:17,897 - core.data_loader_simple - INFO - 加载PIDB数据: C:/Users/<USER>/Desktop/Python/EWReborn/data/pidb/PIDB PPG.xlsx
2025-08-05 11:57:17,898 - utils.excel_utils_simple - INFO - 读取Excel文件: C:/Users/<USER>/Desktop/Python/EWReborn/data/pidb/PIDB PPG.xlsx
2025-08-05 11:57:17,918 - utils.excel_utils_simple - INFO - 成功读取Excel文件，包含工作表: ['project', 'chassis', 'cabinet', 'component']
2025-08-05 11:57:18,419 - gui.allocation_widget - INFO - 数据加载完成
2025-08-05 12:35:02,347 - __main__ - INFO - EWReborn应用程序启动
2025-08-05 12:35:03,516 - gui.material_theme - INFO - Material主题管理器初始化完成
2025-08-05 12:35:03,606 - gui.allocation_widget - INFO - Material Design增强应用成功
2025-08-05 12:35:03,607 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-08-05 12:35:03,608 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-08-05 12:35:03,611 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-08-05 12:35:03,611 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-08-05 12:35:03,627 - gui.config_widget - INFO - 配置界面初始化完成
2025-08-05 12:35:04,070 - gui.main_window - INFO - 主题已更改为: dark_teal.xml
2025-08-05 12:35:04,070 - gui.material_theme - INFO - 主题应用成功: dark_teal.xml
2025-08-05 12:35:04,071 - gui.main_window - INFO - Material主题应用成功: dark_teal.xml
2025-08-05 12:35:04,071 - gui.main_window - INFO - 主窗口初始化完成
2025-08-05 12:35:04,071 - __main__ - INFO - 主窗口创建成功
2025-08-05 12:35:09,821 - __main__ - INFO - 主窗口显示完成
2025-08-05 12:35:34,214 - __main__ - INFO - EWReborn应用程序启动
2025-08-05 12:35:35,885 - gui.material_theme - INFO - Material主题管理器初始化完成
2025-08-05 12:35:35,984 - gui.allocation_widget - INFO - Material Design增强应用成功
2025-08-05 12:35:35,986 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-08-05 12:35:35,989 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-08-05 12:35:35,997 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-08-05 12:35:35,997 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-08-05 12:35:36,014 - gui.config_widget - INFO - 配置界面初始化完成
2025-08-05 12:35:36,846 - gui.main_window - INFO - 主题已更改为: dark_teal.xml
2025-08-05 12:35:36,847 - gui.material_theme - INFO - 主题应用成功: dark_teal.xml
2025-08-05 12:35:36,847 - gui.main_window - INFO - Material主题应用成功: dark_teal.xml
2025-08-05 12:35:36,848 - gui.main_window - INFO - 主窗口初始化完成
2025-08-05 12:35:36,848 - __main__ - INFO - 主窗口创建成功
2025-08-05 12:35:43,159 - __main__ - INFO - 主窗口显示完成
2025-08-05 12:52:48,711 - gui.allocation_widget - INFO - 开始加载数据
2025-08-05 12:52:48,723 - core.data_loader_simple - INFO - 开始加载所有数据
2025-08-05 12:52:48,723 - core.data_loader_simple - INFO - 加载机柜配置: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates
2025-08-05 12:52:48,724 - core.data_loader_simple - INFO - 找到5个机柜配置文件
2025-08-05 12:52:48,725 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG BAR.xml
2025-08-05 12:52:48,726 - utils.xml_parser - INFO - 成功解析机柜配置: PPG BAR
2025-08-05 12:52:48,726 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG REL.xml
2025-08-05 12:52:48,727 - utils.xml_parser - INFO - 成功解析机柜配置: PPG REL
2025-08-05 12:52:48,730 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG RIO.xml
2025-08-05 12:52:48,731 - utils.xml_parser - INFO - 成功解析机柜配置: PPG RIO
2025-08-05 12:52:48,734 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG SYS.xml
2025-08-05 12:52:48,736 - utils.xml_parser - INFO - 成功解析机柜配置: PPG SYS
2025-08-05 12:52:48,736 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG FINAL\For Check.xml
2025-08-05 12:52:48,737 - utils.xml_parser - INFO - 成功解析机柜配置: For Check
2025-08-05 12:52:48,737 - core.data_loader_simple - INFO - 机柜配置文件加载成功
2025-08-05 12:52:48,738 - core.data_loader_simple - INFO - 加载典型回路: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical
2025-08-05 12:52:48,738 - core.data_loader_simple - INFO - 找到10个典型回路文件
2025-08-05 12:52:48,739 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\AI IS BABP.xml
2025-08-05 12:52:48,741 - utils.xml_parser - INFO - 成功解析典型回路: AI IS BABP
2025-08-05 12:52:48,741 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\AI NIS 4WIRE.xml
2025-08-05 12:52:48,743 - utils.xml_parser - INFO - 成功解析典型回路: AI NIS 4WIRE
2025-08-05 12:52:48,746 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\AI NIS MCC.xml
2025-08-05 12:52:48,748 - utils.xml_parser - INFO - 成功解析典型回路: AI NIS MCC
2025-08-05 12:52:48,749 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DI NIS MCC RY.xml
2025-08-05 12:52:48,751 - utils.xml_parser - INFO - 成功解析典型回路: DI NIS MCC RY
2025-08-05 12:52:48,751 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DI NIS N.xml
2025-08-05 12:52:48,752 - utils.xml_parser - INFO - 成功解析典型回路: DI NIS N
2025-08-05 12:52:48,753 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DI NIS REMOTE.xml
2025-08-05 12:52:48,754 - utils.xml_parser - INFO - 成功解析典型回路: DI NIS REMOTE
2025-08-05 12:52:48,755 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DO NIS DRY HPRY.xml
2025-08-05 12:52:48,757 - utils.xml_parser - INFO - 成功解析典型回路: DO NIS DRY HPRY
2025-08-05 12:52:48,757 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DO NIS DRY SIL3RY.xml
2025-08-05 12:52:48,758 - utils.xml_parser - INFO - 成功解析典型回路: DO NIS DRY SIL3RY
2025-08-05 12:52:48,759 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DO NIS WET REMOTE.xml
2025-08-05 12:52:48,761 - utils.xml_parser - INFO - 成功解析典型回路: DO NIS WET REMOTE
2025-08-05 12:52:48,761 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DO NIS WET SIL3RY.xml
2025-08-05 12:52:48,763 - utils.xml_parser - INFO - 成功解析典型回路: DO NIS WET SIL3RY
2025-08-05 12:52:48,763 - core.data_loader_simple - INFO - 典型回路文件加载成功
2025-08-05 12:52:48,763 - core.data_loader_simple - INFO - 数据加载完成
2025-08-05 12:52:48,766 - core.data_loader_simple - INFO - 加载IODB数据: C:/Users/<USER>/Desktop/Python/EWReborn/04A_IODB/IODB_nospare.xlsx
2025-08-05 12:52:48,767 - utils.excel_utils_simple - INFO - 读取Excel文件: C:/Users/<USER>/Desktop/Python/EWReborn/04A_IODB/IODB_nospare.xlsx
2025-08-05 12:52:48,838 - utils.excel_utils_simple - INFO - 成功读取Excel文件，包含工作表: ['PPG']
2025-08-05 12:52:48,839 - core.data_loader_simple - INFO - IODB工作表包含 219 行数据
2025-08-05 12:52:48,860 - core.data_loader_simple - INFO - 成功处理 219 个I/O点，43 条电缆
2025-08-05 12:52:48,860 - core.data_loader_simple - INFO - 成功加载IODB数据: 219个I/O点，43条电缆
2025-08-05 12:52:48,861 - core.data_loader_simple - INFO - 加载PIDB数据: C:/Users/<USER>/Desktop/Python/EWReborn/04B_PIDB/PIDB PPG.xlsx
2025-08-05 12:52:48,862 - utils.excel_utils_simple - INFO - 读取Excel文件: C:/Users/<USER>/Desktop/Python/EWReborn/04B_PIDB/PIDB PPG.xlsx
2025-08-05 12:52:48,876 - utils.excel_utils_simple - INFO - 成功读取Excel文件，包含工作表: ['project', 'chassis', 'cabinet', 'component']
2025-08-05 12:52:48,876 - core.data_loader_simple - INFO - 分析PIDB工作表: project, 行数: 6
2025-08-05 12:52:48,877 - core.data_loader_simple - INFO - 分析PIDB工作表: chassis, 行数: 4
2025-08-05 12:52:48,877 - core.data_loader_simple - INFO - 分析PIDB工作表: cabinet, 行数: 4
2025-08-05 12:52:48,877 - core.data_loader_simple - INFO - 分析PIDB工作表: component, 行数: 10
2025-08-05 12:52:48,877 - core.data_loader_simple - INFO - 未找到明确的机架和机柜信息，创建默认结构
2025-08-05 12:52:49,372 - gui.allocation_widget - INFO - 数据加载完成
2025-08-05 12:52:53,454 - gui.allocation_widget - INFO - 开始加载数据
2025-08-05 12:52:53,456 - core.data_loader_simple - INFO - 开始加载所有数据
2025-08-05 12:52:53,457 - core.data_loader_simple - INFO - 加载机柜配置: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates
2025-08-05 12:52:53,458 - core.data_loader_simple - INFO - 找到5个机柜配置文件
2025-08-05 12:52:53,458 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG BAR.xml
2025-08-05 12:52:53,459 - utils.xml_parser - INFO - 成功解析机柜配置: PPG BAR
2025-08-05 12:52:53,459 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG REL.xml
2025-08-05 12:52:53,460 - utils.xml_parser - INFO - 成功解析机柜配置: PPG REL
2025-08-05 12:52:53,460 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG RIO.xml
2025-08-05 12:52:53,461 - utils.xml_parser - INFO - 成功解析机柜配置: PPG RIO
2025-08-05 12:52:53,462 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG SYS.xml
2025-08-05 12:52:53,462 - utils.xml_parser - INFO - 成功解析机柜配置: PPG SYS
2025-08-05 12:52:53,463 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG FINAL\For Check.xml
2025-08-05 12:52:53,463 - utils.xml_parser - INFO - 成功解析机柜配置: For Check
2025-08-05 12:52:53,464 - core.data_loader_simple - INFO - 机柜配置文件加载成功
2025-08-05 12:52:53,464 - core.data_loader_simple - INFO - 加载典型回路: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical
2025-08-05 12:52:53,464 - core.data_loader_simple - INFO - 找到10个典型回路文件
2025-08-05 12:52:53,465 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\AI IS BABP.xml
2025-08-05 12:52:53,467 - utils.xml_parser - INFO - 成功解析典型回路: AI IS BABP
2025-08-05 12:52:53,467 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\AI NIS 4WIRE.xml
2025-08-05 12:52:53,469 - utils.xml_parser - INFO - 成功解析典型回路: AI NIS 4WIRE
2025-08-05 12:52:53,470 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\AI NIS MCC.xml
2025-08-05 12:52:53,471 - utils.xml_parser - INFO - 成功解析典型回路: AI NIS MCC
2025-08-05 12:52:53,472 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DI NIS MCC RY.xml
2025-08-05 12:52:53,473 - utils.xml_parser - INFO - 成功解析典型回路: DI NIS MCC RY
2025-08-05 12:52:53,474 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DI NIS N.xml
2025-08-05 12:52:53,475 - utils.xml_parser - INFO - 成功解析典型回路: DI NIS N
2025-08-05 12:52:53,475 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DI NIS REMOTE.xml
2025-08-05 12:52:53,476 - utils.xml_parser - INFO - 成功解析典型回路: DI NIS REMOTE
2025-08-05 12:52:53,477 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DO NIS DRY HPRY.xml
2025-08-05 12:52:53,478 - utils.xml_parser - INFO - 成功解析典型回路: DO NIS DRY HPRY
2025-08-05 12:52:53,478 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DO NIS DRY SIL3RY.xml
2025-08-05 12:52:53,479 - utils.xml_parser - INFO - 成功解析典型回路: DO NIS DRY SIL3RY
2025-08-05 12:52:53,480 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DO NIS WET REMOTE.xml
2025-08-05 12:52:53,481 - utils.xml_parser - INFO - 成功解析典型回路: DO NIS WET REMOTE
2025-08-05 12:52:53,481 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DO NIS WET SIL3RY.xml
2025-08-05 12:52:53,483 - utils.xml_parser - INFO - 成功解析典型回路: DO NIS WET SIL3RY
2025-08-05 12:52:53,483 - core.data_loader_simple - INFO - 典型回路文件加载成功
2025-08-05 12:52:53,484 - core.data_loader_simple - INFO - 数据加载完成
2025-08-05 12:52:53,485 - core.data_loader_simple - INFO - 加载IODB数据: C:/Users/<USER>/Desktop/Python/EWReborn/04A_IODB/IODB_nospare.xlsx
2025-08-05 12:52:53,485 - utils.excel_utils_simple - INFO - 读取Excel文件: C:/Users/<USER>/Desktop/Python/EWReborn/04A_IODB/IODB_nospare.xlsx
2025-08-05 12:52:53,518 - utils.excel_utils_simple - INFO - 成功读取Excel文件，包含工作表: ['PPG']
2025-08-05 12:52:53,518 - core.data_loader_simple - INFO - IODB工作表包含 219 行数据
2025-08-05 12:52:53,539 - core.data_loader_simple - INFO - 成功处理 219 个I/O点，43 条电缆
2025-08-05 12:52:53,540 - core.data_loader_simple - INFO - 成功加载IODB数据: 219个I/O点，43条电缆
2025-08-05 12:52:53,541 - core.data_loader_simple - INFO - 加载PIDB数据: C:/Users/<USER>/Desktop/Python/EWReborn/04B_PIDB/PIDB PPG.xlsx
2025-08-05 12:52:53,541 - utils.excel_utils_simple - INFO - 读取Excel文件: C:/Users/<USER>/Desktop/Python/EWReborn/04B_PIDB/PIDB PPG.xlsx
2025-08-05 12:52:53,555 - utils.excel_utils_simple - INFO - 成功读取Excel文件，包含工作表: ['project', 'chassis', 'cabinet', 'component']
2025-08-05 12:52:53,555 - core.data_loader_simple - INFO - 分析PIDB工作表: project, 行数: 6
2025-08-05 12:52:53,556 - core.data_loader_simple - INFO - 分析PIDB工作表: chassis, 行数: 4
2025-08-05 12:52:53,556 - core.data_loader_simple - INFO - 分析PIDB工作表: cabinet, 行数: 4
2025-08-05 12:52:53,556 - core.data_loader_simple - INFO - 分析PIDB工作表: component, 行数: 10
2025-08-05 12:52:53,557 - core.data_loader_simple - INFO - 未找到明确的机架和机柜信息，创建默认结构
2025-08-05 12:52:54,058 - gui.allocation_widget - INFO - 数据加载完成
2025-08-05 12:53:01,006 - gui.main_window - INFO - 主题已更改为: light_amber.xml
2025-08-05 12:53:01,007 - gui.material_theme - INFO - 主题应用成功: light_amber.xml
2025-08-05 12:53:01,008 - gui.material_theme - INFO - 主题配置已更新: light_amber.xml
2025-08-05 12:53:01,008 - gui.main_window - INFO - 主题切换成功: light_amber.xml
2025-08-05 12:53:06,042 - gui.main_window - INFO - 主题已更改为: light_cyan.xml
2025-08-05 12:53:06,043 - gui.material_theme - INFO - 主题应用成功: light_cyan.xml
2025-08-05 12:53:06,043 - gui.material_theme - INFO - 主题配置已更新: light_cyan.xml
2025-08-05 12:53:06,044 - gui.main_window - INFO - 主题切换成功: light_cyan.xml
2025-08-05 12:53:15,887 - gui.main_window - INFO - 主题已更改为: light_teal.xml
2025-08-05 12:53:15,888 - gui.material_theme - INFO - 主题应用成功: light_teal.xml
2025-08-05 12:53:15,888 - gui.material_theme - INFO - 主题配置已更新: light_teal.xml
2025-08-05 12:53:15,891 - gui.main_window - INFO - 主题切换成功: light_teal.xml
2025-08-05 12:53:20,867 - gui.main_window - INFO - 主题已更改为: light_yellow.xml
2025-08-05 12:53:20,867 - gui.material_theme - INFO - 主题应用成功: light_yellow.xml
2025-08-05 12:53:20,868 - gui.material_theme - INFO - 主题配置已更新: light_yellow.xml
2025-08-05 12:53:20,868 - gui.main_window - INFO - 主题切换成功: light_yellow.xml
2025-08-05 12:53:25,355 - gui.main_window - INFO - 主题已更改为: light_blue.xml
2025-08-05 12:53:25,357 - gui.material_theme - INFO - 主题应用成功: light_blue.xml
2025-08-05 12:53:25,357 - gui.material_theme - INFO - 主题配置已更新: light_blue.xml
2025-08-05 12:53:25,357 - gui.main_window - INFO - 主题切换成功: light_blue.xml
2025-08-05 12:53:38,814 - gui.allocation_widget - INFO - 开始加载数据
2025-08-05 12:53:38,815 - core.data_loader_simple - INFO - 开始加载所有数据
2025-08-05 12:53:38,816 - core.data_loader_simple - INFO - 加载机柜配置: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates
2025-08-05 12:53:38,816 - core.data_loader_simple - INFO - 找到5个机柜配置文件
2025-08-05 12:53:38,817 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG BAR.xml
2025-08-05 12:53:38,818 - utils.xml_parser - INFO - 成功解析机柜配置: PPG BAR
2025-08-05 12:53:38,819 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG REL.xml
2025-08-05 12:53:38,820 - utils.xml_parser - INFO - 成功解析机柜配置: PPG REL
2025-08-05 12:53:38,820 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG RIO.xml
2025-08-05 12:53:38,821 - utils.xml_parser - INFO - 成功解析机柜配置: PPG RIO
2025-08-05 12:53:38,822 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG SYS.xml
2025-08-05 12:53:38,822 - utils.xml_parser - INFO - 成功解析机柜配置: PPG SYS
2025-08-05 12:53:38,823 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG FINAL\For Check.xml
2025-08-05 12:53:38,824 - utils.xml_parser - INFO - 成功解析机柜配置: For Check
2025-08-05 12:53:38,825 - core.data_loader_simple - INFO - 机柜配置文件加载成功
2025-08-05 12:53:38,825 - core.data_loader_simple - INFO - 加载典型回路: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical
2025-08-05 12:53:38,826 - core.data_loader_simple - INFO - 找到10个典型回路文件
2025-08-05 12:53:38,826 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\AI IS BABP.xml
2025-08-05 12:53:38,828 - utils.xml_parser - INFO - 成功解析典型回路: AI IS BABP
2025-08-05 12:53:38,828 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\AI NIS 4WIRE.xml
2025-08-05 12:53:38,830 - utils.xml_parser - INFO - 成功解析典型回路: AI NIS 4WIRE
2025-08-05 12:53:38,830 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\AI NIS MCC.xml
2025-08-05 12:53:38,833 - utils.xml_parser - INFO - 成功解析典型回路: AI NIS MCC
2025-08-05 12:53:38,875 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DI NIS MCC RY.xml
2025-08-05 12:53:38,908 - utils.xml_parser - INFO - 成功解析典型回路: DI NIS MCC RY
2025-08-05 12:53:38,921 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DI NIS N.xml
2025-08-05 12:53:38,923 - utils.xml_parser - INFO - 成功解析典型回路: DI NIS N
2025-08-05 12:53:38,936 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DI NIS REMOTE.xml
2025-08-05 12:53:38,939 - utils.xml_parser - INFO - 成功解析典型回路: DI NIS REMOTE
2025-08-05 12:53:38,940 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DO NIS DRY HPRY.xml
2025-08-05 12:53:38,942 - utils.xml_parser - INFO - 成功解析典型回路: DO NIS DRY HPRY
2025-08-05 12:53:38,942 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DO NIS DRY SIL3RY.xml
2025-08-05 12:53:38,944 - utils.xml_parser - INFO - 成功解析典型回路: DO NIS DRY SIL3RY
2025-08-05 12:53:38,945 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DO NIS WET REMOTE.xml
2025-08-05 12:53:38,969 - utils.xml_parser - INFO - 成功解析典型回路: DO NIS WET REMOTE
2025-08-05 12:53:38,970 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DO NIS WET SIL3RY.xml
2025-08-05 12:53:38,975 - utils.xml_parser - INFO - 成功解析典型回路: DO NIS WET SIL3RY
2025-08-05 12:53:38,976 - core.data_loader_simple - INFO - 典型回路文件加载成功
2025-08-05 12:53:38,977 - core.data_loader_simple - INFO - 数据加载完成
2025-08-05 12:53:38,978 - core.data_loader_simple - INFO - 加载IODB数据: C:/Users/<USER>/Desktop/Python/EWReborn/04A_IODB/IODB_nospare.xlsx
2025-08-05 12:53:38,979 - utils.excel_utils_simple - INFO - 读取Excel文件: C:/Users/<USER>/Desktop/Python/EWReborn/04A_IODB/IODB_nospare.xlsx
2025-08-05 12:53:39,014 - utils.excel_utils_simple - INFO - 成功读取Excel文件，包含工作表: ['PPG']
2025-08-05 12:53:39,015 - core.data_loader_simple - INFO - IODB工作表包含 219 行数据
2025-08-05 12:53:39,035 - core.data_loader_simple - INFO - 成功处理 219 个I/O点，43 条电缆
2025-08-05 12:53:39,035 - core.data_loader_simple - INFO - 成功加载IODB数据: 219个I/O点，43条电缆
2025-08-05 12:53:39,036 - core.data_loader_simple - INFO - 加载PIDB数据: C:/Users/<USER>/Desktop/Python/EWReborn/04B_PIDB/PIDB PPG.xlsx
2025-08-05 12:53:39,037 - utils.excel_utils_simple - INFO - 读取Excel文件: C:/Users/<USER>/Desktop/Python/EWReborn/04B_PIDB/PIDB PPG.xlsx
2025-08-05 12:53:39,050 - utils.excel_utils_simple - INFO - 成功读取Excel文件，包含工作表: ['project', 'chassis', 'cabinet', 'component']
2025-08-05 12:53:39,051 - core.data_loader_simple - INFO - 分析PIDB工作表: project, 行数: 6
2025-08-05 12:53:39,052 - core.data_loader_simple - INFO - 分析PIDB工作表: chassis, 行数: 4
2025-08-05 12:53:39,053 - core.data_loader_simple - INFO - 分析PIDB工作表: cabinet, 行数: 4
2025-08-05 12:53:39,053 - core.data_loader_simple - INFO - 分析PIDB工作表: component, 行数: 10
2025-08-05 12:53:39,053 - core.data_loader_simple - INFO - 未找到明确的机架和机柜信息，创建默认结构
2025-08-05 12:53:39,565 - gui.allocation_widget - INFO - 数据加载完成
2025-08-05 12:53:43,405 - gui.allocation_widget - INFO - 开始数据验证
2025-08-05 12:53:43,409 - core.validator - INFO - 开始IODB数据验证
2025-08-05 12:53:43,409 - core.validator - INFO - 验证219个I/O点和43条电缆
2025-08-05 12:53:43,410 - core.validator - INFO - Tag唯一性验证通过
2025-08-05 12:53:43,410 - core.validator - WARNING - 电缆 1105-iAZC-10001 的I/O点数量(8)与对数(8)不匹配，期望16个点
2025-08-05 12:53:43,410 - core.validator - WARNING - 电缆 1105-iAZC-10002 的I/O点数量(8)与对数(8)不匹配，期望16个点
2025-08-05 12:53:43,410 - core.validator - WARNING - 电缆 1105-iAZC-10003 的I/O点数量(8)与对数(8)不匹配，期望16个点
2025-08-05 12:53:43,411 - core.validator - WARNING - 电缆 1105-iAZC-10004 的I/O点数量(8)与对数(8)不匹配，期望16个点
2025-08-05 12:53:43,411 - core.validator - WARNING - 电缆 1105-iAZC-10005 的I/O点数量(8)与对数(8)不匹配，期望16个点
2025-08-05 12:53:43,412 - core.validator - WARNING - 电缆 1105-iAZC-10006 的I/O点数量(8)与对数(8)不匹配，期望16个点
2025-08-05 12:53:43,412 - core.validator - WARNING - 电缆 1105-iAZC-10007 的I/O点数量(8)与对数(8)不匹配，期望16个点
2025-08-05 12:53:43,413 - core.validator - WARNING - 电缆 1105-SZC-10003 的I/O点数量(12)与对数(12)不匹配，期望24个点
2025-08-05 12:53:43,413 - core.validator - WARNING - 电缆 1105-SZC-10001 的I/O点数量(8)与对数(8)不匹配，期望16个点
2025-08-05 12:53:43,414 - core.validator - WARNING - 电缆 1105-SZC-10002 的I/O点数量(8)与对数(8)不匹配，期望16个点
2025-08-05 12:53:43,414 - core.validator - WARNING - 电缆 1105-SZC-10004 的I/O点数量(8)与对数(8)不匹配，期望16个点
2025-08-05 12:53:43,414 - core.validator - WARNING - 电缆 1105-AZC-10001 的I/O点数量(8)与对数(8)不匹配，期望16个点
2025-08-05 12:53:43,415 - core.validator - WARNING - 电缆 1105-DZC-10001 的I/O点数量(8)与对数(8)不匹配，期望16个点
2025-08-05 12:53:43,415 - core.validator - WARNING - 电缆 1105-YLZ-10001 的I/O点数量(8)与对数(8)不匹配，期望16个点
2025-08-05 12:53:43,415 - core.validator - WARNING - 电缆 1105-YSPZ-10001 的I/O点数量(8)与对数(8)不匹配，期望16个点
2025-08-05 12:53:43,415 - core.validator - WARNING - 电缆 1105-YIZT-10001 的I/O点数量(8)与对数(8)不匹配，期望16个点
2025-08-05 12:53:43,416 - core.validator - WARNING - 电缆 1105-KC-10001 的I/O点数量(8)与对数(12)不匹配，期望24个点
2025-08-05 12:53:43,417 - core.validator - WARNING - 电缆 1105-KC-10002 的I/O点数量(12)与对数(12)不匹配，期望24个点
2025-08-05 12:53:43,418 - core.validator - WARNING - 电缆 1105-KC-10003 的I/O点数量(12)与对数(12)不匹配，期望24个点
2025-08-05 12:53:43,419 - core.validator - WARNING - 电缆 1105-KC-10004 的I/O点数量(8)与对数(12)不匹配，期望24个点
2025-08-05 12:53:43,419 - core.validator - WARNING - 电缆 1105-KC-10005 的I/O点数量(8)与对数(12)不匹配，期望24个点
2025-08-05 12:53:43,419 - core.validator - WARNING - 电缆 1105-HZ-002-1 的I/O点数量(1)与对数(1)不匹配，期望2个点
2025-08-05 12:53:43,420 - core.validator - WARNING - 电缆 1105-HZ-002-2 的I/O点数量(1)与对数(1)不匹配，期望2个点
2025-08-05 12:53:43,420 - core.validator - WARNING - 电缆 1105-HZ-002-3 的I/O点数量(1)与对数(1)不匹配，期望2个点
2025-08-05 12:53:43,420 - core.validator - WARNING - 电缆 1105-HZ-003-1 的I/O点数量(1)与对数(1)不匹配，期望2个点
2025-08-05 12:53:43,420 - core.validator - WARNING - 电缆 1105-HZ-003-2 的I/O点数量(1)与对数(1)不匹配，期望2个点
2025-08-05 12:53:43,421 - core.validator - WARNING - 电缆 1105-HZ-003-3 的I/O点数量(1)与对数(1)不匹配，期望2个点
2025-08-05 12:53:43,422 - core.validator - WARNING - 电缆 1105-HS-10503B-1 的I/O点数量(1)与对数(1)不匹配，期望2个点
2025-08-05 12:53:43,422 - core.validator - WARNING - 电缆 1105-HS-10503B-2 的I/O点数量(1)与对数(1)不匹配，期望2个点
2025-08-05 12:53:43,422 - core.validator - WARNING - 电缆 1105-HS-10503B-3 的I/O点数量(1)与对数(1)不匹配，期望2个点
2025-08-05 12:53:43,423 - core.validator - WARNING - 电缆 1105-HS-10503B-RESET 的I/O点数量(1)与对数(1)不匹配，期望2个点
2025-08-05 12:53:43,423 - core.validator - WARNING - 电缆 1105-AUX-10001 的I/O点数量(8)与对数(8)不匹配，期望16个点
2025-08-05 12:53:43,423 - core.validator - WARNING - 电缆 1105-AUX-10002 的I/O点数量(8)与对数(8)不匹配，期望16个点
2025-08-05 12:53:43,423 - core.validator - WARNING - 电缆 1105-HZ-002A-1 的I/O点数量(1)与对数(1)不匹配，期望2个点
2025-08-05 12:53:43,424 - core.validator - WARNING - 电缆 1105-HZ-002A-2 的I/O点数量(1)与对数(1)不匹配，期望2个点
2025-08-05 12:53:43,424 - core.validator - WARNING - 电缆 1105-HZ-002A-3 的I/O点数量(1)与对数(1)不匹配，期望2个点
2025-08-05 12:53:43,424 - core.validator - WARNING - 电缆 1105-HZ-004-1 的I/O点数量(1)与对数(1)不匹配，期望2个点
2025-08-05 12:53:43,424 - core.validator - WARNING - 电缆 1105-HZ-004-2 的I/O点数量(1)与对数(1)不匹配，期望2个点
2025-08-05 12:53:43,425 - core.validator - WARNING - 电缆 1105-HZ-004-3 的I/O点数量(1)与对数(1)不匹配，期望2个点
2025-08-05 12:53:43,425 - core.validator - WARNING - 电缆 1105-HS-10503A-1 的I/O点数量(1)与对数(1)不匹配，期望2个点
2025-08-05 12:53:43,425 - core.validator - WARNING - 电缆 1105-HS-10503A-2 的I/O点数量(1)与对数(1)不匹配，期望2个点
2025-08-05 12:53:43,425 - core.validator - WARNING - 电缆 1105-HS-10503A-3 的I/O点数量(1)与对数(1)不匹配，期望2个点
2025-08-05 12:53:43,426 - core.validator - WARNING - 电缆 1105-AUX-10003 的I/O点数量(4)与对数(4)不匹配，期望8个点
2025-08-05 12:53:43,426 - core.validator - INFO - Cable配对验证通过
2025-08-05 12:53:43,426 - core.validator - INFO - Cable属性一致性验证通过
2025-08-05 12:53:43,427 - core.validator - INFO - IODB数据验证通过
2025-08-05 12:53:43,454 - gui.allocation_widget - ERROR - 数据验证失败: 'ValidationResult' object has no attribute 'allocation_summary'
2025-08-05 13:06:57,281 - __main__ - INFO - 系统测试开始
2025-08-05 13:06:58,516 - core.data_loader_simple - INFO - 开始加载所有数据
2025-08-05 13:06:58,522 - core.data_loader_simple - INFO - 加载机柜配置: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates
2025-08-05 13:06:58,526 - core.data_loader_simple - INFO - 找到5个机柜配置文件
2025-08-05 13:06:58,528 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG BAR.xml
2025-08-05 13:06:58,530 - utils.xml_parser - INFO - 成功解析机柜配置: PPG BAR
2025-08-05 13:06:58,531 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG REL.xml
2025-08-05 13:06:58,531 - utils.xml_parser - INFO - 成功解析机柜配置: PPG REL
2025-08-05 13:06:58,533 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG RIO.xml
2025-08-05 13:06:58,536 - utils.xml_parser - INFO - 成功解析机柜配置: PPG RIO
2025-08-05 13:06:58,538 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG SYS.xml
2025-08-05 13:06:58,540 - utils.xml_parser - INFO - 成功解析机柜配置: PPG SYS
2025-08-05 13:06:58,540 - utils.xml_parser - INFO - 解析机柜配置XML: C:\Users\<USER>\Desktop\Python\EWReborn\01B_Cabinet Templates\PPG FINAL\For Check.xml
2025-08-05 13:06:58,541 - utils.xml_parser - INFO - 成功解析机柜配置: For Check
2025-08-05 13:06:58,541 - core.data_loader_simple - INFO - 机柜配置文件加载成功
2025-08-05 13:06:58,542 - core.data_loader_simple - INFO - 加载典型回路: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical
2025-08-05 13:06:58,542 - core.data_loader_simple - INFO - 找到10个典型回路文件
2025-08-05 13:06:58,542 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\AI IS BABP.xml
2025-08-05 13:06:58,561 - utils.xml_parser - INFO - 成功解析典型回路: AI IS BABP
2025-08-05 13:06:58,569 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\AI NIS 4WIRE.xml
2025-08-05 13:06:58,572 - utils.xml_parser - INFO - 成功解析典型回路: AI NIS 4WIRE
2025-08-05 13:06:58,573 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\AI NIS MCC.xml
2025-08-05 13:06:58,596 - utils.xml_parser - INFO - 成功解析典型回路: AI NIS MCC
2025-08-05 13:06:58,605 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DI NIS MCC RY.xml
2025-08-05 13:06:58,607 - utils.xml_parser - INFO - 成功解析典型回路: DI NIS MCC RY
2025-08-05 13:06:58,607 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DI NIS N.xml
2025-08-05 13:06:58,626 - utils.xml_parser - INFO - 成功解析典型回路: DI NIS N
2025-08-05 13:06:58,637 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DI NIS REMOTE.xml
2025-08-05 13:06:58,639 - utils.xml_parser - INFO - 成功解析典型回路: DI NIS REMOTE
2025-08-05 13:06:58,640 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DO NIS DRY HPRY.xml
2025-08-05 13:06:58,641 - utils.xml_parser - INFO - 成功解析典型回路: DO NIS DRY HPRY
2025-08-05 13:06:58,644 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DO NIS DRY SIL3RY.xml
2025-08-05 13:06:58,674 - utils.xml_parser - INFO - 成功解析典型回路: DO NIS DRY SIL3RY
2025-08-05 13:06:58,696 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DO NIS WET REMOTE.xml
2025-08-05 13:06:58,806 - utils.xml_parser - INFO - 成功解析典型回路: DO NIS WET REMOTE
2025-08-05 13:06:58,831 - utils.xml_parser - INFO - 解析典型回路XML: C:\Users\<USER>\Desktop\Python\EWReborn\01C_Wiring Typical\DO NIS WET SIL3RY.xml
2025-08-05 13:06:58,923 - utils.xml_parser - INFO - 成功解析典型回路: DO NIS WET SIL3RY
2025-08-05 13:06:59,026 - core.data_loader_simple - INFO - 典型回路文件加载成功
2025-08-05 13:06:59,074 - core.data_loader_simple - INFO - 数据加载完成
2025-08-05 13:06:59,171 - core.data_loader_simple - INFO - 创建数据模型对象
2025-08-05 13:06:59,176 - core.data_loader_simple - INFO - 数据模型创建完成: 0个I/O点, 0条电缆, 5个机柜, 10个典型回路
2025-08-05 13:06:59,274 - core.validator - INFO - 开始IODB数据验证
2025-08-05 13:06:59,306 - core.validator - INFO - 验证0个I/O点和0条电缆
2025-08-05 13:06:59,359 - core.validator - INFO - Tag唯一性验证通过
2025-08-05 13:06:59,370 - core.validator - INFO - 电缆配对验证通过
2025-08-05 13:06:59,390 - core.validator - INFO - Cable属性一致性验证通过
2025-08-05 13:06:59,422 - core.validator - INFO - IODB数据验证通过
2025-08-05 13:06:59,452 - core.allocator - INFO - 开始I/O点分配，电缆数: 0, 机柜数: 5
2025-08-05 13:06:59,453 - core.allocator - INFO - 总计需要分配 0 个I/O点
2025-08-05 13:06:59,455 - core.allocator - INFO - I/O点分配完成，成功率: 0.0%
2025-08-05 13:06:59,455 - core.allocator - INFO - 成功分配: 0, 失败: 0
2025-08-05 13:07:00,527 - gui.material_theme - INFO - Material主题管理器初始化完成
2025-08-05 13:07:00,652 - gui.allocation_widget - INFO - Material Design增强应用成功
2025-08-05 13:07:00,654 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-08-05 13:07:00,655 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-08-05 13:07:00,860 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-08-05 13:07:00,870 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-08-05 13:07:01,056 - gui.config_widget - INFO - 配置界面初始化完成
2025-08-05 13:07:01,532 - gui.main_window - INFO - 主题已更改为: dark_teal.xml
2025-08-05 13:07:01,533 - gui.material_theme - INFO - 主题应用成功: dark_teal.xml
2025-08-05 13:07:01,533 - gui.main_window - INFO - Material主题应用成功: dark_teal.xml
2025-08-05 13:07:01,534 - gui.main_window - INFO - 主窗口初始化完成
2025-08-05 13:07:01,562 - gui.allocation_widget - INFO - Material Design增强应用成功
2025-08-05 13:07:01,562 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-08-05 13:07:01,563 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-08-05 13:07:01,572 - gui.progress_widget - INFO - 进度组件苹果风格增强应用成功
2025-08-05 13:07:01,573 - gui.progress_widget - INFO - 进度显示组件初始化完成
2025-08-05 14:28:26,931 - gui.main_window - INFO - 用户确认退出应用程序
2025-08-05 14:28:26,991 - __main__ - INFO - 应用程序退出，退出码: 0
2025-08-05 14:28:29,923 - __main__ - INFO - EWReborn应用程序启动
2025-08-05 14:28:31,082 - gui.material_theme - INFO - Material主题管理器初始化完成
2025-08-05 14:28:31,242 - gui.allocation_widget - INFO - Material Design增强应用成功
2025-08-05 14:28:31,243 - gui.allocation_widget - INFO - 示例数据初始化完成
2025-08-05 14:28:31,247 - gui.allocation_widget - INFO - I/O分配界面初始化完成
2025-08-05 14:28:31,252 - gui.xml_editor_widget - INFO - XML编辑器模块路径已添加
2025-08-05 14:28:31,252 - gui.xml_editor_widget - INFO - XML编辑器组件初始化完成
2025-08-05 14:28:31,268 - gui.config_widget - INFO - 配置界面初始化完成
2025-08-05 14:28:31,769 - gui.main_window - INFO - 主题已更改为: dark_teal.xml
2025-08-05 14:28:31,769 - gui.material_theme - INFO - 主题应用成功: dark_teal.xml
2025-08-05 14:28:31,770 - gui.main_window - INFO - Material主题应用成功: dark_teal.xml
2025-08-05 14:28:31,770 - gui.main_window - INFO - 主窗口初始化完成
2025-08-05 14:28:31,771 - __main__ - INFO - 主窗口创建成功
2025-08-05 14:28:37,686 - __main__ - INFO - 主窗口显示完成
2025-08-05 14:29:47,340 - gui.main_window - INFO - 用户确认退出应用程序
2025-08-05 14:29:47,398 - __main__ - INFO - 应用程序退出，退出码: 0
