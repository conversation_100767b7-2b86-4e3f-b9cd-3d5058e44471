# xml_operations.py
import xml.etree.ElementTree as ET
import os
import copy # 用于深拷贝
import traceback # 用于打印完整的错误堆栈

class XMLOperationError(Exception):
    """自定义XML操作错误类。"""
    pass

def load_xml_to_elementtree(file_path):
    """
    将XML文件加载为ElementTree对象。

    参数:
        file_path (str): XML文件的路径。

    返回:
        ET.ElementTree: 解析后的XML树对象。

    异常:
        FileNotFoundError: 如果文件未找到。
        XMLOperationError: 如果文件无效或发生其他加载错误。
    """
    try:
        # 确保使用UTF-8编码解析
        parser = ET.XMLParser(encoding="utf-8")
        tree = ET.parse(file_path, parser=parser)
        return tree
    except ET.ParseError as e:
        raise XMLOperationError(f"无效的XML文件: {file_path}。错误: {e}")
    except FileNotFoundError:
        raise FileNotFoundError(f"XML文件未找到: {file_path}")
    except Exception as e:
        raise XMLOperationError(f"加载XML文件 '{file_path}' 时发生意外错误: {e}")

def _manual_indent_xml_tree(element, level=0, indent_space="  "):
    """
    手动缩进 XML 树 (ElementTree.Element)。
    这是 Python 3.9 之前版本中 ET.indent 不可用的回退方案。
    """
    i = "\n" + level * indent_space
    if len(element):  # 如果元素有子节点
        if not element.text or not element.text.strip():
            element.text = i + indent_space
        if not element.tail or not element.tail.strip():
            element.tail = i
        for idx, subelement in enumerate(element):
            _manual_indent_xml_tree(subelement, level + 1, indent_space)
        # 确保最后一个子元素的尾部是正确的
        if element[-1].tail is None or not element[-1].tail.strip():
             element[-1].tail = i # 确保父结束标签在新行并缩进
    else:  # 如果元素没有子节点
        if level and (not element.tail or not element.tail.strip()):
            element.tail = i

def get_unique_filepath(filepath):
    """
    如果文件已存在，则生成一个带数字后缀的唯一文件名。
    例如：如果 'file.xml' 存在，尝试 'file_1.xml', 'file_2.xml' 等。
    返回最终确定的、唯一的文件路径。
    """
    if not os.path.exists(filepath):
        return filepath # 文件不存在，直接返回原路径

    base, ext = os.path.splitext(filepath)
    counter = 1
    # 检查是否有现有的数字后缀，例如 'file_1.xml' -> base='file_1', ext='.xml'
    # 如果base以 '_数字' 结尾，则从这个数字开始计数
    parts = base.rsplit('_', 1)
    if len(parts) == 2 and parts[1].isdigit():
        base = parts[0]
        counter = int(parts[1]) + 1 # 从现有后缀的下一个数字开始

    new_filepath = f"{base}_{counter}{ext}"
    while os.path.exists(new_filepath):
        counter += 1
        new_filepath = f"{base}_{counter}{ext}"
    return new_filepath


def save_elementtree_to_xml(tree, file_path, create_subdir=True, overwrite_prompt_func=None):
    """
    将ElementTree对象保存到XML文件。
    新增 overwrite_prompt_func 参数用于处理文件已存在的情况。

    参数:
        tree (ET.ElementTree): 要保存的XML树对象。
        file_path (str): 保存XML文件的目标路径。
        create_subdir (bool): 如果为True且目录不存在，则创建它。
        overwrite_prompt_func (callable, optional): 一个函数，当文件已存在时调用。
            该函数应接收原始 file_path 作为参数，并返回最终要保存的文件路径。
            如果返回 None，则表示取消保存。
            如果未提供，则默认覆盖。

    返回:
        str: 最终保存的文件路径，如果取消则返回 None。

    异常:
        XMLOperationError: 如果发生IO错误或其它保存错误。
    """
    final_file_path = file_path

    if os.path.exists(file_path):
        if overwrite_prompt_func:
            final_file_path = overwrite_prompt_func(file_path)
            if final_file_path is None: # 用户选择取消
                print(f"[xml_operations] 用户取消保存文件: {file_path}")
                return None # 表示取消保存
        # else: 如果没有提供 overwrite_prompt_func，则默认行为是覆盖（不做任何操作，final_file_path 保持不变）


    try:
        print(f"[xml_operations] 尝试保存到: {final_file_path}")
        if create_subdir:
            directory = os.path.dirname(final_file_path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                print(f"[xml_operations] 创建目录: {directory}")

        root = tree.getroot()
        if hasattr(ET, 'indent'):
            ET.indent(tree, space="  ", level=0)
            print("[xml_operations] 使用 ET.indent() 进行格式化。")
        else:
            _manual_indent_xml_tree(root)
            print("[xml_operations] 使用 _manual_indent_xml_tree() 进行格式化。")

        try:
            xml_content_snippet_bytes = ET.tostring(root, encoding='utf-8')
            xml_content_snippet = xml_content_snippet_bytes.decode('utf-8', errors='ignore')[:500]
            print(f"[xml_operations] 文件 '{os.path.basename(final_file_path)}' 的 XML 内容片段:\n{xml_content_snippet}...")
        except Exception as e_tostring:
            print(f"[xml_operations] 无法生成用于调试片段的 XML 字符串: {e_tostring}")

        tree.write(final_file_path, encoding='utf-8', xml_declaration=True)
        print(f"[xml_operations] 成功写入到: {final_file_path}")
        return final_file_path # 返回实际保存的路径
    except IOError as e:
        print(f"[xml_operations] 保存到 '{final_file_path}' 时发生 IOError: {e}")
        raise XMLOperationError(f"写入文件 '{final_file_path}' 时发生IO错误: {e}")
    except Exception as e:
        print(f"[xml_operations] 保存到 '{final_file_path}' 时发生通用异常: {e}")
        traceback.print_exc()
        raise XMLOperationError(f"保存XML到 '{final_file_path}' 时发生意外错误: {e}")


def create_new_profile(root_tag="ProfileRoot", attributes=None, sub_elements_config=None):
    """
    创建一个新的XML ElementTree 实例 (profile)。
    """
    root = ET.Element(root_tag)
    if attributes:
        for key, value in attributes.items():
            root.set(key, str(value))

    if sub_elements_config:
        for config in sub_elements_config:
            tag = config.get('tag')
            if not tag:
                continue

            sub_element_attrs = config.get('attributes', {})
            sub_element_text = config.get('text')

            sub_element = ET.SubElement(root, tag)
            for attr_key, attr_val in sub_element_attrs.items():
                sub_element.set(attr_key, str(attr_val))
            if sub_element_text is not None:
                sub_element.text = str(sub_element_text)

    return ET.ElementTree(root)

def deep_copy_element(element):
    """
    深拷贝一个XML元素。
    """
    return copy.deepcopy(element)

def add_element_to_tree(parent_element, tag, text=None, attributes=None, position=None):
    """
    在父元素下添加一个新元素，可以指定插入位置。
    """
    new_element = ET.Element(tag)
    if text is not None:
        new_element.text = str(text)
    if attributes:
        for key, value in attributes.items():
            new_element.set(key, str(value))

    try:
        if position is not None and 0 <= position <= len(list(parent_element)):
            parent_element.insert(position, new_element)
        else:
            parent_element.append(new_element)
    except Exception as e:
        raise XMLOperationError(f"向元素 <{parent_element.tag}> 添加子元素 <{tag}> 时失败: {e}")
    return new_element

def remove_element_from_tree(parent_element, element_to_remove):
    """
    从父元素中移除指定的子元素。
    """
    try:
        parent_element.remove(element_to_remove)
    except ValueError: # 如果 element_to_remove 不是 parent_element 的直接子元素
        raise XMLOperationError(f"元素 <{element_to_remove.tag}> 不是父元素 <{parent_element.tag}> 的直接子元素，无法移除。")
    except Exception as e:
        raise XMLOperationError(f"移除元素 <{element_to_remove.tag}> 时发生意外错误: {e}")


def modify_element_in_tree(element, new_text=None, new_attributes=None, new_tag=None, clear_existing_attributes=False):
    """
    修改现有元素的标签、文本或属性。
    """
    if new_tag is not None and element.tag != new_tag:
        element.tag = new_tag
    if new_text is not None:
        element.text = str(new_text)
    if new_attributes is not None:
        if clear_existing_attributes:
            element.attrib.clear()
        for key, value in new_attributes.items():
            if value is None and not clear_existing_attributes: # 如果值为 None 且不清除现有属性，则删除该属性
                if key in element.attrib:
                    del element.attrib[key]
            elif value is not None: # 否则，设置新值（或更新现有值）
                element.set(key, str(value))

def get_element_identifier(element, preferred_attrs=None):
    """
    获取元素的唯一标识符（如果存在）。
    会按 preferred_attrs 列表中的顺序尝试获取属性值。
    """
    if element is None: return None
    if preferred_attrs is None: preferred_attrs = ["Name", "id", "ID"] # 默认尝试的属性名
    for attr_name in preferred_attrs:
        identifier = element.get(attr_name)
        if identifier is not None: return identifier
    return None # 如果所有优选属性都不存在，则返回None

def find_element_parent_and_index(root_element_of_tree, element_to_find):
    """
    在给定的树中查找element_to_find，返回其父元素和它在父元素子列表中的索引。
    如果 element_to_find 是根元素，或者未找到，则返回 (None, -1)。
    """
    if root_element_of_tree is None or element_to_find is None or root_element_of_tree == element_to_find:
        return None, -1 # 根元素没有父级，或输入无效
    for parent_candidate in root_element_of_tree.iter(): # 遍历树中所有元素
        try:
            children = list(parent_candidate) # 获取当前候选父元素的子元素列表
            if element_to_find in children: # 检查目标元素是否是其子元素
                return parent_candidate, children.index(element_to_find)
        except (TypeError, ValueError):
             # .iter() 可能会产生非元素节点（如注释），list()会失败，忽略它们
             continue
    return None, -1 # 如果遍历完整个树都未找到

def element_to_string(element, encoding='unicode'):
    """
    将 ET.Element 对象转换为 XML 字符串。
    encoding='unicode' 返回字符串，encoding='utf-8' 返回字节串。
    xml_declaration=True 仅在 encoding 为 'utf-8' 等字节编码时有意义。
    """
    return ET.tostring(element, encoding=encoding, xml_declaration=(encoding.lower() == 'utf-8'))


def string_to_element(xml_string):
    """
    将 XML 字符串或字节串转换为 ET.Element 对象。
    """
    try:
        if isinstance(xml_string, bytes):
             # 如果是字节串，假设是utf-8编码 (通常XML文件或网络传输会是字节串)
             parser = ET.XMLParser(encoding='utf-8')
             return ET.fromstring(xml_string, parser=parser)
        else:
             # 如果是字符串，直接解析 (通常内部处理或从文本框获取的是字符串)
             return ET.fromstring(xml_string)
    except ET.ParseError as e:
        raise XMLOperationError(f"从字符串解析XML失败: {e}")
    except Exception as e:
        raise XMLOperationError(f"解析XML字符串时发生意外错误: {e}")


if __name__ == '__main__':
    # --- 以下为模块的示例用法和测试代码 ---
    print("--- XML操作模块测试 ---")

    # 准备测试文件路径
    test_dir = "xml_test_files_chinese_test_v2" # 测试目录名
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    sample_file_path = os.path.join(test_dir, '测试Profile.xml') # 测试文件名
    new_save_path = os.path.join(test_dir, '测试Profile_已保存.xml') # 测试文件名
    created_profile_path = os.path.join(test_dir, '新创建的Profile.xml') # 测试文件名

    # 0. 测试 get_unique_filepath
    print("\n0. 测试 get_unique_filepath:")
    # 先创建一些文件用于测试唯一性
    with open(os.path.join(test_dir, "unique_test.xml"), "w") as f: f.write("<test/>")
    with open(os.path.join(test_dir, "unique_test_1.xml"), "w") as f: f.write("<test/>")
    print(f"  unique_test.xml -> {get_unique_filepath(os.path.join(test_dir, 'unique_test.xml'))}")
    print(f"  unique_test_new.xml -> {get_unique_filepath(os.path.join(test_dir, 'unique_test_new.xml'))}")
    print(f"  unique_test_1.xml (should become _2) -> {get_unique_filepath(os.path.join(test_dir, 'unique_test_1.xml'))}")
    print(f"  unique_test_with_number_5.xml -> {get_unique_filepath(os.path.join(test_dir, 'unique_test_with_number_5.xml'))}")
    if os.path.exists(os.path.join(test_dir, "unique_test_with_number_5.xml")):
         with open(os.path.join(test_dir, "unique_test_with_number_5_1.xml"), "w") as f: f.write("<test/>")
         print(f"  unique_test_with_number_5.xml (again, should be _5_2 or similar if _5_1 exists) -> {get_unique_filepath(os.path.join(test_dir, 'unique_test_with_number_5.xml'))}")


    # 1. 测试创建新Profile
    print("\n1. 测试创建新Profile:")
    try:
        new_attrs = {"Name": "我的测试Profile", "Version": "1.1", "Type": "示例"}
        new_subs = [
            {"tag": "设置A", "text": "值A", "attributes": {"单位": "秒"}},
            {"tag": "设置B", "attributes": {"启用": "true"}},
            {"tag": "空元素"},
            {"tag": "列表项", "text":"条目1"},
            {"tag": "列表项", "text":"条目2"},
            {"tag": "Components"}, # 添加 Components 标签用于测试 find_element_parent_and_index
        ]
        new_tree = create_new_profile("根配置", attributes=new_attrs, sub_elements_config=new_subs)
        root = new_tree.getroot()
        # 在 Components 标签下添加一个子组件用于测试
        components_node = root.find("Components")
        if components_node is not None:
            child_comp = ET.SubElement(components_node, "ProfileComponent", {"Name":"内部组件"}) # 中文名称测试
            grand_child_comp_container = ET.SubElement(child_comp, "Components") # 嵌套 Components 容器
            ET.SubElement(grand_child_comp_container, "ProfileComponent", {"Name":"深层内部组件"}) # 中文名称测试


        print(f"  新Profile根元素: <{root.tag}>, 属性: {root.attrib}")
        for child in root:
            print(f"    子元素: <{child.tag}>, 属性: {child.attrib}, 文本: '{child.text}'")

        # 测试保存 (不带overwrite_prompt_func，即默认覆盖)
        save_elementtree_to_xml(new_tree, created_profile_path)
        print(f"  新Profile已保存到 (默认覆盖): {created_profile_path}")

        # 测试保存 (带overwrite_prompt_func)
        def mock_prompt_overwrite(original_path):
            print(f"    [MOCK PROMPT] 文件 '{original_path}' 已存在。")
            user_choice = input("    输入 'o' 覆盖, 's' 跳过并用后缀, 'c' 取消: ").lower()
            if user_choice == 'o':
                return original_path
            elif user_choice == 's':
                return get_unique_filepath(original_path)
            else: # 'c' or other
                return None

        # 再次保存，这次应该会触发 mock_prompt_overwrite
        final_saved_path = save_elementtree_to_xml(new_tree, created_profile_path, overwrite_prompt_func=mock_prompt_overwrite)
        if final_saved_path:
            print(f"  新Profile通过prompt机制保存到: {final_saved_path}")
        else:
            print(f"  新Profile通过prompt机制被取消保存。")


    except Exception as e:
        print(f"  创建新Profile时出错: {e}")
        traceback.print_exc()


    # 2. 测试加载XML (确保路径是实际存在的)
    print("\n2. 测试加载XML:")
    path_to_load = None
    if os.path.exists(created_profile_path): # 优先加载上一步可能创建的文件
        path_to_load = created_profile_path
    elif os.path.exists(os.path.join(test_dir, "unique_test.xml")): # 后备
        path_to_load = os.path.join(test_dir, "unique_test.xml")


    if not path_to_load:
        # 如果前面所有文件都不存在，创建一个临时的简单文件
        temp_root = ET.Element("临时根")
        ET.SubElement(temp_root, "临时子项", id="001").text = "临时文本"
        temp_tree_for_load_test = ET.ElementTree(temp_root)
        path_to_load = sample_file_path
        save_elementtree_to_xml(temp_tree_for_load_test, path_to_load) # 使用我们的保存函数
        print(f"  (为加载测试创建了临时文件: {path_to_load})")


    loaded_tree = None
    try:
        loaded_tree = load_xml_to_elementtree(path_to_load)
        print(f"  成功从 '{path_to_load}' 加载XML。")
        if loaded_tree:
            print(f"  加载的根元素: <{loaded_tree.getroot().tag}>, 属性: {loaded_tree.getroot().attrib}")
    except Exception as e:
        print(f"  加载XML时出错: {e}")
        traceback.print_exc()


    if loaded_tree:
        root = loaded_tree.getroot()
        # 测试 find_element_parent_and_index
        print("\n测试 find_element_parent_and_index:")
        inner_comp = root.find(".//ProfileComponent[@Name='内部组件']")
        if inner_comp is not None:
            parent, index = find_element_parent_and_index(root, inner_comp)
            if parent is not None:
                print(f"  元素 <{inner_comp.tag} Name='{inner_comp.get('Name')}'> 已找到。")
                print(f"    父级: <{parent.tag}>, 索引: {index}")
                assert parent.tag == "Components"
                assert list(parent)[index] == inner_comp
            else:
                print(f"  未找到 <{inner_comp.tag} Name='{inner_comp.get('Name')}'> 的父级或它是根元素。")
        else:
            print("  未找到 '内部组件' 进行 find_element_parent_and_index 测试 (可能在上一步保存时被跳过或重命名)。")

        deep_inner_comp = root.find(".//ProfileComponent[@Name='深层内部组件']")
        if deep_inner_comp is not None:
            parent, index = find_element_parent_and_index(root, deep_inner_comp)
            if parent is not None:
                print(f"  元素 <{deep_inner_comp.tag} Name='{deep_inner_comp.get('Name')}'> 已找到。")
                print(f"    父级: <{parent.tag}>, 索引: {index}")
                grandparent, _ = find_element_parent_and_index(root, parent) # parent of "Components"
                assert parent.tag == "Components"
                assert grandparent is not None and grandparent.get("Name") == "内部组件"
                assert list(parent)[index] == deep_inner_comp
            else:
                print(f"  未找到 <{deep_inner_comp.tag} Name='{deep_inner_comp.get('Name')}'> 的父级或它是根元素。")
        else:
            print("  未找到 '深层内部组件' 进行测试 (可能在上一步保存时其父组件被跳过或重命名)。")


        # 测试 element_to_string 和 string_to_element
        print("\n测试 element_to_string 和 string_to_element:")
        element_for_str_test = root.find(".//ProfileComponent") # 找任意一个 ProfileComponent
        if element_for_str_test is None: element_for_str_test = root # 如果没有，就用根
        
        if element_for_str_test is not None:
            try:
                xml_str_bytes = element_to_string(element_for_str_test, encoding='utf-8') # 获取字节串
                xml_str = xml_str_bytes.decode('utf-8') # 解码为字符串以打印
                print(f"  元素 '{element_for_str_test.get('Name', element_for_str_test.tag)}' 的 XML 字符串:\n{xml_str[:200]}...")
                reparsed_element = string_to_element(xml_str_bytes) # 从字节串解析
                assert reparsed_element.tag == element_for_str_test.tag
                # assert reparsed_element.get("Name") == element_for_str_test.get("Name") # Name可能不存在
                print(f"  成功重新解析: <{reparsed_element.tag} Name='{reparsed_element.get('Name', '')}'>")

            except XMLOperationError as e:
                 print(f"  XML 字符串转换/解析测试失败: {e}")
                 traceback.print_exc()
        else:
            print("  未找到元素进行字符串转换测试。")

        # --- 元素操作测试 ---
        print("\n3. 测试元素操作:")
        # ... (这部分测试与之前类似，可以保持)
        if root.find(".//设置A") is not None: # 确保测试元素存在
            # 3a. 添加元素
            print("  3a. 添加元素:")
            try:
                added_el = add_element_to_tree(root, "新添加的元素", text="这是内容", attributes={"状态":"测试中"}, position=1)
                print(f"    已添加: <{added_el.tag}>, 父级: <{root.tag}>, 位置: 1")
                add_element_to_tree(added_el, "嵌套子元素", text="嵌套文本")
                print(f"    已添加嵌套子元素到 <{added_el.tag}>")
            except Exception as e:
                print(f"    添加元素时出错: {e}")
                traceback.print_exc()

            # 3b. 获取标识符
            print("\n  3b. 获取元素标识符:")
            id_el = root.find(".//*[@Name='我的测试Profile']")
            if id_el is None and root.get("Name") == "我的测试Profile": id_el = root
            identifier = get_element_identifier(id_el)
            print(f"    元素 <{id_el.tag if id_el is not None else '未知'}> 的标识符 (Name, id, ID): '{identifier}'")
            no_id_el = root.find(".//空元素")
            identifier_none = get_element_identifier(no_id_el)
            print(f"    元素 <{no_id_el.tag if no_id_el is not None else '未知'}> 的标识符: '{identifier_none}'")


            # 3c. 修改元素
            print("\n  3c. 修改元素:")
            element_to_modify = root.find(".//设置A")
            if element_to_modify is not None:
                print(f"    修改前 <{element_to_modify.tag}>: 属性={element_to_modify.attrib}, 文本='{element_to_modify.text}'")
                modify_element_in_tree(element_to_modify,
                                    new_text="新值A",
                                    new_attributes={"单位": "分钟", "新属性":"新值"},
                                    new_tag="修改后设置A")
                print(f"    修改后 <{element_to_modify.tag}>: 属性={element_to_modify.attrib}, 文本='{element_to_modify.text}'")
            else:
                print("    未找到 '设置A' 元素进行修改测试。")

            # 3d. 深拷贝元素
            if element_to_modify: # 现在 element_to_modify 是 "修改后设置A"
                copied_el = deep_copy_element(element_to_modify)
                copied_el.set("拷贝标记", "是")
                print(f"    已深拷贝并修改 <{copied_el.tag}>: 属性={copied_el.attrib} (原始 <{element_to_modify.tag}> 不应改变)")


            # 3e. 移除元素
            print("\n  3e. 移除元素:")
            element_to_remove = root.find(".//列表项[text()='条目1']")
            if element_to_remove is not None:
                parent_of_removed, _ = find_element_parent_and_index(root, element_to_remove)
                if parent_of_removed is not None:
                    try:
                        remove_element_from_tree(parent_of_removed, element_to_remove)
                        print(f"    已从 <{parent_of_removed.tag}> 移除元素 <{element_to_remove.tag} Text='{element_to_remove.text}'>")
                    except Exception as e:
                        print(f"    移除元素时出错: {e}")
                        traceback.print_exc()
                else:
                    print(f"    未找到 <{element_to_remove.tag}> 的父元素，无法测试移除。")
            else:
                print("    未找到要移除的 '列表项' 元素。")
        else:
            print("   跳过部分元素操作测试，因为依赖的元素 '设置A' 不存在。")


        # 4. 测试保存修改后的XML
        print("\n4. 测试保存修改后的XML:")
        try:
            # 使用 mock_prompt_overwrite 进行保存测试
            final_saved_path = save_elementtree_to_xml(loaded_tree, new_save_path, overwrite_prompt_func=mock_prompt_overwrite)
            if final_saved_path:
                print(f"  修改后的XML已保存到: {final_saved_path}")
                print(f"  请检查文件 '{final_saved_path}' 和其他可能生成的文件。")
            else:
                print(f"  修改后的XML被取消保存。")
        except Exception as e:
            print(f"  保存修改后的XML时出错: {e}")
            traceback.print_exc()

    print("\n--- XML操作模块测试结束 ---")