"""
约束求解器
提供复杂约束求解和优化算法
"""

import logging
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass
from enum import Enum
from abc import ABC, abstractmethod

from core.logger import get_logger
from core.data_models import IOPoint, SignalType


class ConstraintType(Enum):
    """约束类型"""
    CAPACITY = "CAPACITY"           # 容量约束
    COMPATIBILITY = "COMPATIBILITY" # 兼容性约束
    SEPARATION = "SEPARATION"       # 分离约束
    GROUPING = "GROUPING"          # 分组约束
    DISTANCE = "DISTANCE"          # 距离约束
    PRIORITY = "PRIORITY"          # 优先级约束


class ConstraintOperator(Enum):
    """约束操作符"""
    EQUAL = "EQUAL"
    NOT_EQUAL = "NOT_EQUAL"
    LESS_THAN = "LESS_THAN"
    GREATER_THAN = "GREATER_THAN"
    LESS_EQUAL = "LESS_EQUAL"
    GREATER_EQUAL = "GREATER_EQUAL"
    IN = "IN"
    NOT_IN = "NOT_IN"


@dataclass
class Constraint:
    """约束定义"""
    id: str
    constraint_type: ConstraintType
    operator: ConstraintOperator
    target_attribute: str
    constraint_value: Any
    priority: int = 1
    description: str = ""
    is_hard: bool = True  # 硬约束或软约束
    penalty_weight: float = 1.0  # 软约束的惩罚权重


@dataclass
class Variable:
    """决策变量"""
    id: str
    name: str
    domain: List[Any]
    current_value: Any = None
    is_assigned: bool = False


@dataclass
class ConstraintViolation:
    """约束违反"""
    constraint_id: str
    variable_id: str
    violation_type: str
    severity: float
    description: str


@dataclass
class SolutionState:
    """求解状态"""
    variables: Dict[str, Variable]
    violations: List[ConstraintViolation]
    objective_value: float = 0.0
    is_feasible: bool = False
    iteration_count: int = 0


class ConstraintChecker:
    """约束检查器"""
    
    def __init__(self):
        """初始化约束检查器"""
        self.logger = get_logger(__name__)
    
    def check_constraint(self, constraint: Constraint, 
                        variables: Dict[str, Variable]) -> Optional[ConstraintViolation]:
        """
        检查约束是否满足
        
        Args:
            constraint: 约束
            variables: 变量字典
            
        Returns:
            约束违反对象或None
        """
        try:
            if constraint.constraint_type == ConstraintType.CAPACITY:
                return self._check_capacity_constraint(constraint, variables)
            elif constraint.constraint_type == ConstraintType.COMPATIBILITY:
                return self._check_compatibility_constraint(constraint, variables)
            elif constraint.constraint_type == ConstraintType.SEPARATION:
                return self._check_separation_constraint(constraint, variables)
            elif constraint.constraint_type == ConstraintType.GROUPING:
                return self._check_grouping_constraint(constraint, variables)
            elif constraint.constraint_type == ConstraintType.DISTANCE:
                return self._check_distance_constraint(constraint, variables)
            elif constraint.constraint_type == ConstraintType.PRIORITY:
                return self._check_priority_constraint(constraint, variables)
            else:
                self.logger.warning(f"未知约束类型: {constraint.constraint_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"检查约束 {constraint.id} 失败: {e}")
            return None
    
    def _check_capacity_constraint(self, constraint: Constraint, 
                                  variables: Dict[str, Variable]) -> Optional[ConstraintViolation]:
        """检查容量约束"""
        # 简化的容量约束检查
        target_var = variables.get(constraint.target_attribute)
        if not target_var or not target_var.is_assigned:
            return None
        
        current_capacity = target_var.current_value
        max_capacity = constraint.constraint_value
        
        if constraint.operator == ConstraintOperator.LESS_EQUAL:
            if current_capacity > max_capacity:
                return ConstraintViolation(
                    constraint_id=constraint.id,
                    variable_id=target_var.id,
                    violation_type="CAPACITY_EXCEEDED",
                    severity=(current_capacity - max_capacity) / max_capacity,
                    description=f"容量超限: {current_capacity} > {max_capacity}"
                )
        
        return None
    
    def _check_compatibility_constraint(self, constraint: Constraint, 
                                       variables: Dict[str, Variable]) -> Optional[ConstraintViolation]:
        """检查兼容性约束"""
        # 简化的兼容性约束检查
        target_var = variables.get(constraint.target_attribute)
        if not target_var or not target_var.is_assigned:
            return None
        
        compatible_values = constraint.constraint_value
        current_value = target_var.current_value
        
        if constraint.operator == ConstraintOperator.IN:
            if current_value not in compatible_values:
                return ConstraintViolation(
                    constraint_id=constraint.id,
                    variable_id=target_var.id,
                    violation_type="INCOMPATIBLE_VALUE",
                    severity=1.0,
                    description=f"不兼容的值: {current_value} 不在 {compatible_values} 中"
                )
        
        return None
    
    def _check_separation_constraint(self, constraint: Constraint, 
                                    variables: Dict[str, Variable]) -> Optional[ConstraintViolation]:
        """检查分离约束"""
        # 简化的分离约束检查
        return None
    
    def _check_grouping_constraint(self, constraint: Constraint, 
                                  variables: Dict[str, Variable]) -> Optional[ConstraintViolation]:
        """检查分组约束"""
        # 简化的分组约束检查
        return None
    
    def _check_distance_constraint(self, constraint: Constraint, 
                                  variables: Dict[str, Variable]) -> Optional[ConstraintViolation]:
        """检查距离约束"""
        # 简化的距离约束检查
        return None
    
    def _check_priority_constraint(self, constraint: Constraint, 
                                  variables: Dict[str, Variable]) -> Optional[ConstraintViolation]:
        """检查优先级约束"""
        # 简化的优先级约束检查
        return None


class ObjectiveFunction:
    """目标函数"""
    
    def __init__(self, weights: Dict[str, float]):
        """
        初始化目标函数
        
        Args:
            weights: 权重字典
        """
        self.weights = weights
        self.logger = get_logger(__name__)
    
    def evaluate(self, solution_state: SolutionState) -> float:
        """
        评估目标函数值
        
        Args:
            solution_state: 求解状态
            
        Returns:
            目标函数值
        """
        try:
            total_score = 0.0
            
            # 计算约束违反惩罚
            violation_penalty = self._calculate_violation_penalty(solution_state.violations)
            total_score -= violation_penalty * self.weights.get('violation_penalty', 1.0)
            
            # 计算资源利用率奖励
            utilization_reward = self._calculate_utilization_reward(solution_state.variables)
            total_score += utilization_reward * self.weights.get('utilization_reward', 0.5)
            
            # 计算分配效率奖励
            efficiency_reward = self._calculate_efficiency_reward(solution_state.variables)
            total_score += efficiency_reward * self.weights.get('efficiency_reward', 0.3)
            
            return total_score
            
        except Exception as e:
            self.logger.error(f"评估目标函数失败: {e}")
            return float('-inf')
    
    def _calculate_violation_penalty(self, violations: List[ConstraintViolation]) -> float:
        """计算约束违反惩罚"""
        penalty = 0.0
        for violation in violations:
            penalty += violation.severity
        return penalty
    
    def _calculate_utilization_reward(self, variables: Dict[str, Variable]) -> float:
        """计算资源利用率奖励"""
        # 简化的利用率计算
        assigned_count = sum(1 for var in variables.values() if var.is_assigned)
        total_count = len(variables)
        
        if total_count > 0:
            return assigned_count / total_count
        return 0.0
    
    def _calculate_efficiency_reward(self, variables: Dict[str, Variable]) -> float:
        """计算分配效率奖励"""
        # 简化的效率计算
        return 0.8  # 固定值，实际应该基于具体的效率指标


class ConstraintSolver:
    """约束求解器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化约束求解器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = get_logger(__name__)
        
        # 初始化组件
        self.constraint_checker = ConstraintChecker()
        
        # 求解参数
        self.max_iterations = config.get('constraint_solver', {}).get('max_iterations', 1000)
        self.tolerance = config.get('constraint_solver', {}).get('tolerance', 1e-6)
        
        # 目标函数权重
        weights = config.get('constraint_solver', {}).get('objective_weights', {})
        default_weights = {
            'violation_penalty': 1.0,
            'utilization_reward': 0.5,
            'efficiency_reward': 0.3
        }
        default_weights.update(weights)
        self.objective_function = ObjectiveFunction(default_weights)
        
        self.logger.info("约束求解器初始化完成")
    
    def solve(self, io_points: List[IOPoint], 
             resources: Dict[str, Any], 
             constraints: List[Constraint]) -> SolutionState:
        """
        求解约束满足问题
        
        Args:
            io_points: I/O点列表
            resources: 资源字典
            constraints: 约束列表
            
        Returns:
            求解状态
        """
        self.logger.info(f"开始求解约束满足问题，I/O点数: {len(io_points)}, 约束数: {len(constraints)}")
        
        try:
            # 初始化变量
            variables = self._initialize_variables(io_points, resources)
            
            # 初始化求解状态
            solution_state = SolutionState(
                variables=variables,
                violations=[],
                objective_value=float('-inf'),
                is_feasible=False,
                iteration_count=0
            )
            
            # 执行求解
            best_solution = self._solve_iteratively(solution_state, constraints)
            
            self.logger.info(f"约束求解完成，迭代次数: {best_solution.iteration_count}, "
                           f"目标值: {best_solution.objective_value:.4f}")
            
            return best_solution
            
        except Exception as e:
            self.logger.error(f"约束求解异常: {e}")
            return SolutionState(variables={}, violations=[], is_feasible=False)
    
    def _initialize_variables(self, io_points: List[IOPoint], 
                             resources: Dict[str, Any]) -> Dict[str, Variable]:
        """
        初始化决策变量
        
        Args:
            io_points: I/O点列表
            resources: 资源字典
            
        Returns:
            变量字典
        """
        variables = {}
        
        # 为每个I/O点创建分配变量
        for i, io_point in enumerate(io_points):
            var_id = f"io_point_{i}"
            
            # 构建可选资源域
            domain = []
            for resource_type, resource_list in resources.items():
                for resource in resource_list:
                    if isinstance(resource, dict):
                        domain.append(resource.get('name', f'{resource_type}_{len(domain)}'))
            
            variable = Variable(
                id=var_id,
                name=f"allocation_{io_point.tag}",
                domain=domain,
                current_value=None,
                is_assigned=False
            )
            
            variables[var_id] = variable
        
        return variables
    
    def _solve_iteratively(self, initial_state: SolutionState, 
                          constraints: List[Constraint]) -> SolutionState:
        """
        迭代求解
        
        Args:
            initial_state: 初始状态
            constraints: 约束列表
            
        Returns:
            最优解状态
        """
        current_state = initial_state
        best_state = initial_state
        best_objective = float('-inf')
        
        for iteration in range(self.max_iterations):
            current_state.iteration_count = iteration
            
            # 尝试改进当前解
            improved_state = self._improve_solution(current_state, constraints)
            
            # 评估目标函数
            objective_value = self.objective_function.evaluate(improved_state)
            improved_state.objective_value = objective_value
            
            # 更新最优解
            if objective_value > best_objective:
                best_objective = objective_value
                best_state = improved_state
                
                # 检查收敛条件
                if abs(objective_value - best_objective) < self.tolerance:
                    self.logger.info(f"在第 {iteration} 次迭代收敛")
                    break
            
            current_state = improved_state
        
        # 检查可行性
        best_state.is_feasible = len(best_state.violations) == 0
        
        return best_state
    
    def _improve_solution(self, current_state: SolutionState, 
                         constraints: List[Constraint]) -> SolutionState:
        """
        改进当前解
        
        Args:
            current_state: 当前状态
            constraints: 约束列表
            
        Returns:
            改进后的状态
        """
        # 创建新状态
        new_state = SolutionState(
            variables=current_state.variables.copy(),
            violations=[],
            objective_value=current_state.objective_value,
            is_feasible=current_state.is_feasible,
            iteration_count=current_state.iteration_count
        )
        
        # 简化的改进策略：随机分配未分配的变量
        for var_id, variable in new_state.variables.items():
            if not variable.is_assigned and variable.domain:
                # 选择域中的第一个值（简化策略）
                variable.current_value = variable.domain[0]
                variable.is_assigned = True
        
        # 检查约束违反
        for constraint in constraints:
            violation = self.constraint_checker.check_constraint(constraint, new_state.variables)
            if violation:
                new_state.violations.append(violation)
        
        return new_state
    
    def create_default_constraints(self, io_points: List[IOPoint], 
                                  resources: Dict[str, Any]) -> List[Constraint]:
        """
        创建默认约束
        
        Args:
            io_points: I/O点列表
            resources: 资源字典
            
        Returns:
            约束列表
        """
        constraints = []
        
        # 容量约束
        capacity_constraint = Constraint(
            id="capacity_limit",
            constraint_type=ConstraintType.CAPACITY,
            operator=ConstraintOperator.LESS_EQUAL,
            target_attribute="resource_capacity",
            constraint_value=32,  # 假设最大容量为32
            priority=1,
            description="资源容量限制",
            is_hard=True
        )
        constraints.append(capacity_constraint)
        
        # 兼容性约束
        compatibility_constraint = Constraint(
            id="signal_compatibility",
            constraint_type=ConstraintType.COMPATIBILITY,
            operator=ConstraintOperator.IN,
            target_attribute="signal_type",
            constraint_value=["AI", "AO", "DI", "DO"],
            priority=1,
            description="信号类型兼容性",
            is_hard=True
        )
        constraints.append(compatibility_constraint)
        
        return constraints
    
    def get_solution_summary(self, solution_state: SolutionState) -> Dict[str, Any]:
        """
        获取求解摘要
        
        Args:
            solution_state: 求解状态
            
        Returns:
            摘要字典
        """
        summary = {
            'is_feasible': solution_state.is_feasible,
            'objective_value': solution_state.objective_value,
            'iteration_count': solution_state.iteration_count,
            'total_variables': len(solution_state.variables),
            'assigned_variables': sum(1 for var in solution_state.variables.values() if var.is_assigned),
            'total_violations': len(solution_state.violations),
            'hard_violations': sum(1 for v in solution_state.violations if 'HARD' in v.violation_type),
            'soft_violations': sum(1 for v in solution_state.violations if 'SOFT' in v.violation_type)
        }
        
        # 违反详情
        if solution_state.violations:
            summary['violation_details'] = [
                {
                    'constraint_id': v.constraint_id,
                    'variable_id': v.variable_id,
                    'severity': v.severity,
                    'description': v.description
                }
                for v in solution_state.violations
            ]
        
        return summary
