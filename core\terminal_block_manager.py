"""
端子排管理器
负责管理端子排的分配和组织
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from core.logger import get_logger
from core.data_models import IOPoint, SignalType


class TerminalBlockStrategy(Enum):
    """端子排分配策略"""
    ETP_FORM = "ETP_FORM"      # ETP形式：按ETP类型分组
    CABLE_FORM = "CABLE_FORM"  # 电缆形式：按电缆分组


@dataclass
class TerminalBlock:
    """端子排数据类"""
    name: str
    etp_type: str = ""
    max_points: int = 32
    current_points: int = 0
    io_points: List[IOPoint] = None
    cabinet: str = ""
    rail: str = ""
    position: float = 0.0
    spare_limit: int = 2  # spare点下限

    def __post_init__(self):
        if self.io_points is None:
            self.io_points = []

    @property
    def available_points(self) -> int:
        """可用点数"""
        return self.max_points - self.current_points

    @property
    def is_full(self) -> bool:
        """是否已满"""
        return self.current_points >= self.max_points

    @property
    def spare_count(self) -> int:
        """当前spare点数量"""
        return sum(1 for point in self.io_points if hasattr(point, 'is_spare') and point.is_spare)

    @property
    def needs_spare_points(self) -> bool:
        """是否需要更多spare点"""
        return self.spare_count < self.spare_limit and self.available_points > 0


@dataclass
class TerminalBlockAllocationResult:
    """端子排分配结果"""
    success: bool = False
    terminal_blocks: List[TerminalBlock] = None
    allocated_points: List[IOPoint] = None
    failed_points: List[IOPoint] = None
    warnings: List[str] = None
    errors: List[str] = None
    summary: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.terminal_blocks is None:
            self.terminal_blocks = []
        if self.allocated_points is None:
            self.allocated_points = []
        if self.failed_points is None:
            self.failed_points = []
        if self.warnings is None:
            self.warnings = []
        if self.errors is None:
            self.errors = []
        if self.summary is None:
            self.summary = {}


class TerminalBlockManager:
    """端子排管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化端子排管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = get_logger(__name__)
        
        # 分配策略
        self.strategy = TerminalBlockStrategy.ETP_FORM
        
        # ETP类型映射
        self.etp_type_mapping = {
            SignalType.AI: 'CPM16-AI3700',
            SignalType.AO: 'CPM16-AO3700',
            SignalType.DI: 'CPM16-DI3700',
            SignalType.DO: 'CPM16-DO3700'
        }
        
        # 端子排配置
        self.max_points_per_block = 32
        self.etp_width = 6.2  # mm
    
    def set_strategy(self, strategy: TerminalBlockStrategy):
        """
        设置分配策略
        
        Args:
            strategy: 端子排分配策略
        """
        self.strategy = strategy
        self.logger.info(f"设置端子排分配策略: {strategy.value}")
    
    def allocate_terminal_blocks(self, io_points: List[IOPoint], 
                                cabinets: List[Dict[str, Any]], 
                                wiring_typicals: Dict[str, Any]) -> TerminalBlockAllocationResult:
        """
        分配端子排
        
        Args:
            io_points: I/O点列表
            cabinets: 机柜列表
            wiring_typicals: 典型回路字典
            
        Returns:
            端子排分配结果
        """
        self.logger.info(f"开始端子排分配，策略: {self.strategy.value}, I/O点数: {len(io_points)}")
        
        result = TerminalBlockAllocationResult()
        
        try:
            if self.strategy == TerminalBlockStrategy.ETP_FORM:
                return self._allocate_by_etp_form(io_points, cabinets, result)
            elif self.strategy == TerminalBlockStrategy.CABLE_FORM:
                return self._allocate_by_cable_form(io_points, cabinets, result)
            else:
                error_msg = f"不支持的分配策略: {self.strategy}"
                result.errors.append(error_msg)
                self.logger.error(error_msg)
                return result
                
        except Exception as e:
            self.logger.error(f"端子排分配异常: {e}")
            result.errors.append(f"端子排分配异常: {e}")
            result.success = False
            return result
    
    def _allocate_by_etp_form(self, io_points: List[IOPoint], 
                             cabinets: List[Dict[str, Any]], 
                             result: TerminalBlockAllocationResult) -> TerminalBlockAllocationResult:
        """
        按ETP形式分配
        
        Args:
            io_points: I/O点列表
            cabinets: 机柜列表
            result: 分配结果对象
            
        Returns:
            端子排分配结果
        """
        self.logger.info("执行ETP形式分配")
        
        # 按ETP类型分组I/O点
        etp_groups = self._group_by_etp_type(io_points)
        
        terminal_block_counter = 1
        
        for etp_type, points in etp_groups.items():
            self.logger.info(f"处理ETP类型: {etp_type}, I/O点数: {len(points)}")
            
            # 为每个ETP类型创建端子排
            while points:
                # 创建新的端子排
                terminal_block = TerminalBlock(
                    name=f"TB{terminal_block_counter:03d}",
                    etp_type=etp_type,
                    max_points=self.max_points_per_block
                )
                
                # 分配I/O点到端子排
                allocated_in_block = []
                for point in points[:]:
                    if terminal_block.available_points > 0:
                        terminal_block.io_points.append(point)
                        terminal_block.current_points += 1
                        allocated_in_block.append(point)
                        result.allocated_points.append(point)
                        
                        # 更新I/O点的端子排信息
                        point.rack = terminal_block.name
                        
                        self.logger.debug(f"分配I/O点 {point.tag} 到端子排 {terminal_block.name}")
                    else:
                        break
                
                # 从待分配列表中移除已分配的点
                for point in allocated_in_block:
                    points.remove(point)
                
                # 选择机柜和导轨
                target_cabinet = self._select_cabinet_for_terminal_block(terminal_block, cabinets)
                if target_cabinet:
                    terminal_block.cabinet = target_cabinet['name']
                    # 简化：选择第一个导轨
                    rails = target_cabinet.get('rails', [])
                    if rails:
                        terminal_block.rail = rails[0].get('name', 'Rail1')
                
                result.terminal_blocks.append(terminal_block)
                terminal_block_counter += 1
                
                self.logger.info(f"创建端子排 {terminal_block.name}, ETP类型: {etp_type}, I/O点数: {terminal_block.current_points}")
        
        # 生成摘要
        result.summary = self._generate_summary(result)
        result.success = len(result.errors) == 0
        
        return result
    
    def _allocate_by_cable_form(self, io_points: List[IOPoint], 
                               cabinets: List[Dict[str, Any]], 
                               result: TerminalBlockAllocationResult) -> TerminalBlockAllocationResult:
        """
        按电缆形式分配
        
        Args:
            io_points: I/O点列表
            cabinets: 机柜列表
            result: 分配结果对象
            
        Returns:
            端子排分配结果
        """
        self.logger.info("执行电缆形式分配")
        
        # 按电缆分组I/O点
        cable_groups = self._group_by_cable(io_points)
        
        terminal_block_counter = 1
        
        for cable_name, points in cable_groups.items():
            self.logger.info(f"处理电缆: {cable_name}, I/O点数: {len(points)}")
            
            # 为每个电缆创建端子排
            while points:
                # 创建新的端子排
                terminal_block = TerminalBlock(
                    name=f"TB{terminal_block_counter:03d}",
                    etp_type="CABLE",
                    max_points=self.max_points_per_block
                )
                
                # 分配I/O点到端子排
                allocated_in_block = []
                for point in points[:]:
                    if terminal_block.available_points > 0:
                        terminal_block.io_points.append(point)
                        terminal_block.current_points += 1
                        allocated_in_block.append(point)
                        result.allocated_points.append(point)
                        
                        # 更新I/O点的端子排信息
                        point.rack = terminal_block.name
                        
                        self.logger.debug(f"分配I/O点 {point.tag} 到端子排 {terminal_block.name}")
                    else:
                        break
                
                # 从待分配列表中移除已分配的点
                for point in allocated_in_block:
                    points.remove(point)
                
                # 选择机柜和导轨
                target_cabinet = self._select_cabinet_for_terminal_block(terminal_block, cabinets)
                if target_cabinet:
                    terminal_block.cabinet = target_cabinet['name']
                    # 简化：选择第一个导轨
                    rails = target_cabinet.get('rails', [])
                    if rails:
                        terminal_block.rail = rails[0].get('name', 'Rail1')
                
                result.terminal_blocks.append(terminal_block)
                terminal_block_counter += 1
                
                self.logger.info(f"创建端子排 {terminal_block.name}, 电缆: {cable_name}, I/O点数: {terminal_block.current_points}")
        
        # 生成摘要
        result.summary = self._generate_summary(result)
        result.success = len(result.errors) == 0
        
        return result
    
    def _group_by_etp_type(self, io_points: List[IOPoint]) -> Dict[str, List[IOPoint]]:
        """
        按ETP类型分组I/O点
        
        Args:
            io_points: I/O点列表
            
        Returns:
            按ETP类型分组的字典
        """
        groups = {}
        
        for point in io_points:
            etp_type = self.etp_type_mapping.get(point.signal_type, 'CPM16-DI3700')
            
            if etp_type not in groups:
                groups[etp_type] = []
            
            groups[etp_type].append(point)
        
        return groups
    
    def _group_by_cable(self, io_points: List[IOPoint]) -> Dict[str, List[IOPoint]]:
        """
        按电缆分组I/O点
        
        Args:
            io_points: I/O点列表
            
        Returns:
            按电缆分组的字典
        """
        groups = {}
        
        for point in io_points:
            # 简化：使用location作为电缆标识
            cable_name = point.location or "Unknown_Cable"
            
            if cable_name not in groups:
                groups[cable_name] = []
            
            groups[cable_name].append(point)
        
        return groups
    
    def _select_cabinet_for_terminal_block(self, terminal_block: TerminalBlock, 
                                         cabinets: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        为端子排选择机柜
        
        Args:
            terminal_block: 端子排对象
            cabinets: 机柜列表
            
        Returns:
            选中的机柜，如果没有合适的返回None
        """
        # 简化选择逻辑：选择第一个可用的机柜
        for cabinet in cabinets:
            rails = cabinet.get('rails', [])
            if rails:
                return cabinet
        
        return None
    
    def _generate_summary(self, result: TerminalBlockAllocationResult) -> Dict[str, Any]:
        """
        生成分配摘要
        
        Args:
            result: 分配结果对象
            
        Returns:
            摘要字典
        """
        total_points = len(result.allocated_points) + len(result.failed_points)
        success_rate = (len(result.allocated_points) / total_points * 100) if total_points > 0 else 0
        
        # 统计端子排利用率
        block_utilization = []
        for block in result.terminal_blocks:
            utilization = (block.current_points / block.max_points * 100) if block.max_points > 0 else 0
            block_utilization.append({
                'name': block.name,
                'etp_type': block.etp_type,
                'utilization': utilization,
                'current_points': block.current_points,
                'max_points': block.max_points
            })
        
        summary = {
            'total_points': total_points,
            'allocated_points': len(result.allocated_points),
            'failed_points': len(result.failed_points),
            'success_rate': success_rate,
            'total_terminal_blocks': len(result.terminal_blocks),
            'strategy': self.strategy.value,
            'block_utilization': block_utilization
        }
        
        return summary
