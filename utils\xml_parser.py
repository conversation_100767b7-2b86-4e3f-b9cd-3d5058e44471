"""
XML解析工具
负责解析机柜配置XML文件
"""

import xml.etree.ElementTree as ET
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional

from core.logger import get_logger


class XMLParser:
    """XML解析器类"""
    
    def __init__(self):
        """初始化XML解析器"""
        self.logger = get_logger(__name__)
    
    def parse_cabinet_xml(self, file_path: str) -> Dict[str, Any]:
        """
        解析机柜配置XML文件
        
        Args:
            file_path: XML文件路径
            
        Returns:
            机柜配置字典
        """
        self.logger.info(f"解析机柜配置XML: {file_path}")
        
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            cabinet_config = {
                'name': Path(file_path).stem,
                'file_path': file_path,
                'rails': [],
                'racks': [],
                'metadata': {}
            }
            
            # 解析XML结构
            cabinet_config = self._parse_xml_structure(root, cabinet_config)
            
            self.logger.info(f"成功解析机柜配置: {cabinet_config['name']}")
            return cabinet_config
            
        except Exception as e:
            self.logger.error(f"解析XML文件失败 {file_path}: {e}")
            raise
    
    def _parse_xml_structure(self, root: ET.Element, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析XML结构
        
        Args:
            root: XML根元素
            config: 配置字典
            
        Returns:
            更新后的配置字典
        """
        # 提取基本信息
        config['metadata'] = {
            'tag': root.tag,
            'attributes': root.attrib
        }
        
        # 查找导轨信息
        rails = self._extract_rails(root)
        config['rails'] = rails
        
        # 查找机架信息
        racks = self._extract_racks(root)
        config['racks'] = racks
        
        return config
    
    def _extract_rails(self, root: ET.Element) -> List[Dict[str, Any]]:
        """
        提取导轨信息
        
        Args:
            root: XML根元素
            
        Returns:
            导轨列表
        """
        rails = []
        
        # 查找可能的导轨元素
        rail_elements = []
        
        # 常见的导轨元素名称
        rail_tags = ['rail', 'Rail', 'DINRail', 'dinrail', 'track', 'Track']
        
        for tag in rail_tags:
            rail_elements.extend(root.findall(f".//{tag}"))
        
        # 如果没有找到明确的导轨元素，创建默认导轨
        if not rail_elements:
            # 基于XML结构推断导轨数量
            rail_count = self._estimate_rail_count(root)
            for i in range(rail_count):
                rails.append({
                    'name': f'Rail{i+1}',
                    'width': 500.0,  # 默认宽度
                    'height': 100.0,  # 默认高度
                    'position': i
                })
        else:
            # 解析找到的导轨元素
            for i, rail_elem in enumerate(rail_elements):
                rail_info = {
                    'name': rail_elem.get('name', f'Rail{i+1}'),
                    'width': float(rail_elem.get('width', 500.0)),
                    'height': float(rail_elem.get('height', 100.0)),
                    'position': i,
                    'attributes': rail_elem.attrib
                }
                rails.append(rail_info)
        
        return rails
    
    def _extract_racks(self, root: ET.Element) -> List[Dict[str, Any]]:
        """
        提取机架信息
        
        Args:
            root: XML根元素
            
        Returns:
            机架列表
        """
        racks = []
        
        # 查找可能的机架元素
        rack_elements = []
        
        # 常见的机架元素名称
        rack_tags = ['rack', 'Rack', 'chassis', 'Chassis', 'frame', 'Frame']
        
        for tag in rack_tags:
            rack_elements.extend(root.findall(f".//{tag}"))
        
        # 如果没有找到明确的机架元素，创建默认机架
        if not rack_elements:
            # 基于XML结构推断机架数量
            rack_count = self._estimate_rack_count(root)
            for i in range(rack_count):
                racks.append({
                    'name': f'R{i+1:02d}',
                    'max_slots': 16,
                    'rail': f'Rail{i+1}',
                    'position': i
                })
        else:
            # 解析找到的机架元素
            for i, rack_elem in enumerate(rack_elements):
                rack_info = {
                    'name': rack_elem.get('name', f'R{i+1:02d}'),
                    'max_slots': int(rack_elem.get('slots', 16)),
                    'rail': rack_elem.get('rail', f'Rail{i+1}'),
                    'position': i,
                    'attributes': rack_elem.attrib
                }
                racks.append(rack_info)
        
        return racks
    
    def _estimate_rail_count(self, root: ET.Element) -> int:
        """
        估算导轨数量
        
        Args:
            root: XML根元素
            
        Returns:
            估算的导轨数量
        """
        # 简单的启发式方法
        # 查找可能表示导轨的元素数量
        potential_rails = len(root.findall(".//rail")) + len(root.findall(".//Rail"))
        
        if potential_rails > 0:
            return potential_rails
        
        # 如果没有明确的导轨元素，默认返回2个导轨
        return 2
    
    def _estimate_rack_count(self, root: ET.Element) -> int:
        """
        估算机架数量
        
        Args:
            root: XML根元素
            
        Returns:
            估算的机架数量
        """
        # 简单的启发式方法
        # 查找可能表示机架的元素数量
        potential_racks = (len(root.findall(".//rack")) + 
                          len(root.findall(".//Rack")) + 
                          len(root.findall(".//chassis")) + 
                          len(root.findall(".//Chassis")))
        
        if potential_racks > 0:
            return potential_racks
        
        # 如果没有明确的机架元素，默认返回2个机架
        return 2
    
    def parse_wiring_typical_xml(self, file_path: str) -> Dict[str, Any]:
        """
        解析典型回路XML文件
        
        Args:
            file_path: XML文件路径
            
        Returns:
            典型回路配置字典
        """
        self.logger.info(f"解析典型回路XML: {file_path}")
        
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            typical_config = {
                'name': Path(file_path).stem,
                'file_path': file_path,
                'components': [],
                'connections': [],
                'metadata': {
                    'tag': root.tag,
                    'attributes': root.attrib
                }
            }
            
            # 提取组件信息
            components = self._extract_components(root)
            typical_config['components'] = components
            
            # 提取连接信息
            connections = self._extract_connections(root)
            typical_config['connections'] = connections
            
            self.logger.info(f"成功解析典型回路: {typical_config['name']}")
            return typical_config
            
        except Exception as e:
            self.logger.error(f"解析典型回路XML文件失败 {file_path}: {e}")
            raise
    
    def _extract_components(self, root: ET.Element) -> List[Dict[str, Any]]:
        """
        提取组件信息
        
        Args:
            root: XML根元素
            
        Returns:
            组件列表
        """
        components = []
        
        # 查找可能的组件元素
        component_tags = ['component', 'Component', 'device', 'Device', 'element', 'Element']
        
        for tag in component_tags:
            for elem in root.findall(f".//{tag}"):
                component = {
                    'name': elem.get('name', elem.tag),
                    'type': elem.get('type', 'unknown'),
                    'attributes': elem.attrib,
                    'text': elem.text
                }
                components.append(component)
        
        return components
    
    def _extract_connections(self, root: ET.Element) -> List[Dict[str, Any]]:
        """
        提取连接信息
        
        Args:
            root: XML根元素
            
        Returns:
            连接列表
        """
        connections = []
        
        # 查找可能的连接元素
        connection_tags = ['connection', 'Connection', 'wire', 'Wire', 'link', 'Link']
        
        for tag in connection_tags:
            for elem in root.findall(f".//{tag}"):
                connection = {
                    'from': elem.get('from', ''),
                    'to': elem.get('to', ''),
                    'type': elem.get('type', 'wire'),
                    'attributes': elem.attrib
                }
                connections.append(connection)
        
        return connections
