"""
日志系统模块
提供统一的日志记录功能
"""

import logging
import logging.handlers
import os
from pathlib import Path
from typing import Dict, Any, Optional

def setup_logger(config: Optional[Dict[str, Any]] = None) -> None:
    """
    设置日志系统
    
    Args:
        config: 日志配置字典
    """
    if config is None:
        config = {}
    
    # 默认配置
    default_config = {
        'level': 'INFO',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'file_path': 'logs/ewreborn.log',
        'max_bytes': 10 * 1024 * 1024,  # 10MB
        'backup_count': 5
    }
    
    # 合并配置
    log_config = {**default_config, **config}
    
    # 创建日志目录
    log_file = Path(log_config['file_path'])
    log_file.parent.mkdir(parents=True, exist_ok=True)
    
    # 设置日志级别
    level = getattr(logging, log_config['level'].upper(), logging.INFO)
    
    # 配置根日志器
    logging.basicConfig(
        level=level,
        format=log_config['format'],
        handlers=[
            # 控制台处理器
            logging.StreamHandler(),
            # 文件处理器（带轮转）
            logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=log_config['max_bytes'],
                backupCount=log_config['backup_count'],
                encoding='utf-8'
            )
        ]
    )

def get_logger(name: str) -> logging.Logger:
    """
    获取日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        logging.Logger: 日志器实例
    """
    return logging.getLogger(name)
