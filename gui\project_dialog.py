"""
项目管理对话框
提供项目创建和打开功能
"""

import os
from pathlib import Path
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QLineEdit, QPushButton, QTextEdit, QFileDialog,
                               QMessageBox, QTabWidget, QWidget, QFormLayout,
                               QListWidget, QListWidgetItem, QSplitter)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from core.project_manager import ProjectManager, ProjectInfo


class NewProjectDialog(QDialog):
    """新建项目对话框"""
    
    project_created = Signal(str)  # 项目创建成功信号
    
    def __init__(self, project_manager: ProjectManager, parent=None):
        super().__init__(parent)
        self.project_manager = project_manager
        self.setWindowTitle("新建项目")
        self.setModal(True)
        self.resize(500, 400)
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 项目信息表单
        form_layout = QFormLayout()
        
        # 项目名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("输入项目名称")
        form_layout.addRow("项目名称*:", self.name_edit)
        
        # 项目路径
        path_layout = QHBoxLayout()
        self.path_edit = QLineEdit()
        self.path_edit.setPlaceholderText("选择项目保存路径")
        self.path_edit.setText(str(Path.home() / "Documents"))
        
        self.browse_button = QPushButton("浏览...")
        self.browse_button.clicked.connect(self.browse_path)
        
        path_layout.addWidget(self.path_edit)
        path_layout.addWidget(self.browse_button)
        form_layout.addRow("保存路径*:", path_layout)
        
        # 项目描述
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("输入项目描述（可选）")
        self.description_edit.setMaximumHeight(100)
        form_layout.addRow("项目描述:", self.description_edit)
        
        layout.addLayout(form_layout)
        
        # 项目结构说明
        info_label = QLabel("项目将创建以下标准文件夹结构：")
        info_label.setFont(QFont("", 9, QFont.Bold))
        layout.addWidget(info_label)
        
        structure_text = """• 01B_Cabinet Templates - 机柜配置文件
• 01C_Wiring Typical - 典型回路文件  
• 04A_IODB - IODB数据文件
• 04B_PIDB - PIDB数据文件
• output - 输出文件
• logs - 日志文件
• temp - 临时文件"""
        
        structure_label = QLabel(structure_text)
        structure_label.setStyleSheet("color: #666; padding: 10px; background: #f5f5f5; border-radius: 5px;")
        layout.addWidget(structure_label)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.create_button = QPushButton("创建项目")
        self.create_button.clicked.connect(self.create_project)
        self.create_button.setDefault(True)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.create_button)
        
        layout.addLayout(button_layout)
    
    def browse_path(self):
        """浏览路径"""
        path = QFileDialog.getExistingDirectory(
            self, 
            "选择项目保存路径",
            self.path_edit.text()
        )
        
        if path:
            self.path_edit.setText(path)
    
    def create_project(self):
        """创建项目"""
        name = self.name_edit.text().strip()
        path = self.path_edit.text().strip()
        description = self.description_edit.toPlainText().strip()
        
        # 验证输入
        if not name:
            QMessageBox.warning(self, "警告", "请输入项目名称")
            return
        
        if not path:
            QMessageBox.warning(self, "警告", "请选择项目保存路径")
            return
        
        if not os.path.exists(path):
            QMessageBox.warning(self, "警告", "选择的路径不存在")
            return
        
        # 检查项目是否已存在
        project_path = Path(path) / name
        if project_path.exists():
            QMessageBox.warning(self, "警告", f"项目目录已存在：{project_path}")
            return
        
        # 创建项目
        try:
            success = self.project_manager.create_new_project(name, path, description)
            
            if success:
                QMessageBox.information(self, "成功", f"项目创建成功：{project_path}")
                self.project_created.emit(str(project_path))
                self.accept()
            else:
                QMessageBox.critical(self, "错误", "项目创建失败，请查看日志")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"项目创建失败：{e}")


class OpenProjectDialog(QDialog):
    """打开项目对话框"""
    
    project_opened = Signal(str)  # 项目打开成功信号
    
    def __init__(self, project_manager: ProjectManager, parent=None):
        super().__init__(parent)
        self.project_manager = project_manager
        self.setWindowTitle("打开项目")
        self.setModal(True)
        self.resize(600, 400)
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 说明
        info_label = QLabel("选择要打开的项目文件夹：")
        layout.addWidget(info_label)
        
        # 路径选择
        path_layout = QHBoxLayout()
        
        self.path_edit = QLineEdit()
        self.path_edit.setPlaceholderText("选择项目文件夹")
        
        self.browse_button = QPushButton("浏览...")
        self.browse_button.clicked.connect(self.browse_project)
        
        path_layout.addWidget(self.path_edit)
        path_layout.addWidget(self.browse_button)
        
        layout.addLayout(path_layout)
        
        # 项目信息显示
        self.info_widget = QWidget()
        self.info_layout = QFormLayout(self.info_widget)
        
        self.project_name_label = QLabel("-")
        self.project_path_label = QLabel("-")
        self.project_desc_label = QLabel("-")
        
        self.info_layout.addRow("项目名称:", self.project_name_label)
        self.info_layout.addRow("项目路径:", self.project_path_label)
        self.info_layout.addRow("项目描述:", self.project_desc_label)
        
        layout.addWidget(self.info_widget)
        
        # 项目结构验证
        self.structure_label = QLabel("项目结构验证：")
        layout.addWidget(self.structure_label)
        
        self.structure_list = QListWidget()
        layout.addWidget(self.structure_list)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.open_button = QPushButton("打开项目")
        self.open_button.clicked.connect(self.open_project)
        self.open_button.setEnabled(False)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.open_button)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.path_edit.textChanged.connect(self.validate_project)
    
    def browse_project(self):
        """浏览项目"""
        path = QFileDialog.getExistingDirectory(
            self,
            "选择项目文件夹",
            self.path_edit.text() or str(Path.home())
        )
        
        if path:
            self.path_edit.setText(path)
    
    def validate_project(self):
        """验证项目"""
        path = self.path_edit.text().strip()
        
        if not path or not os.path.exists(path):
            self.clear_project_info()
            return
        
        project_path = Path(path)
        
        # 检查项目配置文件
        config_file = project_path / "project_config.json"
        if config_file.exists():
            try:
                import json
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                project_info = config.get('project_info', {})
                self.project_name_label.setText(project_info.get('name', project_path.name))
                self.project_path_label.setText(str(project_path))
                self.project_desc_label.setText(project_info.get('description', '无描述'))
                
            except Exception:
                self.project_name_label.setText(project_path.name)
                self.project_path_label.setText(str(project_path))
                self.project_desc_label.setText("配置文件读取失败")
        else:
            self.project_name_label.setText(project_path.name)
            self.project_path_label.setText(str(project_path))
            self.project_desc_label.setText("无项目配置文件")
        
        # 验证项目结构
        validation_result = self.project_manager.validate_project_structure(path)
        
        self.structure_list.clear()
        all_valid = True
        
        for folder, exists in validation_result.items():
            item = QListWidgetItem(f"{'✓' if exists else '✗'} {folder}")
            item.setForeground(Qt.green if exists else Qt.red)
            self.structure_list.addItem(item)
            
            if not exists:
                all_valid = False
        
        # 启用/禁用打开按钮
        self.open_button.setEnabled(True)  # 即使结构不完整也允许打开
    
    def clear_project_info(self):
        """清空项目信息"""
        self.project_name_label.setText("-")
        self.project_path_label.setText("-")
        self.project_desc_label.setText("-")
        self.structure_list.clear()
        self.open_button.setEnabled(False)
    
    def open_project(self):
        """打开项目"""
        path = self.path_edit.text().strip()
        
        if not path:
            QMessageBox.warning(self, "警告", "请选择项目文件夹")
            return
        
        try:
            success = self.project_manager.open_project(path)
            
            if success:
                QMessageBox.information(self, "成功", f"项目打开成功：{path}")
                self.project_opened.emit(path)
                self.accept()
            else:
                QMessageBox.critical(self, "错误", "项目打开失败，请查看日志")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"项目打开失败：{e}")


class ProjectDialog(QDialog):
    """项目管理主对话框"""
    
    project_changed = Signal(str)  # 项目变更信号
    
    def __init__(self, project_manager: ProjectManager, parent=None):
        super().__init__(parent)
        self.project_manager = project_manager
        self.setWindowTitle("项目管理")
        self.setModal(True)
        self.resize(700, 500)
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 标签页
        self.tab_widget = QTabWidget()
        
        # 新建项目标签页
        self.new_project_dialog = NewProjectDialog(self.project_manager)
        self.new_project_dialog.project_created.connect(self.on_project_created)
        
        # 打开项目标签页
        self.open_project_dialog = OpenProjectDialog(self.project_manager)
        self.open_project_dialog.project_opened.connect(self.on_project_opened)
        
        self.tab_widget.addTab(self.new_project_dialog, "新建项目")
        self.tab_widget.addTab(self.open_project_dialog, "打开项目")
        
        layout.addWidget(self.tab_widget)
        
        # 关闭按钮
        button_layout = QHBoxLayout()
        
        self.close_button = QPushButton("关闭")
        self.close_button.clicked.connect(self.accept)
        
        button_layout.addStretch()
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
    
    def on_project_created(self, project_path: str):
        """项目创建成功"""
        self.project_changed.emit(project_path)
        self.accept()
    
    def on_project_opened(self, project_path: str):
        """项目打开成功"""
        self.project_changed.emit(project_path)
        self.accept()
