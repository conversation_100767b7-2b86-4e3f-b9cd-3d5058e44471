"""
I/O分配界面组件
负责I/O点分配的用户界面和逻辑
"""

import logging
from typing import Dict, Any

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QPushButton, QLabel, QLineEdit, QTextEdit, QProgressBar,
    QGroupBox, QCheckBox, QComboBox, QFileDialog, QMessageBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QSplitter,
    QGraphicsDropShadowEffect, QFrame
)
from PySide6.QtCore import Qt, Signal, QThread, QTimer, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QFont, QColor

from core.logger import get_logger
from .material_theme import get_theme_manager


class AllocationWidget(QWidget):
    """I/O分配界面组件"""
    
    # 信号定义
    progress_updated = Signal(int, str)  # 进度更新信号
    status_message = Signal(str, int)    # 状态消息信号
    
    def __init__(self, config: Dict[str, Any], parent=None):
        """
        初始化I/O分配界面
        
        Args:
            config: 应用程序配置
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.config = config
        self.logger = get_logger(__name__)
        
        # 数据存储
        self.iodb_data = None
        self.pidb_data = None
        self.cabinet_profiles = {}
        self.wiring_typicals = {}
        
        # 初始化UI
        self._setup_ui()
        self._connect_signals()
        
        # 应用Material主题增强
        self._apply_material_enhancements()

        # 初始化示例数据以便测试
        self._initialize_sample_data()

        self.logger.info("I/O分配界面初始化完成")
    
    def _setup_ui(self):
        """设置用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制面板
        control_panel = self._create_control_panel()
        splitter.addWidget(control_panel)
        
        # 右侧结果显示
        result_panel = self._create_result_panel()
        splitter.addWidget(result_panel)
        
        # 设置分割器比例
        splitter.setSizes([400, 800])
        
        # 底部状态和进度
        status_layout = self._create_status_layout()
        main_layout.addLayout(status_layout)
    
    def _create_control_panel(self) -> QWidget:
        """创建控制面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 数据文件选择组
        file_group = QGroupBox("数据文件选择")
        file_layout = QGridLayout(file_group)
        
        # IODB文件选择
        file_layout.addWidget(QLabel("IODB文件:"), 0, 0)
        self.iodb_path_edit = QLineEdit()
        self.iodb_path_edit.setReadOnly(True)
        file_layout.addWidget(self.iodb_path_edit, 0, 1)
        self.iodb_browse_btn = QPushButton("浏览...")
        file_layout.addWidget(self.iodb_browse_btn, 0, 2)

        # PIDB文件选择
        file_layout.addWidget(QLabel("PIDB文件:"), 1, 0)
        self.pidb_path_edit = QLineEdit()
        self.pidb_path_edit.setReadOnly(True)
        file_layout.addWidget(self.pidb_path_edit, 1, 1)
        self.pidb_browse_btn = QPushButton("浏览...")
        file_layout.addWidget(self.pidb_browse_btn, 1, 2)
        
        layout.addWidget(file_group)
        
        # 分配设置组
        settings_group = QGroupBox("分配设置")
        settings_layout = QVBoxLayout(settings_group)
        
        # PartType匹配选项
        self.parttype_matching_cb = QCheckBox("启用PartType匹配")
        self.parttype_matching_cb.setChecked(
            self.config.get('allocation_settings.enable_parttype_matching', True)
        )
        settings_layout.addWidget(self.parttype_matching_cb)
        
        # 详细日志选项
        self.detailed_logging_cb = QCheckBox("启用详细日志")
        self.detailed_logging_cb.setChecked(
            self.config.get('allocation_settings.enable_detailed_logging', True)
        )
        settings_layout.addWidget(self.detailed_logging_cb)
        
        # 分配顺序选择
        settings_layout.addWidget(QLabel("分配顺序:"))
        self.allocation_order_combo = QComboBox()
        self.allocation_order_combo.addItems([
            "电缆名称和Pair升序",
            "电缆名称和Pair降序",
            "系统分组"
        ])
        settings_layout.addWidget(self.allocation_order_combo)

        # 端子排分配策略选择
        settings_layout.addWidget(QLabel("端子排分配策略:"))
        self.terminal_block_strategy_combo = QComboBox()
        self.terminal_block_strategy_combo.addItems([
            "ETP形式分配",
            "电缆形式分配"
        ])
        self.terminal_block_strategy_combo.setCurrentIndex(0)  # 默认ETP形式
        settings_layout.addWidget(self.terminal_block_strategy_combo)

        # 卡件配置组
        card_group = QGroupBox("卡件配置")
        card_layout = QVBoxLayout(card_group)

        # 机架前缀选择
        rack_prefix_layout = QHBoxLayout()
        rack_prefix_layout.addWidget(QLabel("机架前缀:"))
        self.rack_prefix_combo = QComboBox()
        self.rack_prefix_combo.addItems(["R (Rack)", "C (Chassis)"])
        self.rack_prefix_combo.setCurrentIndex(0)  # 默认R
        rack_prefix_layout.addWidget(self.rack_prefix_combo)
        card_layout.addLayout(rack_prefix_layout)

        # ETP后缀设置
        etp_suffix_layout = QHBoxLayout()
        etp_suffix_layout.addWidget(QLabel("ETP后缀:"))
        etp_suffix_layout.addWidget(QLabel("上:"))
        self.etp_upper_suffix_edit = QLineEdit("U")
        self.etp_upper_suffix_edit.setMaximumWidth(30)
        etp_suffix_layout.addWidget(self.etp_upper_suffix_edit)
        etp_suffix_layout.addWidget(QLabel("下:"))
        self.etp_lower_suffix_edit = QLineEdit("L")
        self.etp_lower_suffix_edit.setMaximumWidth(30)
        etp_suffix_layout.addWidget(self.etp_lower_suffix_edit)
        etp_suffix_layout.addStretch()
        card_layout.addLayout(etp_suffix_layout)

        # 卡件分配策略
        card_strategy_layout = QHBoxLayout()
        card_strategy_layout.addWidget(QLabel("卡件分配策略:"))
        self.card_allocation_strategy_combo = QComboBox()
        self.card_allocation_strategy_combo.addItems([
            "优先级分配",
            "负载均衡分配"
        ])
        self.card_allocation_strategy_combo.setCurrentIndex(0)  # 默认优先级
        card_strategy_layout.addWidget(self.card_allocation_strategy_combo)
        card_layout.addLayout(card_strategy_layout)

        settings_layout.addWidget(card_group)
        
        layout.addWidget(settings_group)
        
        # 操作按钮组
        action_group = QGroupBox("操作")
        action_layout = QVBoxLayout(action_group)
        
        self.load_data_btn = QPushButton("加载数据")
        self.load_data_btn.setMinimumHeight(35)
        action_layout.addWidget(self.load_data_btn)

        self.validate_data_btn = QPushButton("验证数据")
        self.validate_data_btn.setMinimumHeight(35)
        self.validate_data_btn.setEnabled(False)
        action_layout.addWidget(self.validate_data_btn)

        self.start_allocation_btn = QPushButton("开始分配")
        self.start_allocation_btn.setMinimumHeight(35)
        self.start_allocation_btn.setEnabled(False)
        action_layout.addWidget(self.start_allocation_btn)

        self.export_results_btn = QPushButton("分配结果")
        self.export_results_btn.setMinimumHeight(35)
        self.export_results_btn.setEnabled(False)
        action_layout.addWidget(self.export_results_btn)

        self.export_io_report_btn = QPushButton("IO分配表")
        self.export_io_report_btn.setMinimumHeight(35)
        self.export_io_report_btn.setEnabled(False)
        action_layout.addWidget(self.export_io_report_btn)

        # 添加分隔符
        action_layout.addSpacing(10)

        self.naming_config_btn = QPushButton("命名规则")
        self.naming_config_btn.setMinimumHeight(35)
        self.naming_config_btn.setToolTip("配置器件命名规则")
        action_layout.addWidget(self.naming_config_btn)

        layout.addWidget(action_group)
        
        # 添加弹性空间
        layout.addStretch()
        
        return panel
    
    def _create_result_panel(self) -> QWidget:
        """创建结果显示面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 结果选项卡或表格
        self.result_table = QTableWidget()
        self.result_table.setAlternatingRowColors(True)
        layout.addWidget(self.result_table)
        
        # 日志显示
        log_group = QGroupBox("分配日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        return panel
    
    def _create_status_layout(self) -> QHBoxLayout:
        """创建状态布局"""
        layout = QHBoxLayout()

        # 状态标签
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)

        # 弹性空间
        layout.addStretch()

        # 失败原因显示标签
        self.failure_reason_label = QLabel("")
        self.failure_reason_label.setStyleSheet("color: #FF3B30; font-weight: bold;")
        self.failure_reason_label.setVisible(False)
        layout.addWidget(self.failure_reason_label)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(300)
        layout.addWidget(self.progress_bar)

        return layout
    
    def _connect_signals(self):
        """连接信号和槽"""
        self.iodb_browse_btn.clicked.connect(self._browse_iodb_file)
        self.pidb_browse_btn.clicked.connect(self._browse_pidb_file)
        self.load_data_btn.clicked.connect(self._load_data)
        self.validate_data_btn.clicked.connect(self._validate_data)
        self.start_allocation_btn.clicked.connect(self._start_allocation)
        self.export_results_btn.clicked.connect(self._export_results)
        self.export_io_report_btn.clicked.connect(self._export_io_report)
        self.naming_config_btn.clicked.connect(self._configure_naming_rules)
    
    def _browse_iodb_file(self):
        """浏览IODB文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择IODB文件", 
            self.config.get('data_paths.iodb', ''),
            "Excel文件 (*.xlsx *.xls)"
        )
        if file_path:
            self.iodb_path_edit.setText(file_path)
    
    def _browse_pidb_file(self):
        """浏览PIDB文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择PIDB文件",
            self.config.get('data_paths.pidb', ''),
            "Excel文件 (*.xlsx *.xls)"
        )
        if file_path:
            self.pidb_path_edit.setText(file_path)
    
    def _load_data(self):
        """加载数据"""
        self.logger.info("开始加载数据")
        self.status_message.emit("正在加载数据...", 0)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        try:
            from core.data_loader_simple import DataLoader

            # 创建数据加载器
            data_loader = DataLoader(self.config)

            # 加载配置文件数据
            self.progress_bar.setValue(20)
            all_data = data_loader.load_all_data()

            # 检查IODB文件路径
            iodb_path = self.iodb_path_edit.text().strip()
            if iodb_path:
                self.progress_bar.setValue(50)
                self.iodb_data = data_loader.load_iodb_data(iodb_path)
                all_data['iodb'] = self.iodb_data
                all_data['load_status']['iodb'] = True

            # 检查PIDB文件路径
            pidb_path = self.pidb_path_edit.text().strip()
            if pidb_path:
                self.progress_bar.setValue(80)
                self.pidb_data = data_loader.load_pidb_data(pidb_path)
                all_data['pidb'] = self.pidb_data
                all_data['load_status']['pidb'] = True

            # 保存数据
            self.cabinet_profiles = all_data['cabinet_profiles']
            self.wiring_typicals = all_data['wiring_typicals']

            self.progress_bar.setValue(100)

            # 更新界面
            self._update_data_summary(all_data)

            QTimer.singleShot(500, self._on_data_loaded)

        except Exception as e:
            self.logger.error(f"加载数据失败: {e}")
            self.status_message.emit(f"加载数据失败: {str(e)}", 5000)
            self.progress_bar.setVisible(False)
    
    def _on_data_loaded(self):
        """数据加载完成"""
        self.logger.info("数据加载完成")
        self.status_message.emit("数据加载完成", 3000)
        self.validate_data_btn.setEnabled(True)
    
    def _validate_data(self):
        """验证数据"""
        self.logger.info("开始数据验证")
        self.status_message.emit("正在验证数据...", 0)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        try:
            if not self.iodb_data:
                raise ValueError("请先加载IODB数据")

            from core.validator import IODBValidator

            # 创建验证器
            validator = IODBValidator(self.config)

            self.progress_bar.setValue(30)

            # 执行验证
            validation_result = validator.validate_iodb_data(self.iodb_data)

            self.progress_bar.setValue(100)

            # 显示验证结果
            self._display_validation_results(validation_result)

            # 根据验证结果决定是否可以开始分配
            if validation_result.success:
                QTimer.singleShot(500, self._on_data_validated)
            else:
                self.status_message.emit(f"数据验证失败: {len(validation_result.errors)}个错误", 5000)
                self.progress_bar.setVisible(False)

        except Exception as e:
            self.logger.error(f"数据验证失败: {e}")
            self.status_message.emit(f"数据验证失败: {str(e)}", 5000)
            self.progress_bar.setVisible(False)
    
    def _on_data_validated(self):
        """数据验证完成"""
        self.logger.info("数据验证完成")
        self.status_message.emit("数据验证完成", 3000)
        self.start_allocation_btn.setEnabled(True)
    
    def _start_allocation(self):
        """开始分配"""
        self.logger.info("开始I/O点分配")
        self.status_message.emit("正在进行I/O点分配...", 0)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 清除之前的失败原因显示
        self.failure_reason_label.setVisible(False)

        try:
            if not self.iodb_data or not self.cabinet_profiles:
                raise ValueError("请先加载并验证数据")

            from core.allocator import IOAllocator
            from core.data_loader_simple import DataLoader
            from core.data_models import TerminalBlockStrategy, CardAllocationStrategy, RackPrefix

            # 创建分配器
            allocator = IOAllocator(self.config)

            # 设置端子排分配策略
            strategy_index = self.terminal_block_strategy_combo.currentIndex()
            if strategy_index == 0:
                strategy = TerminalBlockStrategy.ETP_FORM
            else:
                strategy = TerminalBlockStrategy.CABLE_FORM
            allocator.set_terminal_block_strategy(strategy)

            # 设置卡件配置
            # 机架前缀
            rack_prefix_index = self.rack_prefix_combo.currentIndex()
            rack_prefix = RackPrefix.R if rack_prefix_index == 0 else RackPrefix.C
            allocator.card_slot_manager.set_rack_prefix(rack_prefix)

            # ETP后缀
            upper_suffix = self.etp_upper_suffix_edit.text().strip() or "U"
            lower_suffix = self.etp_lower_suffix_edit.text().strip() or "L"
            allocator.card_slot_manager.set_etp_suffixes(upper_suffix, lower_suffix)

            # 卡件分配策略
            card_strategy_index = self.card_allocation_strategy_combo.currentIndex()
            card_strategy = CardAllocationStrategy.PRIORITY if card_strategy_index == 0 else CardAllocationStrategy.LOAD_BALANCE
            allocator.card_slot_manager.set_allocation_strategy(card_strategy)

            # 创建数据加载器来构建数据模型
            data_loader = DataLoader(self.config)

            self.progress_bar.setValue(20)

            # 构建数据模型
            models = data_loader.create_data_models(
                self.iodb_data,
                self.pidb_data or {},
                self.cabinet_profiles,
                self.wiring_typicals
            )

            self.progress_bar.setValue(40)

            # 执行分配
            allocation_result = allocator.allocate_io_points(
                models['cables'],
                models['cabinets'],
                self.wiring_typicals
            )

            self.progress_bar.setValue(100)

            # 保存分配结果
            self.allocation_result = allocation_result

            # 显示分配结果
            self._display_allocation_results(allocation_result)

            # 更新结果表格
            self._update_result_table(allocation_result)

            # 无论成功失败都调用完成处理
            QTimer.singleShot(500, lambda: self._on_allocation_completed(allocation_result))

        except Exception as e:
            self.logger.error(f"I/O点分配失败: {e}")

            # 创建一个失败的分配结果
            from core.data_models import AllocationResult
            failed_result = AllocationResult(success=False)
            failed_result.add_error(f"分配过程异常: {str(e)}")
            self.allocation_result = failed_result

            # 调用完成处理
            QTimer.singleShot(500, lambda: self._on_allocation_completed(failed_result))
    
    def _on_allocation_completed(self, allocation_result):
        """分配完成"""
        self.logger.info("I/O点分配完成")

        # 无论成功失败都启用导出按钮
        self.export_results_btn.setEnabled(True)
        self.export_io_report_btn.setEnabled(True)
        self.progress_bar.setVisible(False)

        # 根据分配结果显示不同的状态信息
        if allocation_result.success:
            # 显示成功信息和统计
            allocated_count = len(allocation_result.allocated_points)
            total_count = allocated_count + len(allocation_result.failed_points)

            status_msg = f"I/O点分配完成: {allocated_count}/{total_count} 成功分配"
            self.status_message.emit(status_msg, 3000)
            self.failure_reason_label.setVisible(False)

            # 显示分配统计
            if hasattr(allocation_result, 'allocation_summary') and allocation_result.allocation_summary:
                summary = allocation_result.allocation_summary

                # 端子排统计
                terminal_summary = summary.get('terminal_block_summary', {})
                terminal_count = terminal_summary.get('total_terminal_blocks', 0)

                # 卡件统计
                card_summary = summary.get('card_slot_summary', {})
                card_count = card_summary.get('total_cards', 0)

                # 电缆完整性统计
                cable_integrity_summary = summary.get('cable_integrity_summary', {})
                cable_violations = cable_integrity_summary.get('violations', 0)
                integrity_rate = cable_integrity_summary.get('integrity_rate', 100)

                if terminal_count > 0 or card_count > 0:
                    info_parts = []

                    if terminal_count > 0:
                        strategy = terminal_summary.get('strategy', 'Unknown')
                        strategy_name = "ETP形式" if strategy == "ETP_FORM" else "电缆形式"
                        info_parts.append(f"{strategy_name}策略，{terminal_count}个端子排")

                    if card_count > 0:
                        allocated_cards = card_summary.get('allocated_cards', 0)
                        info_parts.append(f"{allocated_cards}/{card_count}个卡件已分配")

                    # 添加电缆完整性信息
                    if cable_violations > 0:
                        info_parts.append(f"⚠️ {cable_violations}个电缆跨柜违规")
                        info_text = "，".join(info_parts)
                        self.failure_reason_label.setText(info_text)
                        self.failure_reason_label.setStyleSheet("color: #FF9500; font-weight: bold;")  # 橙色显示警告
                    else:
                        info_parts.append(f"✓ 电缆完整性{integrity_rate:.0f}%")
                        info_text = "，".join(info_parts)
                        self.failure_reason_label.setText(info_text)
                        self.failure_reason_label.setStyleSheet("color: #007AFF; font-weight: bold;")  # 蓝色显示成功信息

                    self.failure_reason_label.setVisible(True)
        else:
            # 显示失败信息
            error_count = len(allocation_result.errors)
            failed_count = len(allocation_result.failed_points)

            status_msg = f"分配完成但有问题: {error_count}个错误, {failed_count}个失败点"
            self.status_message.emit(status_msg, 5000)

            # 显示主要失败原因
            if allocation_result.errors:
                main_error = allocation_result.errors[0]
                if len(main_error) > 80:
                    main_error = main_error[:77] + "..."
                self.failure_reason_label.setText(f"主要问题: {main_error}")
                self.failure_reason_label.setStyleSheet("color: #FF3B30; font-weight: bold;")  # 红色显示错误
                self.failure_reason_label.setVisible(True)
            elif failed_count > 0:
                self.failure_reason_label.setText(f"有 {failed_count} 个I/O点分配失败")
                self.failure_reason_label.setStyleSheet("color: #FF3B30; font-weight: bold;")
                self.failure_reason_label.setVisible(True)
    
    def _export_results(self):
        """导出结果"""
        self.logger.info("导出分配结果")

        try:
            if not hasattr(self, 'allocation_result') or not self.allocation_result:
                QMessageBox.warning(self, "导出结果", "没有可导出的分配结果")
                return

            from PySide6.QtWidgets import QFileDialog
            import pandas as pd
            from datetime import datetime

            # 选择导出文件路径
            default_filename = f"allocation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出分配结果", default_filename,
                "Excel文件 (*.xlsx);;CSV文件 (*.csv)"
            )

            if not file_path:
                return

            # 准备导出数据
            export_data = self._prepare_export_data(self.allocation_result)

            # 根据文件扩展名选择导出格式
            if file_path.lower().endswith('.xlsx'):
                self._export_to_excel(export_data, file_path)
            elif file_path.lower().endswith('.csv'):
                self._export_to_csv(export_data, file_path)
            else:
                # 默认导出为Excel
                self._export_to_excel(export_data, file_path + '.xlsx')

            QMessageBox.information(self, "导出完成", f"分配结果已导出到:\n{file_path}")
            self.logger.info(f"分配结果导出完成: {file_path}")

        except Exception as e:
            self.logger.error(f"导出分配结果失败: {e}")
            QMessageBox.critical(self, "导出失败", f"导出分配结果失败:\n{str(e)}")

    def _prepare_export_data(self, allocation_result) -> Dict[str, Any]:
        """
        准备导出数据

        Args:
            allocation_result: 分配结果对象

        Returns:
            导出数据字典
        """
        # 合并所有I/O点
        all_points = allocation_result.allocated_points + allocation_result.failed_points

        # 创建主要数据表
        main_data = []
        for io_point in all_points:
            main_data.append({
                'Tag': io_point.tag,
                'Cable': io_point.cable_name,
                'Pair': io_point.pair_number,
                'SignalType': io_point.signal_type.value,
                'Intrinsic': 'IS' if io_point.is_intrinsic else 'NIS',
                'System': io_point.system,
                'Location': io_point.location,
                'CableType': io_point.cable_type,
                'WiringTypical': io_point.wiring_typical,
                'AllocatedCabinet': io_point.allocated_cabinet or '',
                'AllocatedRail': io_point.allocated_rail or '',
                'AllocatedRack': io_point.allocated_rack or '',
                'AllocatedSlot': io_point.allocated_slot or '',
                'Status': io_point.allocation_status,
                'Description': io_point.description
            })

        # 创建统计数据
        summary_data = []
        if allocation_result.allocation_summary:
            summary = allocation_result.allocation_summary
            summary_data = [
                {'项目': '总电缆数', '数值': summary.get('total_cables', 0)},
                {'项目': '总I/O点数', '数值': summary.get('total_points', 0)},
                {'项目': '成功分配', '数值': summary.get('allocated_points', 0)},
                {'项目': '分配失败', '数值': summary.get('failed_points', 0)},
                {'项目': '成功率', '数值': f"{summary.get('success_rate', 0) * 100:.1f}%"}
            ]

        # 创建错误和警告数据
        issues_data = []
        for error in allocation_result.errors:
            issues_data.append({'类型': '错误', '描述': error})
        for warning in allocation_result.warnings:
            issues_data.append({'类型': '警告', '描述': warning})

        return {
            'main_data': main_data,
            'summary_data': summary_data,
            'issues_data': issues_data
        }

    def _export_to_excel(self, export_data: Dict[str, Any], file_path: str):
        """
        导出到Excel文件

        Args:
            export_data: 导出数据
            file_path: 文件路径
        """
        import pandas as pd

        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            # 主要数据表
            if export_data['main_data']:
                df_main = pd.DataFrame(export_data['main_data'])
                df_main.to_excel(writer, sheet_name='分配结果', index=False)

            # 统计数据表
            if export_data['summary_data']:
                df_summary = pd.DataFrame(export_data['summary_data'])
                df_summary.to_excel(writer, sheet_name='统计信息', index=False)

            # 问题数据表
            if export_data['issues_data']:
                df_issues = pd.DataFrame(export_data['issues_data'])
                df_issues.to_excel(writer, sheet_name='错误和警告', index=False)

    def _export_to_csv(self, export_data: Dict[str, Any], file_path: str):
        """
        导出到CSV文件

        Args:
            export_data: 导出数据
            file_path: 文件路径
        """
        import pandas as pd

        # 只导出主要数据到CSV
        if export_data['main_data']:
            df_main = pd.DataFrame(export_data['main_data'])
            df_main.to_csv(file_path, index=False, encoding='utf-8-sig')

    def _apply_material_enhancements(self):
        """应用Material Design增强效果"""
        try:
            # 获取主题管理器
            theme_manager = get_theme_manager()
            if not theme_manager:
                self.logger.warning("主题管理器不可用，跳过Material增强")
                return

            # 为按钮设置Material图标
            self._setup_material_icons(theme_manager)

            # 设置组件属性以适配Material主题
            self._setup_material_properties()

            self.logger.info("Material Design增强应用成功")

        except Exception as e:
            self.logger.error(f"应用Material增强失败: {e}")

    def _setup_material_icons(self, theme_manager):
        """设置Material Design图标"""
        # 为按钮设置Material图标
        button_icons = {
            'iodb_browse_btn': 'folder-open',
            'pidb_browse_btn': 'folder-open',
            'load_iodb_btn': 'upload',
            'load_pidb_btn': 'upload',
            'validate_data_btn': 'check-circle',
            'start_allocation_btn': 'play',
            'export_results_btn': 'download',
            'export_io_report_btn': 'file-export',
            'naming_config_btn': 'cog'
        }

        for btn_name, icon_name in button_icons.items():
            if hasattr(self, btn_name):
                btn = getattr(self, btn_name)
                icon = theme_manager.get_icon(icon_name)
                if icon:
                    btn.setIcon(icon)

    def _setup_material_properties(self):
        """设置Material主题属性"""
        # 设置表格属性
        if hasattr(self, 'result_table'):
            self.result_table.setAlternatingRowColors(True)
            self.result_table.setSelectionBehavior(QTableWidget.SelectRows)
            self.result_table.setShowGrid(False)

            # 设置表格头部
            header = self.result_table.horizontalHeader()
            header.setDefaultAlignment(Qt.AlignLeft | Qt.AlignVCenter)

        # 设置组框属性
        for group_box in self.findChildren(QGroupBox):
            group_box.setFlat(False)

    def _initialize_sample_data(self):
        """初始化示例数据以便测试"""
        try:
            # 创建示例数据
            self.iodb_data = {
                'io_points': [],
                'cables': {},
                'summary': {
                    'total_points': 10,
                    'total_cables': 3,
                    'signal_types': {'AI': 6, 'DI': 4},
                    'systems': {'SYS_A', 'SYS_B'},
                    'locations': {'AREA_1', 'AREA_2'}
                }
            }

            self.cabinet_profiles = {
                'CABINET_001': {
                    'name': 'CABINET_001',
                    'type': 'Mixed',
                    'rails': ['RAIL_001'],
                    'racks': ['RACK_001']
                }
            }

            self.wiring_typicals = {
                'AI_IS_BABP': {
                    'name': 'AI_IS_BABP',
                    'signal_type': 'AI',
                    'is_intrinsic': True,
                    'components': []
                }
            }

            # 更新界面状态
            self.validate_data_btn.setEnabled(True)
            self.start_allocation_btn.setEnabled(True)

            # 更新数据摘要显示
            self._update_data_summary({
                'iodb': self.iodb_data,
                'cabinet_profiles': self.cabinet_profiles,
                'wiring_typicals': self.wiring_typicals
            })

            # 显示提示信息
            self.log_text.append("✓ 示例数据已加载，可以直接进行I/O分配测试")
            self.log_text.append("  - 包含10个I/O点示例")
            self.log_text.append("  - 包含3条电缆示例")
            self.log_text.append("  - 包含1个机柜配置")
            self.log_text.append("  - 可以点击'开始分配'进行测试")

            self.logger.info("示例数据初始化完成")

        except Exception as e:
            self.logger.error(f"初始化示例数据失败: {e}")
    
    def validate_data(self):
        """外部调用的数据验证接口"""
        if self.validate_data_btn.isEnabled():
            self._validate_data()
        else:
            QMessageBox.warning(self, "数据验证", "请先加载数据文件")

    def _update_data_summary(self, all_data: Dict[str, Any]):
        """
        更新数据摘要显示

        Args:
            all_data: 所有数据
        """
        summary_text = "数据加载摘要:\n"

        # IODB数据摘要
        if all_data.get('iodb'):
            iodb_summary = all_data['iodb'].get('summary', {})
            summary_text += f"• IODB: {iodb_summary.get('total_points', 0)}个I/O点, {iodb_summary.get('total_cables', 0)}条电缆\n"

        # PIDB数据摘要
        if all_data.get('pidb'):
            pidb_data = all_data['pidb']
            summary_text += f"• PIDB: {len(pidb_data.get('chassis', []))}个机架, {len(pidb_data.get('cabinets', []))}个机柜\n"

        # 机柜配置摘要
        cabinet_profiles = all_data.get('cabinet_profiles', {})
        summary_text += f"• 机柜配置: {len(cabinet_profiles)}个配置文件\n"

        # 典型回路摘要
        wiring_typicals = all_data.get('wiring_typicals', {})
        summary_text += f"• 典型回路: {len(wiring_typicals)}个回路文件\n"

        # 显示在日志区域
        self.log_text.append(summary_text)

    def _display_validation_results(self, validation_result):
        """
        显示验证结果

        Args:
            validation_result: 验证结果对象
        """
        self.log_text.append("\n=== 数据验证结果 ===")

        if validation_result.success:
            self.log_text.append("✓ 数据验证通过")
        else:
            self.log_text.append("✗ 数据验证失败")

        # 显示错误
        if validation_result.errors:
            self.log_text.append(f"\n错误 ({len(validation_result.errors)}):")
            for error in validation_result.errors:
                self.log_text.append(f"  • {error}")

        # 显示警告
        if validation_result.warnings:
            self.log_text.append(f"\n警告 ({len(validation_result.warnings)}):")
            for warning in validation_result.warnings:
                self.log_text.append(f"  • {warning}")

        # 显示统计信息
        if validation_result.allocation_summary:
            summary = validation_result.allocation_summary
            self.log_text.append(f"\n统计信息:")
            self.log_text.append(f"  • 总I/O点数: {summary.get('total_io_points', 0)}")
            self.log_text.append(f"  • 总电缆数: {summary.get('total_cables', 0)}")
            self.log_text.append(f"  • 验证错误: {summary.get('validation_errors', 0)}")
            self.log_text.append(f"  • 验证警告: {summary.get('validation_warnings', 0)}")

        self.log_text.append("=" * 30)

    def _update_result_table(self, allocation_result=None):
        """
        更新结果表格

        Args:
            allocation_result: 分配结果对象
        """
        if not allocation_result:
            # 清空表格
            self.result_table.setRowCount(0)
            self.result_table.setColumnCount(0)
            return

        # 设置表格列
        columns = ['Tag', 'Cable', 'Pair', 'Signal', 'IS', 'Cabinet', 'Rail', 'Rack', 'Slot', 'Status']
        self.result_table.setColumnCount(len(columns))
        self.result_table.setHorizontalHeaderLabels(columns)

        # 添加成功分配的点
        all_points = allocation_result.allocated_points + allocation_result.failed_points
        self.result_table.setRowCount(len(all_points))

        for row, io_point in enumerate(all_points):
            self.result_table.setItem(row, 0, QTableWidgetItem(io_point.tag))
            self.result_table.setItem(row, 1, QTableWidgetItem(io_point.cable_name))
            self.result_table.setItem(row, 2, QTableWidgetItem(str(io_point.pair_number)))
            self.result_table.setItem(row, 3, QTableWidgetItem(io_point.signal_type.value))
            self.result_table.setItem(row, 4, QTableWidgetItem('IS' if io_point.is_intrinsic else 'NIS'))
            self.result_table.setItem(row, 5, QTableWidgetItem(io_point.allocated_cabinet or ''))
            self.result_table.setItem(row, 6, QTableWidgetItem(io_point.allocated_rail or ''))
            self.result_table.setItem(row, 7, QTableWidgetItem(io_point.allocated_rack or ''))
            self.result_table.setItem(row, 8, QTableWidgetItem(io_point.allocated_slot or ''))
            self.result_table.setItem(row, 9, QTableWidgetItem(io_point.allocation_status))

        # 调整列宽
        header = self.result_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(columns) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def _display_allocation_results(self, allocation_result):
        """
        显示分配结果

        Args:
            allocation_result: 分配结果对象
        """
        self.log_text.append("\n=== I/O点分配结果 ===")

        if allocation_result.success:
            self.log_text.append("✓ I/O点分配成功完成")
        else:
            self.log_text.append("✗ I/O点分配完成但有错误")

        # 显示统计信息
        if allocation_result.allocation_summary:
            summary = allocation_result.allocation_summary
            self.log_text.append(f"\n分配统计:")
            self.log_text.append(f"  • 总电缆数: {summary.get('total_cables', 0)}")
            self.log_text.append(f"  • 总I/O点数: {summary.get('total_points', 0)}")
            self.log_text.append(f"  • 成功分配: {summary.get('allocated_points', 0)}")
            self.log_text.append(f"  • 分配失败: {summary.get('failed_points', 0)}")

            success_rate = summary.get('success_rate', 0) * 100
            self.log_text.append(f"  • 成功率: {success_rate:.1f}%")

        # 显示错误
        if allocation_result.errors:
            self.log_text.append(f"\n错误 ({len(allocation_result.errors)}):")
            for error in allocation_result.errors[:10]:  # 只显示前10个错误
                self.log_text.append(f"  • {error}")
            if len(allocation_result.errors) > 10:
                self.log_text.append(f"  ... 还有 {len(allocation_result.errors) - 10} 个错误")

        # 显示警告
        if allocation_result.warnings:
            self.log_text.append(f"\n警告 ({len(allocation_result.warnings)}):")
            for warning in allocation_result.warnings[:5]:  # 只显示前5个警告
                self.log_text.append(f"  • {warning}")
            if len(allocation_result.warnings) > 5:
                self.log_text.append(f"  ... 还有 {len(allocation_result.warnings) - 5} 个警告")

        self.log_text.append("=" * 30)

    def _export_io_report(self):
        """导出IO分配表"""
        self.logger.info("导出IO分配表")

        try:
            if not hasattr(self, 'allocation_result') or not self.allocation_result:
                QMessageBox.warning(self, "导出IO分配表", "没有可导出的分配结果")
                return

            from PySide6.QtWidgets import QFileDialog
            from datetime import datetime
            from core.report_generator import ReportTemplate, ReportManager
            from core.pidb_enhanced_reader import PidbEnhancedReader
            from core.naming_engine import NamingEngine
            import os

            # 选择导出文件路径
            default_filename = f"IO_allocation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出IO分配表", default_filename,
                "Excel文件 (*.xlsx)"
            )

            if not file_path:
                return

            # 检查IOL.xlsx模板是否存在
            template_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "杂项", "IOL.xlsx")
            if not os.path.exists(template_path):
                QMessageBox.critical(self, "模板缺失", f"找不到IOL.xlsx模板文件:\n{template_path}")
                return

            # 创建报表管理器
            report_manager = ReportManager()

            # 注册IOL模板
            iol_template = ReportTemplate(template_path, "IOL模板", "IO分配表模板")
            report_manager.register_template("iol", iol_template)

            # 准备数据源
            data_sources = {
                'allocation_result': self.allocation_result,
                'naming_rules': self._get_naming_rules(),
                'pidb_data': self._load_pidb_data(),
                'wiring_typicals': self._get_wiring_typicals()
            }

            # 生成报表
            success = report_manager.generate_report(
                report_type='io_allocation',
                template_id='iol',
                output_path=file_path,
                data_sources=data_sources
            )

            if success:
                QMessageBox.information(self, "导出完成", f"IO分配表已导出到:\n{file_path}")
                self.logger.info(f"IO分配表导出完成: {file_path}")
            else:
                QMessageBox.critical(self, "导出失败", "IO分配表导出失败，请查看日志获取详细信息")

        except Exception as e:
            self.logger.error(f"导出IO分配表失败: {e}")
            QMessageBox.critical(self, "导出失败", f"导出IO分配表失败:\n{str(e)}")

    def _get_naming_rules(self) -> dict:
        """获取命名规则"""
        # 从配置中读取用户自定义的命名规则
        return self._load_naming_config()

    def _load_pidb_data(self) -> dict:
        """加载PIDB数据"""
        try:
            pidb_path = self.pidb_path_edit.text().strip()
            if not pidb_path:
                self.logger.warning("PIDB文件路径为空")
                return {}

            pidb_reader = PidbEnhancedReader()
            return pidb_reader.read_pidb_file(pidb_path)

        except Exception as e:
            self.logger.error(f"加载PIDB数据失败: {e}")
            return {}

    def _get_wiring_typicals(self) -> dict:
        """获取典型回路数据"""
        # 这里需要从实际的典型回路数据中获取，暂时返回空字典
        return {}

    def _configure_naming_rules(self):
        """配置命名规则"""
        self.logger.info("打开命名规则配置对话框")

        try:
            # 使用简化的命名配置对话框
            from gui.simple_naming_dialog import SimpleNamingDialog

            # 获取当前配置
            current_config = self._load_naming_config()

            # 创建配置对话框
            dialog = SimpleNamingDialog(self, current_config)
            dialog.config_updated.connect(self._save_naming_config)

            # 显示对话框
            if dialog.exec() == dialog.Accepted:
                self.logger.info("命名规则配置已更新")
                QMessageBox.information(self, "配置完成", "命名规则配置已保存")
            else:
                self.logger.info("用户取消了命名规则配置")

        except Exception as e:
            self.logger.error(f"打开命名规则配置对话框失败: {e}")
            QMessageBox.critical(self, "配置失败", f"无法打开命名规则配置:\n{str(e)}")

    def _load_naming_config(self) -> dict:
        """加载命名配置"""
        try:
            # 从配置文件或数据库中加载命名规则配置
            # 这里暂时返回默认配置
            # 默认配置（新的灵活格式）
            default_config = {
                'barrier': {
                    'elements': [
                        {'type': 'fixed_text', 'display_name': '固定文本: BA', 'description': '安全栅前缀', 'custom_text': 'BA', 'custom_suffix': ''},
                        {'type': 'rack_number', 'display_name': '机架编号', 'description': '机架编号', 'custom_text': '', 'custom_suffix': ''},
                        {'type': 'slot_number', 'display_name': '槽位编号', 'description': '槽位编号', 'custom_text': '', 'custom_suffix': ''},
                        {'type': 'channel_number', 'display_name': '通道号', 'description': '通道号', 'custom_text': '', 'custom_suffix': ''}
                    ]
                },
                'relay': {
                    'elements': [
                        {'type': 'fixed_text', 'display_name': '固定文本: RY', 'description': '继电器前缀', 'custom_text': 'RY', 'custom_suffix': ''},
                        {'type': 'rack_number', 'display_name': '机架编号', 'description': '机架编号', 'custom_text': '', 'custom_suffix': ''},
                        {'type': 'slot_number', 'display_name': '槽位编号', 'description': '槽位编号', 'custom_text': '', 'custom_suffix': ''},
                        {'type': 'channel_number', 'display_name': '通道号', 'description': '通道号', 'custom_text': '', 'custom_suffix': ''}
                    ]
                },
                'isolator': {
                    'elements': [
                        {'type': 'fixed_text', 'display_name': '固定文本: ISL', 'description': '隔离器前缀', 'custom_text': 'ISL', 'custom_suffix': ''},
                        {'type': 'rack_number', 'display_name': '机架编号', 'description': '机架编号', 'custom_text': '', 'custom_suffix': ''},
                        {'type': 'slot_number', 'display_name': '槽位编号', 'description': '槽位编号', 'custom_text': '', 'custom_suffix': ''},
                        {'type': 'channel_number', 'display_name': '通道号', 'description': '通道号', 'custom_text': '', 'custom_suffix': ''}
                    ]
                },
                'surge_protector': {
                    'elements': [
                        {'type': 'fixed_text', 'display_name': '固定文本: SP', 'description': '防雷栅前缀', 'custom_text': 'SP', 'custom_suffix': ''},
                        {'type': 'rack_number', 'display_name': '机架编号', 'description': '机架编号', 'custom_text': '', 'custom_suffix': ''},
                        {'type': 'slot_number', 'display_name': '槽位编号', 'description': '槽位编号', 'custom_text': '', 'custom_suffix': ''},
                        {'type': 'channel_number', 'display_name': '通道号', 'description': '通道号', 'custom_text': '', 'custom_suffix': ''}
                    ]
                },
                'terminal_block': {
                    'elements': [
                        {'type': 'fixed_text', 'display_name': '固定文本: TB', 'description': '端子排前缀', 'custom_text': 'TB', 'custom_suffix': ''},
                        {'type': 'cabinet_number', 'display_name': '机柜编号', 'description': '机柜编号', 'custom_text': '', 'custom_suffix': ''},
                        {'type': 'rail_number', 'display_name': '导轨号', 'description': '导轨号', 'custom_text': '', 'custom_suffix': ''},
                        {'type': 'device_number', 'display_name': '器件编号', 'description': '器件编号', 'custom_text': '', 'custom_suffix': ''}
                    ]
                },
                'tr_terminal': {
                    'elements': [
                        {'type': 'fixed_text', 'display_name': '固定文本: TR', 'description': 'TR端子排前缀', 'custom_text': 'TR', 'custom_suffix': ''},
                        {'type': 'etp_name', 'display_name': 'ETP名', 'description': 'ETP名称', 'custom_text': '', 'custom_suffix': ''}
                    ]
                }
            }

            # 尝试从配置文件加载
            config_file = self.config.get('naming_rules_config', '')
            if config_file and os.path.exists(config_file):
                import json
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    default_config.update(saved_config)

            return default_config

        except Exception as e:
            self.logger.warning(f"加载命名配置失败，使用默认配置: {e}")
            return {}

    def _save_naming_config(self, config: dict):
        """保存命名配置"""
        try:
            # 保存配置到文件
            config_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config')
            os.makedirs(config_dir, exist_ok=True)

            config_file = os.path.join(config_dir, 'naming_rules.json')

            import json
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            # 更新配置管理器
            self.config.set('naming_rules_config', config_file)

            self.logger.info(f"命名规则配置已保存到: {config_file}")

        except Exception as e:
            self.logger.error(f"保存命名配置失败: {e}")
            raise
