# EWReborn Material Design 主题系统使用指南

## 🎨 概述

EWReborn项目已成功集成qt-material框架，提供现代化的Material Design界面体验。本指南将帮助您了解如何使用和配置Material主题系统。

## 📦 安装依赖

### 必需依赖
```bash
pip install qt-material qtawesome
```

### 可选依赖（用于开发）
```bash
pip install -r requirements.txt
```

## 🚀 快速开始

### 1. 启动应用程序
```bash
python main.py
```

### 2. 切换主题
- 通过菜单栏：**视图 → 主题**
- 选择深色主题或浅色主题
- 主题会立即应用并保存配置

## 🎯 主要功能

### ✅ 已实现功能

1. **Material Design主题系统**
   - 19种内置主题（深色/浅色）
   - 运行时主题切换
   - 主题配置持久化

2. **备用样式系统**
   - 当qt-material不可用时自动启用
   - 提供基础的深色/浅色主题
   - 确保应用程序始终可用

3. **Material图标集成**
   - 基于FontAwesome的图标系统
   - 自动适配主题颜色
   - 支持图标回退机制

4. **响应式布局**
   - 适配不同窗口大小
   - 优化的组件间距
   - 改进的可访问性

### 🔧 技术特性

- **完全向后兼容**：保持所有现有功能不变
- **优雅降级**：qt-material不可用时使用备用样式
- **配置管理**：主题设置自动保存和加载
- **信号系统**：主题切换事件通知

## 📋 可用主题

### 深色主题
- dark_amber.xml
- dark_blue.xml
- dark_cyan.xml
- dark_lightgreen.xml
- dark_pink.xml
- dark_purple.xml
- dark_red.xml
- dark_teal.xml（默认）
- dark_yellow.xml

### 浅色主题
- light_amber.xml
- light_blue.xml
- light_cyan.xml
- light_cyan_500.xml
- light_lightgreen.xml
- light_pink.xml
- light_purple.xml
- light_red.xml
- light_teal.xml
- light_yellow.xml

## 🛠️ 配置选项

### 主题配置文件
主题设置保存在应用程序配置中：
```json
{
  "gui": {
    "material_theme": "dark_teal.xml"
  }
}
```

### 自定义主题
可以通过修改`gui/material_theme.py`中的`_get_extra_config()`方法来自定义主题样式。

## 🧪 测试和验证

### 运行测试脚本
```bash
# 测试Material主题集成
python test_material_theme.py

# 测试应用程序启动
python test_app_startup.py
```

### 验证功能
1. ✅ 应用程序正常启动
2. ✅ 主题菜单正确显示
3. ✅ 主题切换功能正常
4. ✅ 配置保存和加载
5. ✅ 备用样式系统工作

## 🔍 故障排除

### 常见问题

**Q: 主题菜单为空或主题切换无效果**
A: 请确保已安装qt-material：`pip install qt-material qtawesome`

**Q: 应用程序界面显示异常**
A: 检查是否有Python包冲突，尝试重新安装依赖

**Q: 主题设置不保存**
A: 确保应用程序有写入配置文件的权限

### 日志信息
查看应用程序日志文件：`logs/ewreborn.log`

关键日志信息：
- `Material主题应用成功`：qt-material主题应用成功
- `备用主题应用成功`：使用备用样式系统
- `主题切换成功`：主题切换操作完成

## 🔮 未来计划

### 计划中的功能
1. **自定义主题编辑器**
   - 可视化主题配置
   - 实时预览效果
   - 导出自定义主题

2. **高级样式选项**
   - 字体大小调节
   - 密度缩放设置
   - 动画效果控制

3. **主题同步**
   - 跟随系统主题
   - 定时切换主题
   - 主题分享功能

## 📞 支持

如果您在使用过程中遇到问题，请：

1. 查看本指南的故障排除部分
2. 检查应用程序日志文件
3. 运行测试脚本验证安装
4. 联系开发团队获取支持

---

**版本**: 1.0.0  
**更新日期**: 2025-01-25  
**兼容性**: PySide6 6.5.0+, Python 3.8+
