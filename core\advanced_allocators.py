"""
高级分配策略
提供负载均衡、优先级分配和空间优化等高级分配算法
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from abc import ABC, abstractmethod
import heapq
from collections import defaultdict

from core.logger import get_logger
from core.data_models import IOPoint, SignalType, AllocationResult


class AllocationStrategy(Enum):
    """分配策略枚举"""
    PRIORITY_BASED = "PRIORITY_BASED"
    LOAD_BALANCING = "LOAD_BALANCING"
    SPACE_OPTIMIZED = "SPACE_OPTIMIZED"
    CONSTRAINT_BASED = "CONSTRAINT_BASED"


@dataclass
class AllocationPriority:
    """分配优先级"""
    signal_type: SignalType
    priority: int
    weight: float = 1.0
    constraints: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.constraints is None:
            self.constraints = {}


@dataclass
class ResourceUtilization:
    """资源利用率"""
    resource_id: str
    resource_type: str
    total_capacity: int
    used_capacity: int
    utilization_rate: float = 0.0
    
    def __post_init__(self):
        if self.total_capacity > 0:
            self.utilization_rate = self.used_capacity / self.total_capacity


@dataclass
class AllocationConstraint:
    """分配约束"""
    constraint_type: str
    target_resource: str
    constraint_value: Any
    priority: int = 1
    description: str = ""


class AdvancedAllocatorBase(ABC):
    """高级分配器基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化高级分配器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = get_logger(__name__)
        self.allocation_history: List[Dict[str, Any]] = []
    
    @abstractmethod
    def allocate(self, io_points: List[IOPoint], 
                resources: Dict[str, Any]) -> AllocationResult:
        """
        执行分配
        
        Args:
            io_points: I/O点列表
            resources: 资源字典
            
        Returns:
            分配结果
        """
        pass
    
    def _calculate_resource_utilization(self, resources: Dict[str, Any]) -> List[ResourceUtilization]:
        """
        计算资源利用率
        
        Args:
            resources: 资源字典
            
        Returns:
            资源利用率列表
        """
        utilizations = []
        
        for resource_type, resource_list in resources.items():
            for resource in resource_list:
                if isinstance(resource, dict):
                    utilization = ResourceUtilization(
                        resource_id=resource.get('name', ''),
                        resource_type=resource_type,
                        total_capacity=resource.get('max_capacity', 0),
                        used_capacity=resource.get('used_capacity', 0)
                    )
                    utilizations.append(utilization)
        
        return utilizations
    
    def _record_allocation(self, io_point: IOPoint, resource_info: Dict[str, Any]):
        """
        记录分配历史
        
        Args:
            io_point: I/O点
            resource_info: 资源信息
        """
        self.allocation_history.append({
            'io_point_tag': io_point.tag,
            'resource_info': resource_info,
            'timestamp': self._get_current_timestamp()
        })
    
    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()


class PriorityBasedAllocator(AdvancedAllocatorBase):
    """优先级分配器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化优先级分配器"""
        super().__init__(config)
        self.priorities = self._load_priorities()
    
    def _load_priorities(self) -> Dict[SignalType, AllocationPriority]:
        """加载分配优先级"""
        default_priorities = {
            SignalType.AI: AllocationPriority(SignalType.AI, 1, 1.0),
            SignalType.AO: AllocationPriority(SignalType.AO, 2, 0.9),
            SignalType.DI: AllocationPriority(SignalType.DI, 3, 0.8),
            SignalType.DO: AllocationPriority(SignalType.DO, 4, 0.7)
        }
        
        # 从配置中加载自定义优先级
        priority_config = self.config.get('allocation_priorities', {})
        for signal_type_str, priority_data in priority_config.items():
            try:
                signal_type = SignalType(signal_type_str)
                priority = AllocationPriority(
                    signal_type=signal_type,
                    priority=priority_data.get('priority', 1),
                    weight=priority_data.get('weight', 1.0),
                    constraints=priority_data.get('constraints', {})
                )
                default_priorities[signal_type] = priority
            except ValueError:
                self.logger.warning(f"无效的信号类型: {signal_type_str}")
        
        return default_priorities
    
    def allocate(self, io_points: List[IOPoint], 
                resources: Dict[str, Any]) -> AllocationResult:
        """
        基于优先级执行分配
        
        Args:
            io_points: I/O点列表
            resources: 资源字典
            
        Returns:
            分配结果
        """
        result = AllocationResult()
        
        try:
            # 按优先级排序I/O点
            sorted_points = self._sort_by_priority(io_points)
            
            # 获取可用资源
            available_resources = self._get_available_resources(resources)
            
            # 执行分配
            for io_point in sorted_points:
                allocation_success = self._allocate_single_point(
                    io_point, available_resources, result
                )
                
                if allocation_success:
                    result.allocated_points.append(io_point)
                else:
                    result.failed_points.append(io_point)
            
            result.success = len(result.failed_points) == 0
            
            # 生成分配摘要
            result.summary = self._generate_priority_summary(result)
            
            self.logger.info(f"优先级分配完成，成功: {len(result.allocated_points)}, 失败: {len(result.failed_points)}")
            
        except Exception as e:
            result.errors.append(f"优先级分配异常: {e}")
            self.logger.error(f"优先级分配异常: {e}")
        
        return result
    
    def _sort_by_priority(self, io_points: List[IOPoint]) -> List[IOPoint]:
        """
        按优先级排序I/O点
        
        Args:
            io_points: I/O点列表
            
        Returns:
            排序后的I/O点列表
        """
        def get_priority_key(point: IOPoint) -> Tuple[int, float]:
            priority_info = self.priorities.get(point.signal_type)
            if priority_info:
                return (priority_info.priority, -priority_info.weight)
            return (999, 0.0)  # 未知类型的低优先级
        
        return sorted(io_points, key=get_priority_key)
    
    def _get_available_resources(self, resources: Dict[str, Any]) -> Dict[str, List[Any]]:
        """
        获取可用资源
        
        Args:
            resources: 资源字典
            
        Returns:
            可用资源字典
        """
        available = {}
        
        for resource_type, resource_list in resources.items():
            available[resource_type] = []
            for resource in resource_list:
                if self._is_resource_available(resource):
                    available[resource_type].append(resource)
        
        return available
    
    def _is_resource_available(self, resource: Any) -> bool:
        """
        检查资源是否可用
        
        Args:
            resource: 资源对象
            
        Returns:
            是否可用
        """
        if isinstance(resource, dict):
            used_capacity = resource.get('used_capacity', 0)
            max_capacity = resource.get('max_capacity', 0)
            return used_capacity < max_capacity
        
        return True
    
    def _allocate_single_point(self, io_point: IOPoint, 
                              available_resources: Dict[str, List[Any]], 
                              result: AllocationResult) -> bool:
        """
        分配单个I/O点
        
        Args:
            io_point: I/O点
            available_resources: 可用资源
            result: 分配结果
            
        Returns:
            分配是否成功
        """
        try:
            # 简化的分配逻辑
            cabinets = available_resources.get('cabinets', [])
            if not cabinets:
                result.errors.append(f"没有可用的机柜分配给 {io_point.tag}")
                return False
            
            # 选择第一个可用机柜
            target_cabinet = cabinets[0]
            
            # 更新I/O点分配信息
            io_point.cabinet = target_cabinet.get('name', 'Cabinet01')
            io_point.rack = "R01"
            io_point.slot = "01"
            io_point.channel = "01"
            
            # 记录分配
            self._record_allocation(io_point, {
                'cabinet': io_point.cabinet,
                'strategy': 'PRIORITY_BASED'
            })
            
            return True
            
        except Exception as e:
            result.errors.append(f"分配I/O点 {io_point.tag} 失败: {e}")
            return False
    
    def _generate_priority_summary(self, result: AllocationResult) -> Dict[str, Any]:
        """
        生成优先级分配摘要
        
        Args:
            result: 分配结果
            
        Returns:
            摘要字典
        """
        summary = {
            'strategy': 'PRIORITY_BASED',
            'total_points': len(result.allocated_points) + len(result.failed_points),
            'allocated_points': len(result.allocated_points),
            'failed_points': len(result.failed_points),
            'success_rate': 0.0
        }
        
        if summary['total_points'] > 0:
            summary['success_rate'] = summary['allocated_points'] / summary['total_points']
        
        # 按信号类型统计
        signal_type_stats = defaultdict(int)
        for point in result.allocated_points:
            signal_type = point.signal_type.value if hasattr(point.signal_type, 'value') else str(point.signal_type)
            signal_type_stats[signal_type] += 1
        
        summary['signal_type_distribution'] = dict(signal_type_stats)
        
        return summary


class LoadBalancingAllocator(AdvancedAllocatorBase):
    """负载均衡分配器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化负载均衡分配器"""
        super().__init__(config)
        self.load_threshold = config.get('load_balancing', {}).get('threshold', 0.8)
    
    def allocate(self, io_points: List[IOPoint], 
                resources: Dict[str, Any]) -> AllocationResult:
        """
        基于负载均衡执行分配
        
        Args:
            io_points: I/O点列表
            resources: 资源字典
            
        Returns:
            分配结果
        """
        result = AllocationResult()
        
        try:
            # 计算当前资源利用率
            utilizations = self._calculate_resource_utilization(resources)
            
            # 创建负载均衡堆
            load_heap = self._create_load_heap(utilizations)
            
            # 执行负载均衡分配
            for io_point in io_points:
                allocation_success = self._allocate_with_load_balancing(
                    io_point, load_heap, result
                )
                
                if allocation_success:
                    result.allocated_points.append(io_point)
                else:
                    result.failed_points.append(io_point)
            
            result.success = len(result.failed_points) == 0
            
            # 生成负载均衡摘要
            result.summary = self._generate_load_balancing_summary(result, utilizations)
            
            self.logger.info(f"负载均衡分配完成，成功: {len(result.allocated_points)}, 失败: {len(result.failed_points)}")
            
        except Exception as e:
            result.errors.append(f"负载均衡分配异常: {e}")
            self.logger.error(f"负载均衡分配异常: {e}")
        
        return result
    
    def _create_load_heap(self, utilizations: List[ResourceUtilization]) -> List[Tuple[float, str]]:
        """
        创建负载均衡堆
        
        Args:
            utilizations: 资源利用率列表
            
        Returns:
            负载堆
        """
        heap = []
        for util in utilizations:
            heapq.heappush(heap, (util.utilization_rate, util.resource_id))
        
        return heap
    
    def _allocate_with_load_balancing(self, io_point: IOPoint, 
                                     load_heap: List[Tuple[float, str]], 
                                     result: AllocationResult) -> bool:
        """
        使用负载均衡分配I/O点
        
        Args:
            io_point: I/O点
            load_heap: 负载堆
            result: 分配结果
            
        Returns:
            分配是否成功
        """
        try:
            if not load_heap:
                result.errors.append(f"没有可用资源分配给 {io_point.tag}")
                return False
            
            # 选择负载最低的资源
            current_load, resource_id = heapq.heappop(load_heap)
            
            # 检查负载阈值
            if current_load >= self.load_threshold:
                result.warnings.append(f"资源 {resource_id} 负载过高: {current_load:.2f}")
            
            # 更新I/O点分配信息
            io_point.cabinet = resource_id
            io_point.rack = "R01"
            io_point.slot = "01"
            io_point.channel = "01"
            
            # 更新负载并重新加入堆
            new_load = current_load + 0.01  # 简化的负载增量
            heapq.heappush(load_heap, (new_load, resource_id))
            
            # 记录分配
            self._record_allocation(io_point, {
                'resource_id': resource_id,
                'load_before': current_load,
                'load_after': new_load,
                'strategy': 'LOAD_BALANCING'
            })
            
            return True
            
        except Exception as e:
            result.errors.append(f"负载均衡分配I/O点 {io_point.tag} 失败: {e}")
            return False
    
    def _generate_load_balancing_summary(self, result: AllocationResult, 
                                        utilizations: List[ResourceUtilization]) -> Dict[str, Any]:
        """
        生成负载均衡摘要
        
        Args:
            result: 分配结果
            utilizations: 资源利用率列表
            
        Returns:
            摘要字典
        """
        summary = {
            'strategy': 'LOAD_BALANCING',
            'total_points': len(result.allocated_points) + len(result.failed_points),
            'allocated_points': len(result.allocated_points),
            'failed_points': len(result.failed_points),
            'load_threshold': self.load_threshold
        }
        
        # 计算负载统计
        if utilizations:
            loads = [util.utilization_rate for util in utilizations]
            summary['load_statistics'] = {
                'min_load': min(loads),
                'max_load': max(loads),
                'avg_load': sum(loads) / len(loads),
                'load_variance': self._calculate_variance(loads)
            }
        
        return summary
    
    def _calculate_variance(self, values: List[float]) -> float:
        """
        计算方差
        
        Args:
            values: 数值列表
            
        Returns:
            方差
        """
        if not values:
            return 0.0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        return variance


class SpaceOptimizedAllocator(AdvancedAllocatorBase):
    """空间优化分配器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化空间优化分配器"""
        super().__init__(config)
        self.space_efficiency_target = config.get('space_optimization', {}).get('efficiency_target', 0.85)
    
    def allocate(self, io_points: List[IOPoint], 
                resources: Dict[str, Any]) -> AllocationResult:
        """
        基于空间优化执行分配
        
        Args:
            io_points: I/O点列表
            resources: 资源字典
            
        Returns:
            分配结果
        """
        result = AllocationResult()
        
        try:
            # 按空间需求分组I/O点
            grouped_points = self._group_by_space_requirements(io_points)
            
            # 计算空间利用率
            space_utilizations = self._calculate_space_utilization(resources)
            
            # 执行空间优化分配
            for group_name, points in grouped_points.items():
                self._allocate_group_optimized(points, space_utilizations, result)
            
            result.success = len(result.failed_points) == 0
            
            # 生成空间优化摘要
            result.summary = self._generate_space_optimization_summary(result, space_utilizations)
            
            self.logger.info(f"空间优化分配完成，成功: {len(result.allocated_points)}, 失败: {len(result.failed_points)}")
            
        except Exception as e:
            result.errors.append(f"空间优化分配异常: {e}")
            self.logger.error(f"空间优化分配异常: {e}")
        
        return result
    
    def _group_by_space_requirements(self, io_points: List[IOPoint]) -> Dict[str, List[IOPoint]]:
        """
        按空间需求分组I/O点
        
        Args:
            io_points: I/O点列表
            
        Returns:
            分组字典
        """
        groups = defaultdict(list)
        
        for point in io_points:
            # 简化的分组逻辑，基于信号类型
            group_key = f"{point.signal_type.value}_{point.system}"
            groups[group_key].append(point)
        
        return dict(groups)
    
    def _calculate_space_utilization(self, resources: Dict[str, Any]) -> Dict[str, float]:
        """
        计算空间利用率
        
        Args:
            resources: 资源字典
            
        Returns:
            空间利用率字典
        """
        utilizations = {}
        
        for resource_type, resource_list in resources.items():
            for resource in resource_list:
                if isinstance(resource, dict):
                    resource_id = resource.get('name', '')
                    used_space = resource.get('used_space', 0)
                    total_space = resource.get('total_space', 100)
                    
                    if total_space > 0:
                        utilizations[resource_id] = used_space / total_space
                    else:
                        utilizations[resource_id] = 0.0
        
        return utilizations
    
    def _allocate_group_optimized(self, points: List[IOPoint], 
                                 space_utilizations: Dict[str, float], 
                                 result: AllocationResult):
        """
        优化分配点组
        
        Args:
            points: I/O点列表
            space_utilizations: 空间利用率
            result: 分配结果
        """
        # 选择空间利用率最优的资源
        best_resource = self._select_optimal_resource(space_utilizations, len(points))
        
        for point in points:
            if best_resource:
                point.cabinet = best_resource
                point.rack = "R01"
                point.slot = "01"
                point.channel = "01"
                
                result.allocated_points.append(point)
                
                # 记录分配
                self._record_allocation(point, {
                    'resource': best_resource,
                    'strategy': 'SPACE_OPTIMIZED'
                })
            else:
                result.failed_points.append(point)
    
    def _select_optimal_resource(self, space_utilizations: Dict[str, float], 
                                points_count: int) -> Optional[str]:
        """
        选择最优资源
        
        Args:
            space_utilizations: 空间利用率
            points_count: 点数量
            
        Returns:
            最优资源ID
        """
        if not space_utilizations:
            return None
        
        # 选择利用率接近目标效率的资源
        best_resource = None
        best_score = float('inf')
        
        for resource_id, utilization in space_utilizations.items():
            # 计算分配后的预期利用率
            estimated_utilization = utilization + (points_count * 0.01)  # 简化估算
            
            # 计算与目标效率的差距
            score = abs(estimated_utilization - self.space_efficiency_target)
            
            if score < best_score:
                best_score = score
                best_resource = resource_id
        
        return best_resource
    
    def _generate_space_optimization_summary(self, result: AllocationResult, 
                                           space_utilizations: Dict[str, float]) -> Dict[str, Any]:
        """
        生成空间优化摘要
        
        Args:
            result: 分配结果
            space_utilizations: 空间利用率
            
        Returns:
            摘要字典
        """
        summary = {
            'strategy': 'SPACE_OPTIMIZED',
            'total_points': len(result.allocated_points) + len(result.failed_points),
            'allocated_points': len(result.allocated_points),
            'failed_points': len(result.failed_points),
            'efficiency_target': self.space_efficiency_target
        }
        
        # 计算空间效率统计
        if space_utilizations:
            utilizations = list(space_utilizations.values())
            summary['space_statistics'] = {
                'min_utilization': min(utilizations),
                'max_utilization': max(utilizations),
                'avg_utilization': sum(utilizations) / len(utilizations),
                'target_efficiency': self.space_efficiency_target
            }
        
        return summary
