"""
测试默认前缀功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_default_prefix_mapping():
    """测试默认前缀映射"""
    print("测试默认前缀映射...")
    
    try:
        from gui.simple_naming_dialog import FlexibleNamingDialog
        
        # 创建临时对话框实例来测试方法
        class TestDialog:
            def _get_default_prefix(self, device_type: str) -> str:
                default_prefixes = {
                    'barrier': 'BA',
                    'relay': 'RY',
                    'isolator': 'ISL',
                    'surge_protector': 'SP',
                    'terminal_block': 'TB',
                    'tr_terminal': 'TR'
                }
                return default_prefixes.get(device_type, 'DEV')
        
        test_dialog = TestDialog()
        
        # 测试所有器件类型的默认前缀
        expected_prefixes = {
            'barrier': 'BA',
            'relay': 'RY',
            'isolator': 'ISL',
            'surge_protector': 'SP',
            'terminal_block': 'TB',
            'tr_terminal': 'TR'
        }
        
        for device_type, expected_prefix in expected_prefixes.items():
            actual_prefix = test_dialog._get_default_prefix(device_type)
            if actual_prefix == expected_prefix:
                print(f"1. ✓ {device_type} -> {actual_prefix}")
            else:
                print(f"1. ✗ {device_type}: 期望{expected_prefix}, 实际{actual_prefix}")
                return False
        
        # 测试未知器件类型
        unknown_prefix = test_dialog._get_default_prefix('unknown_device')
        if unknown_prefix == 'DEV':
            print("2. ✓ 未知器件类型 -> DEV")
        else:
            print(f"2. ✗ 未知器件类型: 期望DEV, 实际{unknown_prefix}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 默认前缀映射测试失败: {e}")
        return False

def test_fixed_text_with_default_prefix():
    """测试固定文本使用默认前缀"""
    print("\n测试固定文本使用默认前缀...")
    
    try:
        from gui.simple_naming_dialog import NamingElement
        
        # 模拟添加固定文本元素的过程
        device_types = ['barrier', 'relay', 'isolator', 'surge_protector', 'terminal_block', 'tr_terminal']
        expected_prefixes = ['BA', 'RY', 'ISL', 'SP', 'TB', 'TR']
        
        class TestDialog:
            def _get_default_prefix(self, device_type: str) -> str:
                default_prefixes = {
                    'barrier': 'BA',
                    'relay': 'RY',
                    'isolator': 'ISL',
                    'surge_protector': 'SP',
                    'terminal_block': 'TB',
                    'tr_terminal': 'TR'
                }
                return default_prefixes.get(device_type, 'DEV')
        
        test_dialog = TestDialog()
        
        for device_type, expected_prefix in zip(device_types, expected_prefixes):
            # 模拟创建固定文本元素
            element = NamingElement("fixed_text", "固定文本", "用户自定义文本")
            
            # 设置默认前缀
            default_prefix = test_dialog._get_default_prefix(device_type)
            element.custom_text = default_prefix
            element.display_name = f"固定文本: {element.custom_text}"
            
            if element.custom_text == expected_prefix:
                print(f"1. ✓ {device_type}固定文本默认值: {element.custom_text}")
            else:
                print(f"1. ✗ {device_type}固定文本默认值错误: 期望{expected_prefix}, 实际{element.custom_text}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 固定文本默认前缀测试失败: {e}")
        return False

def test_default_naming_examples():
    """测试使用默认前缀的命名示例"""
    print("\n测试使用默认前缀的命名示例...")
    
    try:
        from gui.simple_naming_dialog import NamingElement
        
        # 测试各种器件类型的默认命名
        test_cases = [
            {
                'device_type': 'barrier',
                'prefix': 'BA',
                'expected': 'BA1305'
            },
            {
                'device_type': 'relay',
                'prefix': 'RY',
                'expected': 'RY1305'
            },
            {
                'device_type': 'isolator',
                'prefix': 'ISL',
                'expected': 'ISL1305'
            },
            {
                'device_type': 'surge_protector',
                'prefix': 'SP',
                'expected': 'SP1305'
            },
            {
                'device_type': 'terminal_block',
                'prefix': 'TB',
                'expected': 'TB02F03'
            },
            {
                'device_type': 'tr_terminal',
                'prefix': 'TR',
                'expected': 'TRR1S3U'
            }
        ]
        
        for case in test_cases:
            # 创建默认命名规则
            if case['device_type'] in ['barrier', 'relay', 'isolator', 'surge_protector']:
                rules = [
                    NamingElement("fixed_text", f"固定文本: {case['prefix']}", "前缀"),
                    NamingElement("rack_number", "机架编号", "机架编号"),
                    NamingElement("slot_number", "槽位编号", "槽位编号"),
                    NamingElement("channel_number", "通道号", "通道号")
                ]
                rules[0].custom_text = case['prefix']
                
                # 生成示例
                parts = []
                for rule in rules:
                    if rule.element_type == "fixed_text":
                        parts.append(rule.custom_text)
                    elif rule.element_type == "rack_number":
                        parts.append("1")
                    elif rule.element_type == "slot_number":
                        parts.append("3")
                    elif rule.element_type == "channel_number":
                        parts.append("05")
                
            elif case['device_type'] == 'terminal_block':
                rules = [
                    NamingElement("fixed_text", f"固定文本: {case['prefix']}", "前缀"),
                    NamingElement("cabinet_number", "机柜编号", "机柜编号"),
                    NamingElement("rail_number", "导轨号", "导轨号"),
                    NamingElement("device_number", "器件编号", "器件编号")
                ]
                rules[0].custom_text = case['prefix']
                
                # 生成示例
                parts = []
                for rule in rules:
                    if rule.element_type == "fixed_text":
                        parts.append(rule.custom_text)
                    elif rule.element_type == "cabinet_number":
                        parts.append("02")
                    elif rule.element_type == "rail_number":
                        parts.append("F")
                    elif rule.element_type == "device_number":
                        parts.append("03")
                
            elif case['device_type'] == 'tr_terminal':
                rules = [
                    NamingElement("fixed_text", f"固定文本: {case['prefix']}", "前缀"),
                    NamingElement("etp_name", "ETP名", "ETP名"),
                    NamingElement("etp_suffix", "ETP后缀: U", "ETP后缀")
                ]
                rules[0].custom_text = case['prefix']
                rules[2].custom_suffix = "U"
                
                # 生成示例
                parts = []
                for rule in rules:
                    if rule.element_type == "fixed_text":
                        parts.append(rule.custom_text)
                    elif rule.element_type == "etp_name":
                        parts.append("R1S3")
                    elif rule.element_type == "etp_suffix":
                        parts.append(rule.custom_suffix)
            
            result = "".join(parts)
            
            if result == case['expected']:
                print(f"1. ✓ {case['device_type']}默认命名: {result}")
            else:
                print(f"1. ✗ {case['device_type']}默认命名错误: 期望{case['expected']}, 实际{result}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 默认命名示例测试失败: {e}")
        return False

def test_custom_vs_default_prefix():
    """测试自定义前缀与默认前缀"""
    print("\n测试自定义前缀与默认前缀...")
    
    try:
        from gui.simple_naming_dialog import NamingElement
        
        # 测试默认前缀
        default_element = NamingElement("fixed_text", "固定文本", "默认前缀")
        default_element.custom_text = "BA"  # 模拟默认值
        default_element.display_name = f"固定文本: {default_element.custom_text}"
        
        # 测试自定义前缀
        custom_element = NamingElement("fixed_text", "固定文本", "自定义前缀")
        custom_element.custom_text = "SAFETY_BARRIER"  # 用户自定义
        custom_element.display_name = f"固定文本: {custom_element.custom_text}"
        
        # 验证默认前缀
        if default_element.custom_text == "BA":
            print(f"1. ✓ 默认前缀: {default_element.display_name}")
        else:
            print(f"1. ✗ 默认前缀错误: {default_element.display_name}")
            return False
        
        # 验证自定义前缀
        if custom_element.custom_text == "SAFETY_BARRIER":
            print(f"2. ✓ 自定义前缀: {custom_element.display_name}")
        else:
            print(f"2. ✗ 自定义前缀错误: {custom_element.display_name}")
            return False
        
        # 测试命名效果
        rules = [default_element, custom_element]
        
        # 生成示例
        parts = []
        for rule in rules:
            if rule.element_type == "fixed_text":
                parts.append(rule.custom_text)
        
        result = "".join(parts)
        expected = "BASAFETY_BARRIER"
        
        if result == expected:
            print(f"3. ✓ 组合前缀示例: {result}")
        else:
            print(f"3. ✗ 组合前缀示例错误: 期望{expected}, 实际{result}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 自定义vs默认前缀测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("默认前缀功能测试\n")
    print("=" * 60)
    
    tests = [
        test_default_prefix_mapping,
        test_fixed_text_with_default_prefix,
        test_default_naming_examples,
        test_custom_vs_default_prefix
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！")
        print("\n默认前缀功能特性:")
        print("1. ✓ 每种器件类型都有对应的默认前缀")
        print("2. ✓ 添加固定文本时自动使用默认前缀")
        print("3. ✓ 用户可以编辑修改默认前缀")
        print("4. ✓ 支持复杂的前缀组合")
        print("5. ✓ 保持与原有命名规则的一致性")
        print("6. ✓ 未知器件类型有合理的默认值")
        
        print("\n默认前缀映射:")
        print("- 安全栅 (barrier) -> BA")
        print("- 继电器 (relay) -> RY")
        print("- 隔离器 (isolator) -> ISL")
        print("- 防雷栅 (surge_protector) -> SP")
        print("- 端子排 (terminal_block) -> TB")
        print("- TR端子排 (tr_terminal) -> TR")
    else:
        print("✗ 部分测试失败")

if __name__ == '__main__':
    main()
