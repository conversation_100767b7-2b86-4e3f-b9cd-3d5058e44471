"""
项目生命周期管理器
提供项目生命周期、模板管理和版本控制功能
"""

import logging
import json
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

from core.logger import get_logger


class ProjectStatus(Enum):
    """项目状态"""
    CREATED = "CREATED"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    ARCHIVED = "ARCHIVED"
    CANCELLED = "CANCELLED"


class ProjectPhase(Enum):
    """项目阶段"""
    INITIALIZATION = "INITIALIZATION"
    DATA_IMPORT = "DATA_IMPORT"
    CONFIGURATION = "CONFIGURATION"
    ALLOCATION = "ALLOCATION"
    VALIDATION = "VALIDATION"
    REPORT_GENERATION = "REPORT_GENERATION"
    FINALIZATION = "FINALIZATION"


@dataclass
class ProjectMetadata:
    """项目元数据"""
    id: str
    name: str
    description: str
    created_date: str
    modified_date: str
    status: ProjectStatus
    current_phase: ProjectPhase
    version: str = "1.0.0"
    author: str = ""
    tags: List[str] = None
    custom_properties: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.custom_properties is None:
            self.custom_properties = {}


@dataclass
class ProjectTemplate:
    """项目模板"""
    id: str
    name: str
    description: str
    template_path: str
    category: str
    version: str = "1.0.0"
    required_files: List[str] = None
    default_config: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.required_files is None:
            self.required_files = []
        if self.default_config is None:
            self.default_config = {}


@dataclass
class ProjectVersion:
    """项目版本"""
    version: str
    timestamp: str
    description: str
    changes: List[str] = None
    backup_path: str = ""
    
    def __post_init__(self):
        if self.changes is None:
            self.changes = []


@dataclass
class ProjectActivity:
    """项目活动记录"""
    timestamp: str
    activity_type: str
    description: str
    user: str = ""
    details: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.details is None:
            self.details = {}


class ProjectTemplateManager:
    """项目模板管理器"""
    
    def __init__(self, template_dir: str):
        """
        初始化模板管理器
        
        Args:
            template_dir: 模板目录
        """
        self.template_dir = Path(template_dir)
        self.logger = get_logger(__name__)
        self.templates: Dict[str, ProjectTemplate] = {}
        
        # 确保模板目录存在
        self.template_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载模板
        self._load_templates()
    
    def _load_templates(self):
        """加载所有模板"""
        try:
            # 加载内置模板
            self._create_builtin_templates()
            
            # 加载用户自定义模板
            self._load_user_templates()
            
            self.logger.info(f"加载了 {len(self.templates)} 个项目模板")
            
        except Exception as e:
            self.logger.error(f"加载模板失败: {e}")
    
    def _create_builtin_templates(self):
        """创建内置模板"""
        # 标准I/O分配项目模板
        standard_template = ProjectTemplate(
            id="standard_io_allocation",
            name="标准I/O分配项目",
            description="标准的I/O点分配项目模板",
            template_path="builtin://standard",
            category="Standard",
            required_files=["pidb.xlsx", "config.json"],
            default_config={
                "allocation_settings": {
                    "enable_parttype_matching": True,
                    "enable_detailed_logging": True,
                    "max_allocation_attempts": 1000
                },
                "spare_settings": {
                    "enable_cable_spare": True,
                    "enable_etp_spare": True,
                    "default_spare_limit": 2
                }
            }
        )
        self.templates[standard_template.id] = standard_template
        
        # SIS项目模板
        sis_template = ProjectTemplate(
            id="sis_project",
            name="SIS安全系统项目",
            description="安全仪表系统(SIS)专用项目模板",
            template_path="builtin://sis",
            category="Safety",
            required_files=["pidb.xlsx", "sis_config.json", "safety_requirements.xlsx"],
            default_config={
                "allocation_settings": {
                    "enable_parttype_matching": True,
                    "enable_safety_validation": True,
                    "safety_integrity_level": "SIL3"
                },
                "spare_settings": {
                    "enable_cable_spare": True,
                    "enable_etp_spare": True,
                    "default_spare_limit": 3  # SIS系统需要更多spare
                }
            }
        )
        self.templates[sis_template.id] = sis_template
        
        # DCS项目模板
        dcs_template = ProjectTemplate(
            id="dcs_project",
            name="DCS控制系统项目",
            description="分布式控制系统(DCS)专用项目模板",
            template_path="builtin://dcs",
            category="Control",
            required_files=["pidb.xlsx", "dcs_config.json"],
            default_config={
                "allocation_settings": {
                    "enable_parttype_matching": True,
                    "enable_load_balancing": True,
                    "redundancy_required": True
                },
                "spare_settings": {
                    "enable_cable_spare": True,
                    "enable_etp_spare": True,
                    "default_spare_limit": 2
                }
            }
        )
        self.templates[dcs_template.id] = dcs_template
    
    def _load_user_templates(self):
        """加载用户自定义模板"""
        template_config_file = self.template_dir / "user_templates.json"
        
        if template_config_file.exists():
            try:
                with open(template_config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                for template_data in config.get('templates', []):
                    template = ProjectTemplate(**template_data)
                    self.templates[template.id] = template
                    
            except Exception as e:
                self.logger.error(f"加载用户模板失败: {e}")
    
    def get_template(self, template_id: str) -> Optional[ProjectTemplate]:
        """
        获取模板
        
        Args:
            template_id: 模板ID
            
        Returns:
            模板对象或None
        """
        return self.templates.get(template_id)
    
    def get_templates_by_category(self, category: str) -> List[ProjectTemplate]:
        """
        按类别获取模板
        
        Args:
            category: 类别名称
            
        Returns:
            模板列表
        """
        return [template for template in self.templates.values() 
                if template.category == category]
    
    def get_all_templates(self) -> List[ProjectTemplate]:
        """
        获取所有模板
        
        Returns:
            模板列表
        """
        return list(self.templates.values())


class ProjectVersionControl:
    """项目版本控制"""
    
    def __init__(self, project_path: str):
        """
        初始化版本控制
        
        Args:
            project_path: 项目路径
        """
        self.project_path = Path(project_path)
        self.versions_dir = self.project_path / ".versions"
        self.logger = get_logger(__name__)
        
        # 确保版本目录存在
        self.versions_dir.mkdir(parents=True, exist_ok=True)
        
        # 版本历史
        self.version_history: List[ProjectVersion] = []
        self._load_version_history()
    
    def _load_version_history(self):
        """加载版本历史"""
        version_file = self.versions_dir / "version_history.json"
        
        if version_file.exists():
            try:
                with open(version_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.version_history = [
                    ProjectVersion(**version_data) 
                    for version_data in data.get('versions', [])
                ]
                
            except Exception as e:
                self.logger.error(f"加载版本历史失败: {e}")
    
    def _save_version_history(self):
        """保存版本历史"""
        version_file = self.versions_dir / "version_history.json"
        
        try:
            data = {
                'versions': [asdict(version) for version in self.version_history]
            }
            
            with open(version_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"保存版本历史失败: {e}")
    
    def create_version(self, version: str, description: str, 
                      changes: List[str] = None) -> bool:
        """
        创建新版本
        
        Args:
            version: 版本号
            description: 版本描述
            changes: 变更列表
            
        Returns:
            创建是否成功
        """
        try:
            timestamp = datetime.now().isoformat()
            backup_path = self.versions_dir / f"backup_{version}_{timestamp.replace(':', '-')}"
            
            # 创建备份
            if self.project_path.exists():
                shutil.copytree(
                    self.project_path, 
                    backup_path,
                    ignore=shutil.ignore_patterns('.versions')
                )
            
            # 创建版本记录
            project_version = ProjectVersion(
                version=version,
                timestamp=timestamp,
                description=description,
                changes=changes or [],
                backup_path=str(backup_path)
            )
            
            self.version_history.append(project_version)
            self._save_version_history()
            
            self.logger.info(f"创建项目版本: {version}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建版本失败: {e}")
            return False
    
    def restore_version(self, version: str) -> bool:
        """
        恢复到指定版本
        
        Args:
            version: 版本号
            
        Returns:
            恢复是否成功
        """
        try:
            # 查找版本
            target_version = None
            for v in self.version_history:
                if v.version == version:
                    target_version = v
                    break
            
            if not target_version:
                self.logger.error(f"未找到版本: {version}")
                return False
            
            backup_path = Path(target_version.backup_path)
            if not backup_path.exists():
                self.logger.error(f"备份路径不存在: {backup_path}")
                return False
            
            # 创建当前状态的备份
            current_backup = self.versions_dir / f"before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            if self.project_path.exists():
                shutil.copytree(
                    self.project_path,
                    current_backup,
                    ignore=shutil.ignore_patterns('.versions')
                )
            
            # 恢复版本
            if self.project_path.exists():
                shutil.rmtree(self.project_path)
            
            shutil.copytree(backup_path, self.project_path)
            
            # 恢复版本目录
            versions_backup = current_backup / ".versions"
            if versions_backup.exists():
                shutil.copytree(versions_backup, self.versions_dir, dirs_exist_ok=True)
            
            self.logger.info(f"恢复到版本: {version}")
            return True
            
        except Exception as e:
            self.logger.error(f"恢复版本失败: {e}")
            return False
    
    def get_version_history(self) -> List[ProjectVersion]:
        """
        获取版本历史
        
        Returns:
            版本历史列表
        """
        return self.version_history.copy()
    
    def get_latest_version(self) -> Optional[ProjectVersion]:
        """
        获取最新版本
        
        Returns:
            最新版本或None
        """
        if self.version_history:
            return self.version_history[-1]
        return None


class ProjectLifecycleManager:
    """项目生命周期管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化项目生命周期管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = get_logger(__name__)
        
        # 初始化组件
        template_dir = config.get('project_settings', {}).get('template_dir', 'resources/templates')
        self.template_manager = ProjectTemplateManager(template_dir)
        
        # 项目活动历史
        self.activity_history: List[ProjectActivity] = []
        
        self.logger.info("项目生命周期管理器初始化完成")
    
    def create_project(self, project_path: str, template_id: str, 
                      project_name: str, description: str = "") -> bool:
        """
        创建新项目
        
        Args:
            project_path: 项目路径
            template_id: 模板ID
            project_name: 项目名称
            description: 项目描述
            
        Returns:
            创建是否成功
        """
        try:
            self.logger.info(f"创建项目: {project_name}")
            
            # 获取模板
            template = self.template_manager.get_template(template_id)
            if not template:
                self.logger.error(f"未找到模板: {template_id}")
                return False
            
            # 创建项目目录
            project_dir = Path(project_path)
            project_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建项目元数据
            metadata = ProjectMetadata(
                id=f"project_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                name=project_name,
                description=description,
                created_date=datetime.now().isoformat(),
                modified_date=datetime.now().isoformat(),
                status=ProjectStatus.CREATED,
                current_phase=ProjectPhase.INITIALIZATION,
                author="System"
            )
            
            # 保存项目元数据
            self._save_project_metadata(project_dir, metadata)
            
            # 应用模板配置
            self._apply_template(project_dir, template)
            
            # 初始化版本控制
            version_control = ProjectVersionControl(str(project_dir))
            version_control.create_version("1.0.0", "项目初始创建")
            
            # 记录活动
            self._record_activity("PROJECT_CREATED", f"创建项目: {project_name}")
            
            self.logger.info(f"项目创建成功: {project_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建项目失败: {e}")
            return False
    
    def _save_project_metadata(self, project_dir: Path, metadata: ProjectMetadata):
        """
        保存项目元数据

        Args:
            project_dir: 项目目录
            metadata: 项目元数据
        """
        metadata_file = project_dir / "project_metadata.json"

        # 转换枚举为字符串
        metadata_dict = asdict(metadata)
        metadata_dict['status'] = metadata.status.value
        metadata_dict['current_phase'] = metadata.current_phase.value

        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata_dict, f, ensure_ascii=False, indent=2)
    
    def _apply_template(self, project_dir: Path, template: ProjectTemplate):
        """
        应用项目模板
        
        Args:
            project_dir: 项目目录
            template: 项目模板
        """
        # 创建默认配置文件
        if template.default_config:
            config_file = project_dir / "config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(template.default_config, f, ensure_ascii=False, indent=2)
        
        # 创建必需文件的占位符
        for required_file in template.required_files:
            file_path = project_dir / required_file
            if not file_path.exists():
                file_path.parent.mkdir(parents=True, exist_ok=True)
                file_path.touch()
    
    def load_project_metadata(self, project_path: str) -> Optional[ProjectMetadata]:
        """
        加载项目元数据

        Args:
            project_path: 项目路径

        Returns:
            项目元数据或None
        """
        try:
            metadata_file = Path(project_path) / "project_metadata.json"

            if not metadata_file.exists():
                return None

            with open(metadata_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 转换字符串为枚举
            if 'status' in data:
                data['status'] = ProjectStatus(data['status'])
            if 'current_phase' in data:
                data['current_phase'] = ProjectPhase(data['current_phase'])

            return ProjectMetadata(**data)

        except Exception as e:
            self.logger.error(f"加载项目元数据失败: {e}")
            return None
    
    def update_project_phase(self, project_path: str, new_phase: ProjectPhase) -> bool:
        """
        更新项目阶段
        
        Args:
            project_path: 项目路径
            new_phase: 新阶段
            
        Returns:
            更新是否成功
        """
        try:
            metadata = self.load_project_metadata(project_path)
            if not metadata:
                return False
            
            old_phase = metadata.current_phase
            metadata.current_phase = new_phase
            metadata.modified_date = datetime.now().isoformat()
            
            self._save_project_metadata(Path(project_path), metadata)
            
            # 记录活动
            self._record_activity(
                "PHASE_CHANGED", 
                f"项目阶段从 {old_phase.value} 变更为 {new_phase.value}"
            )
            
            self.logger.info(f"项目阶段更新: {old_phase.value} -> {new_phase.value}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新项目阶段失败: {e}")
            return False
    
    def _record_activity(self, activity_type: str, description: str, 
                        user: str = "System", details: Dict[str, Any] = None):
        """
        记录项目活动
        
        Args:
            activity_type: 活动类型
            description: 活动描述
            user: 用户
            details: 详细信息
        """
        activity = ProjectActivity(
            timestamp=datetime.now().isoformat(),
            activity_type=activity_type,
            description=description,
            user=user,
            details=details or {}
        )
        
        self.activity_history.append(activity)
    
    def get_activity_history(self) -> List[ProjectActivity]:
        """
        获取活动历史
        
        Returns:
            活动历史列表
        """
        return self.activity_history.copy()
    
    def get_available_templates(self) -> List[ProjectTemplate]:
        """
        获取可用模板
        
        Returns:
            模板列表
        """
        return self.template_manager.get_all_templates()
