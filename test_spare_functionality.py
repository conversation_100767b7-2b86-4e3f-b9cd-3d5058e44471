"""
Spare点功能测试
测试spare点的生成、分配和报表处理功能
"""

import unittest
from unittest.mock import Mock, patch
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.spare_manager import (
    SparePointManager, SpareConfiguration, SpareStrategy,
    CableSpareStrategy, EtpSpareStrategy, SpareGenerationContext
)
from core.data_models import IOPoint, SignalType, Cable
from core.io_report_builder import IOReportBuilder, ReportTemplate


class TestSpareConfiguration(unittest.TestCase):
    """测试Spare配置"""
    
    def test_default_configuration(self):
        """测试默认配置"""
        config = SpareConfiguration()
        
        self.assertEqual(config.default_spare_limit, 2)
        self.assertTrue(config.enable_cable_spare)
        self.assertTrue(config.enable_etp_spare)
        self.assertEqual(config.spare_naming_prefix, "SPARE_")
        self.assertEqual(config.spare_description, "-")
        
        # 测试ETP配置
        self.assertEqual(config.get_spare_limit("CPM16-AI3700"), 2)
        self.assertEqual(config.get_spare_limit("Unknown_ETP"), 2)
    
    def test_custom_configuration(self):
        """测试自定义配置"""
        custom_limits = {"CPM16-AI3700": 3, "CPM16-DI3700": 1}
        config = SpareConfiguration(
            default_spare_limit=4,
            etp_spare_limits=custom_limits,
            spare_naming_prefix="SP_"
        )
        
        self.assertEqual(config.default_spare_limit, 4)
        self.assertEqual(config.get_spare_limit("CPM16-AI3700"), 3)
        self.assertEqual(config.get_spare_limit("CPM16-DI3700"), 1)
        self.assertEqual(config.get_spare_limit("Unknown_ETP"), 4)
        self.assertEqual(config.spare_naming_prefix, "SP_")


class TestCableSpareStrategy(unittest.TestCase):
    """测试电缆Spare策略"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = SpareConfiguration()
        self.strategy = CableSpareStrategy(self.config)
    
    def test_create_spare_point(self):
        """测试创建spare点"""
        spare_point = self.strategy._create_spare_point(
            cable_name="TEST_CABLE",
            pair_number=5,
            typical_circuit="DI NIS N",
            signal_type=SignalType.DI,
            spare_index=1
        )
        
        self.assertEqual(spare_point.tag, "SPARE_1")
        self.assertEqual(spare_point.description, "-")
        self.assertEqual(spare_point.cable_name, "TEST_CABLE")
        self.assertEqual(spare_point.pair_number, 5)
        self.assertEqual(spare_point.signal_type, SignalType.DI)
        self.assertTrue(spare_point.is_spare)
    
    def test_count_typical_circuits(self):
        """测试典型回路计数"""
        io_points = [
            IOPoint(tag="TAG1", wiring_typical="DI NIS N"),
            IOPoint(tag="TAG2", wiring_typical="DI NIS N"),
            IOPoint(tag="TAG3", wiring_typical="AI IS BABP"),
            IOPoint(tag="TAG4", wiring_typical="DI NIS N")
        ]
        
        counts = self.strategy.count_typical_circuits(io_points)
        
        self.assertEqual(counts["DI NIS N"], 3)
        self.assertEqual(counts["AI IS BABP"], 1)
    
    def test_generate_spare_points_for_cable(self):
        """测试为电缆生成spare点"""
        # 创建测试电缆数据
        io_points = [
            IOPoint(tag="TAG1", pair_number=1, wiring_typical="DI NIS N"),
            IOPoint(tag="TAG2", pair_number=3, wiring_typical="DI NIS N")
        ]
        
        cables = [{
            'name': 'TEST_CABLE',
            'pair_size': 5,
            'io_points': io_points
        }]
        
        context = SpareGenerationContext(cables=cables, config=self.config)
        result = self.strategy.generate_spare_points(context)
        
        self.assertTrue(result.success)
        self.assertEqual(len(result.spare_points), 3)  # 应该为pair 2, 4, 5生成spare点
        
        # 验证spare点属性
        for spare_point in result.spare_points:
            self.assertTrue(spare_point.is_spare)
            self.assertEqual(spare_point.cable_name, 'TEST_CABLE')
            self.assertTrue(spare_point.tag.startswith('SPARE_'))


class TestEtpSpareStrategy(unittest.TestCase):
    """测试ETP Spare策略"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = SpareConfiguration()
        self.strategy = EtpSpareStrategy(self.config)
    
    def test_infer_signal_type_from_etp(self):
        """测试从ETP类型推断信号类型"""
        self.assertEqual(self.strategy._infer_signal_type_from_etp("CPM16-AI3700"), SignalType.AI)
        self.assertEqual(self.strategy._infer_signal_type_from_etp("CPM16-AO3700"), SignalType.AO)
        self.assertEqual(self.strategy._infer_signal_type_from_etp("CPM16-DI3700"), SignalType.DI)
        self.assertEqual(self.strategy._infer_signal_type_from_etp("CPM16-DO3700"), SignalType.DO)
        self.assertEqual(self.strategy._infer_signal_type_from_etp("Unknown"), SignalType.DI)


class TestSparePointManager(unittest.TestCase):
    """测试Spare点管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = {
            'spare_settings': {
                'default_spare_limit': 2,
                'enable_cable_spare': True,
                'enable_etp_spare': True,
                'spare_naming_prefix': 'SPARE_',
                'spare_description': '-'
            }
        }
        self.manager = SparePointManager(self.config)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsNotNone(self.manager.spare_config)
        self.assertIsNotNone(self.manager.cable_strategy)
        self.assertIsNotNone(self.manager.etp_strategy)
        
        self.assertEqual(self.manager.spare_config.default_spare_limit, 2)
        self.assertTrue(self.manager.spare_config.enable_cable_spare)
    
    def test_generate_spare_points_cable_strategy(self):
        """测试电缆策略生成spare点"""
        # 创建测试数据
        io_points = [
            IOPoint(tag="TAG1", pair_number=1, wiring_typical="DI NIS N"),
            IOPoint(tag="TAG2", pair_number=2, wiring_typical="DI NIS N")
        ]
        
        cables = [{
            'name': 'TEST_CABLE',
            'pair_size': 4,
            'io_points': io_points
        }]
        
        result = self.manager.generate_spare_points(
            cables=cables,
            terminal_blocks=[],
            wiring_typicals={},
            cabinets=[],
            strategy=SpareStrategy.CABLE_FORM
        )
        
        self.assertTrue(result.success)
        self.assertEqual(len(result.spare_points), 2)  # 应该为pair 3, 4生成spare点
    
    def test_update_configuration(self):
        """测试更新配置"""
        new_config = {
            'spare_settings': {
                'default_spare_limit': 3,
                'enable_cable_spare': False,
                'spare_naming_prefix': 'SP_'
            }
        }
        
        self.manager.update_configuration(new_config)
        
        self.assertEqual(self.manager.spare_config.default_spare_limit, 3)
        self.assertFalse(self.manager.spare_config.enable_cable_spare)
        self.assertEqual(self.manager.spare_config.spare_naming_prefix, 'SP_')


class TestIOReportBuilder(unittest.TestCase):
    """测试IO报表构建器"""
    
    def setUp(self):
        """设置测试环境"""
        self.template = ReportTemplate("test_template.xlsx", "测试模板")
        self.builder = IOReportBuilder(self.template)
    
    def test_format_tag_for_display(self):
        """测试Tag格式化显示"""
        # 测试常规点
        regular_point = IOPoint(tag="TAG001", is_spare=False)
        self.assertEqual(self.builder._format_tag_for_display(regular_point), "TAG001")
        
        # 测试spare点
        spare_point = IOPoint(tag="SPARE_1", is_spare=True)
        self.assertEqual(self.builder._format_tag_for_display(spare_point), "SPARE")
    
    def test_format_description_for_display(self):
        """测试描述格式化显示"""
        # 测试常规点
        regular_point = IOPoint(description="Temperature sensor", is_spare=False)
        self.assertEqual(self.builder._format_description_for_display(regular_point), "Temperature sensor")
        
        # 测试spare点
        spare_point = IOPoint(description="Spare point", is_spare=True)
        self.assertEqual(self.builder._format_description_for_display(spare_point), "-")
    
    def test_process_io_points(self):
        """测试IO点处理"""
        io_points = [
            IOPoint(tag="TAG001", description="Sensor", signal_type=SignalType.DI, is_spare=False),
            IOPoint(tag="SPARE_1", description="Spare", signal_type=SignalType.DI, is_spare=True)
        ]
        
        processed = self.builder._process_io_points(io_points)
        
        self.assertEqual(len(processed), 2)
        
        # 验证常规点
        regular = processed[0]
        self.assertEqual(regular['tag'], "TAG001")
        self.assertEqual(regular['description'], "Sensor")
        self.assertFalse(regular['is_spare'])
        
        # 验证spare点
        spare = processed[1]
        self.assertEqual(spare['tag'], "SPARE")
        self.assertEqual(spare['description'], "-")
        self.assertTrue(spare['is_spare'])


class TestCableDataModel(unittest.TestCase):
    """测试Cable数据模型扩展"""
    
    def test_get_used_pairs(self):
        """测试获取已使用线对"""
        io_points = [
            IOPoint(pair_number=1),
            IOPoint(pair_number=3),
            IOPoint(pair_number=1),  # 重复
            IOPoint(pair_number=5)
        ]
        
        cable = Cable(name="TEST", pair_size=8, io_points=io_points)
        used_pairs = cable.get_used_pairs()
        
        self.assertEqual(sorted(used_pairs), [1, 3, 5])
    
    def test_get_available_pairs(self):
        """测试获取可用线对"""
        io_points = [
            IOPoint(pair_number=1),
            IOPoint(pair_number=3)
        ]
        
        cable = Cable(name="TEST", pair_size=5, io_points=io_points)
        available_pairs = cable.get_available_pairs()
        
        self.assertEqual(sorted(available_pairs), [2, 4, 5])


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
