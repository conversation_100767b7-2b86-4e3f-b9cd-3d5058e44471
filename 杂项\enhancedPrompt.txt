我需要开发一个基于Python和PySide6的自动化I/O点领域分配软件。该软件需要处理工业控制系统中的I/O点分配到机柜导轨和机架的自动化流程。

## 项目结构要求
1. 使用PySide6创建统一的GUI界面
2. 将现有的xmleditor文件夹中的XML查看和编辑功能集成到主程序GUI中
3. 创建合理的项目文件结构，将非代码文件夹统一管理
4. 忽略"杂项"文件夹

## 输入数据格式
### PIDB数据 (Excel格式)
- **chassis工作表**: 包含机架信息及其所在机柜
- **cabinet工作表**: 包含机柜名称、编号、区域和对应的典型机柜模板
- 参考文件: `04B_PIDB文件夹/PIDB.xlsx`

### IODB数据 (Excel格式)
- 包含IO点(tag)的详细信息：电缆名称、电缆信息、本安/非本安属性、信号类型、系统、区域、典型回路等
- 参考文件: `04A_IODB文件夹/IODB.xlsx`

### 典型机柜 (XML格式)
- 参考文件: `01B_CabinetProfile文件夹`中的PPG SYS和PPG BAR
- 包含导轨(Rail)和机架(Rack)两种类型组件

### 典型回路 (XML格式)
- 参考文件: `01C_WiringTypical文件夹`
- 定义回路中各组件的连接关系和属性

## 核心功能实现

### 第一阶段：IODB数据验证
实现以下验证规则并提供详细的日志和错误报告：

1. **Tag唯一性验证**: 确保所有tag名称唯一
2. **Cable配对验证**: 
   - Cable名称不可重复pair number
   - Pair number不能超过pair size限制
3. **Cable属性一致性验证**: 同一cable的所有记录必须在以下属性保持一致：
   - IS (本安属性)
   - Size (等同于Pair Size)
   - System (等同于所属系统)
   - Location (区域)
   - SignalType (信号类型)
   - CableType (电缆类型)

### 第二阶段：自动分配算法
按照cable name和pair number升序进行分配，实现以下逻辑：

#### 分配约束条件
1. **机柜约束**: 一个cable中的所有IO点必须分配在同一机柜中
2. **导轨匹配规则**:
   - IOType匹配: 导轨IOType必须与cable的SignalType匹配
     - Mixed: 支持AI/AO/DI/DO
     - Analog: 支持AI/AO  
     - Digital: 支持DI/DO
     - 具体类型(如AI): 仅支持对应的信号类型
   - Intrinsic匹配: 导轨的IS属性必须与cable匹配
     - Mixed或"IS,NIS": 支持IS和NIS
     - IS: 仅支持本安
     - NIS: 仅支持非本安
3. **PartType匹配** (可选，通过GUI勾选框控制):
   - 典型回路组件的HardwareType必须与导轨的PartTypeXX匹配
   - 典型回路组件的PartNumber必须与导轨的PartNumXX匹配

#### 机柜类型处理
- **系统柜(SYS)**: 仅包含机架，用于放置卡件
- **辅助接线柜(MAR)**: 仅包含导轨，用于放置端子和隔离栅
- **混合柜**: 同时包含导轨和机架

#### 组件分配顺序
1. **导轨组件分配顺序**:
   - 首先: IsLandedComponent=true的组件
   - 中间: 其他组件(安全栅、继电器、隔离器等)
   - 倒数第二: ETP (Engineering Terminal Panel)
   - 最后: 卡件分配到机架

2. **机架类型和槽位分配**:
   - MAIN机架(MainProcessorChassis): 3-7槽可用
   - EXP机架(Chassis): 1-8槽可用  
   - RXM机架(RXMChassis): 2-7槽可用
   - 卡件按SlotsApplicable属性分配(如"1L,2L,3L,4L,5L,6L,7L,8L")，如1L代表1槽
#### 失败处理和回滚机制
- 如果cable中任何tag分配失败，整个cable进行回滚重新分配
- 对于混合柜：如果导轨组件成功但卡件分配失败，回滚整个cable
- 分配失败时尝试下一个符合条件的机柜
- 所有机柜都失败时报告ERROR级别错误

#### 通道连接关系
- ETP和卡件通过Connectors属性定义通道对应关系
- 例如: J2U_ConnectedChannels="1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16"表示1-16通道

## 技术要求
1. **日志系统**: 实现分级日志(WARNING/ERROR)，包含详细的分配决策信息
2. **错误报告**: 错误信息必须包含cable名称、tag名称、机柜名称等关键信息
3. **GUI界面**: 
   - 集成XML编辑器功能
   - 提供PartType匹配的勾选框控制
   - 显示分配进度和结果
4. **数据处理**: 正确解析Excel和XML文件格式

## 需要确认的技术细节
在开始实现前，请仔细阅读提供的示例XML文件，并确认：
1. XML文件的具体属性结构和读取规则
2. 各种组件类型的识别方法
3. 长度/宽度属性的使用方式
4. 任何其他不明确的技术实现细节

如有任何不清楚的地方请及时询问。