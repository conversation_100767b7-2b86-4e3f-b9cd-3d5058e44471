"""
Spare点功能集成测试
测试spare点功能与主分配系统的集成
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.allocator import IOAllocator
from core.data_models import IOPoint, SignalType
from core.spare_manager import SpareStrategy
from utils.config_manager_simple import ConfigManager


def test_spare_integration():
    """测试spare点功能集成"""
    print("=" * 60)
    print("Spare点功能集成测试")
    print("=" * 60)
    
    try:
        # 1. 加载配置
        print("\n1. 加载系统配置...")
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # 验证spare配置
        spare_settings = config.get('spare_settings', {})
        print(f"   默认spare下限: {spare_settings.get('default_spare_limit', 'N/A')}")
        print(f"   启用电缆spare: {spare_settings.get('enable_cable_spare', 'N/A')}")
        print(f"   启用ETP spare: {spare_settings.get('enable_etp_spare', 'N/A')}")
        print(f"   Spare命名前缀: {spare_settings.get('spare_naming_prefix', 'N/A')}")
        
        # 2. 创建分配器
        print("\n2. 初始化IO分配器...")
        allocator = IOAllocator(config)
        print(f"   分配器初始化完成")
        print(f"   Spare管理器已集成: {hasattr(allocator, 'spare_manager')}")
        
        # 3. 创建测试数据
        print("\n3. 创建测试数据...")
        
        # 创建测试IO点
        test_io_points = [
            IOPoint(
                tag="FT_001",
                signal_type=SignalType.AI,
                description="Flow transmitter",
                cable_name="CABLE_001",
                pair_number=1,
                wiring_typical="AI IS BABP"
            ),
            IOPoint(
                tag="PT_001", 
                signal_type=SignalType.AI,
                description="Pressure transmitter",
                cable_name="CABLE_001",
                pair_number=2,
                wiring_typical="AI IS BABP"
            ),
            IOPoint(
                tag="XV_001",
                signal_type=SignalType.DO,
                description="Control valve",
                cable_name="CABLE_002",
                pair_number=1,
                wiring_typical="DO NIS WET SIL3RY"
            )
        ]
        
        # 创建测试电缆
        test_cables = [
            {
                'name': 'CABLE_001',
                'pair_size': 8,  # 8对线，已用2对，应该生成6个spare点
                'io_points': test_io_points[:2],
                'cable_type': 'Instrumentation',
                'location': 'Field'
            },
            {
                'name': 'CABLE_002', 
                'pair_size': 4,  # 4对线，已用1对，应该生成3个spare点
                'io_points': test_io_points[2:],
                'cable_type': 'Control',
                'location': 'Field'
            }
        ]
        
        # 创建测试机柜
        test_cabinets = [
            {
                'name': '1103-SIS-SYS-001',
                'rails': [
                    {'name': 'Rail1', 'width': 500.0, 'height': 100.0}
                ],
                'location': 'Control Room'
            }
        ]
        
        # 创建测试典型回路
        test_wiring_typicals = {
            'AI IS BABP': {
                'signal_type': 'AI',
                'components': ['Safety Barrier', 'Terminal Block']
            },
            'DO NIS WET SIL3RY': {
                'signal_type': 'DO', 
                'components': ['Relay', 'Terminal Block']
            }
        }
        
        print(f"   创建了 {len(test_io_points)} 个测试IO点")
        print(f"   创建了 {len(test_cables)} 条测试电缆")
        print(f"   创建了 {len(test_cabinets)} 个测试机柜")
        
        # 4. 执行分配
        print("\n4. 执行IO点分配...")
        result = allocator.allocate_io_points(
            cables=test_cables,
            cabinets=test_cabinets,
            wiring_typicals=test_wiring_typicals
        )
        
        print(f"   分配结果: {'成功' if result.success else '失败'}")
        print(f"   成功分配点数: {len(result.allocated_points)}")
        print(f"   失败分配点数: {len(result.failed_points)}")
        print(f"   警告数: {len(result.warnings)}")
        print(f"   错误数: {len(result.errors)}")
        
        # 5. 分析spare点
        print("\n5. 分析Spare点生成结果...")
        
        spare_points = [point for point in result.allocated_points 
                       if hasattr(point, 'is_spare') and point.is_spare]
        regular_points = [point for point in result.allocated_points 
                         if not (hasattr(point, 'is_spare') and point.is_spare)]
        
        print(f"   常规IO点: {len(regular_points)}")
        print(f"   Spare点: {len(spare_points)}")
        
        if spare_points:
            print("\n   Spare点详情:")
            for i, spare in enumerate(spare_points[:5], 1):  # 只显示前5个
                print(f"     {i}. Tag: {spare.tag}, 电缆: {spare.cable_name}, "
                      f"线对: {spare.pair_number}, 信号类型: {spare.signal_type}")
            
            if len(spare_points) > 5:
                print(f"     ... 还有 {len(spare_points) - 5} 个spare点")
        
        # 6. 验证spare点属性
        print("\n6. 验证Spare点属性...")
        
        spare_validation_passed = True
        for spare in spare_points:
            # 检查必要属性
            if not spare.tag.startswith(spare_settings.get('spare_naming_prefix', 'SPARE_')):
                print(f"   ❌ Spare点 {spare.tag} 命名格式不正确")
                spare_validation_passed = False
            
            if spare.description != spare_settings.get('spare_description', '-'):
                print(f"   ❌ Spare点 {spare.tag} 描述不正确: {spare.description}")
                spare_validation_passed = False
            
            if not hasattr(spare, 'is_spare') or not spare.is_spare:
                print(f"   ❌ Spare点 {spare.tag} is_spare标志不正确")
                spare_validation_passed = False
        
        if spare_validation_passed:
            print("   ✅ 所有spare点属性验证通过")
        
        # 7. 测试报表处理
        print("\n7. 测试报表处理...")
        
        from core.io_report_builder import IOReportBuilder, ReportTemplate
        
        template = ReportTemplate("test_template.xlsx", "测试模板")
        builder = IOReportBuilder(template)
        
        # 处理IO点数据
        processed_points = builder._process_io_points(result.allocated_points)
        
        spare_display_count = sum(1 for point in processed_points 
                                 if point['tag'] == 'SPARE')
        
        print(f"   处理了 {len(processed_points)} 个IO点")
        print(f"   报表中显示为'SPARE'的点数: {spare_display_count}")
        
        # 8. 总结
        print("\n" + "=" * 60)
        print("集成测试总结")
        print("=" * 60)
        
        print(f"✅ 配置加载: 成功")
        print(f"✅ 分配器初始化: 成功") 
        print(f"✅ IO点分配: {'成功' if result.success else '失败'}")
        print(f"✅ Spare点生成: {'成功' if len(spare_points) > 0 else '失败'}")
        print(f"✅ Spare点验证: {'成功' if spare_validation_passed else '失败'}")
        print(f"✅ 报表处理: 成功")
        
        expected_spare_count = (8 - 2) + (4 - 1)  # CABLE_001: 6个, CABLE_002: 3个
        actual_spare_count = len(spare_points)
        
        print(f"\n预期spare点数: {expected_spare_count}")
        print(f"实际spare点数: {actual_spare_count}")
        
        if actual_spare_count == expected_spare_count:
            print("🎉 Spare点数量完全正确！")
        else:
            print("⚠️  Spare点数量与预期不符")
        
        # 显示摘要信息
        if 'spare_points_generated' in result.summary:
            print(f"\n分配摘要中的spare点数: {result.summary['spare_points_generated']}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = test_spare_integration()
    if success:
        print("\n🎉 Spare点功能集成测试完成！")
    else:
        print("\n❌ 集成测试失败！")
        sys.exit(1)
