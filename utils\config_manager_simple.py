"""
配置管理器 - 简化版本
负责加载、保存和管理应用程序配置
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
import logging


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，默认为resources/config.json
        """
        self.project_root = Path(__file__).parent.parent
        self.config_file = config_file or self.project_root / "resources" / "config.json"
        self._config = {}
        self._default_config = self._get_default_config()
        
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "application": {
                "name": "EWReborn - I/O点自动分配系统",
                "version": "1.0.0",
                "window": {
                    "width": 1400,
                    "height": 900,
                    "min_width": 1000,
                    "min_height": 600
                }
            },
            "data_paths": {
                "cabinet_profiles": "data/cabinet_profiles",
                "wiring_typical": "data/wiring_typical",
                "iodb": "data/iodb",
                "pidb": "data/pidb"
            },
            "allocation_settings": {
                "enable_parttype_matching": True,
                "enable_detailed_logging": True,
                "max_allocation_attempts": 1000,
                "allocation_order": "cable_name_pair_asc"
            },
            "validation_rules": {
                "tag_uniqueness": True,
                "cable_pair_validation": True,
                "cable_attribute_consistency": True
            },
            "logging": {
                "level": "INFO",
                "file_path": "logs/ewreborn.log",
                "max_file_size": "10MB",
                "backup_count": 5
            },
            "gui": {
                "theme": "default",
                "show_progress_details": True,
                "auto_save_results": True
            },
            "spare_settings": {
                "default_spare_limit": 2,
                "etp_spare_limits": {
                    "CPM16-AI3700": 2,
                    "CPM16-AO3700": 2,
                    "CPM16-DI3700": 2,
                    "CPM16-DO3700": 2
                },
                "enable_cable_spare": True,
                "enable_etp_spare": True,
                "spare_naming_prefix": "SPARE_",
                "spare_description": "-"
            }
        }
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        # 从默认配置开始
        self._config = self._default_config.copy()
        
        # 尝试加载系统配置文件
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    system_config = json.load(f)
                self._merge_config(self._config, system_config)
            except Exception as e:
                print(f"加载系统配置文件失败: {e}")
        
        # 转换相对路径为绝对路径
        self._resolve_paths()
        
        return self._config
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def _merge_config(self, base: Dict[str, Any], override: Dict[str, Any]) -> None:
        """
        合并配置字典
        
        Args:
            base: 基础配置
            override: 覆盖配置
        """
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
    
    def _resolve_paths(self) -> None:
        """将相对路径转换为绝对路径"""
        data_paths = self._config.get('data_paths', {})
        for key, path in data_paths.items():
            if not os.path.isabs(path):
                data_paths[key] = str(self.project_root / path)
        
        # 处理日志文件路径
        log_path = self._config.get('logging', {}).get('file_path', '')
        if log_path and not os.path.isabs(log_path):
            self._config['logging']['file_path'] = str(self.project_root / log_path)
    
    @property
    def config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return self._config.copy()
