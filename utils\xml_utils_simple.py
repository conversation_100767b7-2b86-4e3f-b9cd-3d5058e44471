"""
XML处理工具 - 简化版本
扩展现有的XML操作功能
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import xml.etree.ElementTree as ET

from core.logger import get_logger


class XMLProcessor:
    """XML处理器"""
    
    def __init__(self):
        """初始化XML处理器"""
        self.logger = get_logger(__name__)
    
    def load_xml_file(self, file_path: str) -> Optional[ET.ElementTree]:
        """
        加载XML文件
        
        Args:
            file_path: XML文件路径
            
        Returns:
            XML树对象或None
        """
        try:
            self.logger.info(f"加载XML文件: {file_path}")
            tree = ET.parse(file_path)
            return tree
        except Exception as e:
            self.logger.error(f"加载XML文件失败: {e}")
            return None
    
    def load_cabinet_profile(self, file_path: str) -> Dict[str, Any]:
        """
        加载机柜配置文件
        
        Args:
            file_path: 机柜配置文件路径
            
        Returns:
            机柜配置数据
        """
        tree = self.load_xml_file(file_path)
        if tree is None:
            return {}
        
        try:
            root = tree.getroot()
            cabinet_data = {
                'name': Path(file_path).stem,
                'rails': [],
                'racks': [],
                'properties': {},
                'components': []
            }
            
            # 解析根元素属性
            cabinet_data['properties'].update(root.attrib)
            
            # 简化的组件解析
            for component_elem in root.findall('.//ProfileComponent'):
                component_data = {
                    'name': component_elem.get('Name', ''),
                    'type': component_elem.get('Type', ''),
                    'properties': dict(component_elem.attrib)
                }
                cabinet_data['components'].append(component_data)
            
            self.logger.info(f"机柜配置解析完成: {len(cabinet_data['components'])}个组件")
            return cabinet_data
            
        except Exception as e:
            self.logger.error(f"解析机柜配置文件失败: {e}")
            return {}
    
    def load_wiring_typical(self, file_path: str) -> Dict[str, Any]:
        """
        加载典型回路文件
        
        Args:
            file_path: 典型回路文件路径
            
        Returns:
            典型回路数据
        """
        tree = self.load_xml_file(file_path)
        if tree is None:
            return {}
        
        try:
            root = tree.getroot()
            typical_data = {
                'name': Path(file_path).stem,
                'signal_type': 'AI',  # 从文件名推断
                'is_intrinsic': 'IS' in Path(file_path).stem.upper(),
                'components': [],
                'connections': [],
                'properties': {}
            }
            
            # 解析根元素属性
            typical_data['properties'].update(root.attrib)
            
            # 从文件名解析信号类型
            filename = Path(file_path).stem.upper()
            if 'AI' in filename:
                typical_data['signal_type'] = 'AI'
            elif 'AO' in filename:
                typical_data['signal_type'] = 'AO'
            elif 'DI' in filename:
                typical_data['signal_type'] = 'DI'
            elif 'DO' in filename:
                typical_data['signal_type'] = 'DO'
            
            # 简化的组件解析
            for component_elem in root.findall('.//ProfileComponent'):
                component_data = {
                    'name': component_elem.get('Name', ''),
                    'type': component_elem.get('Type', ''),
                    'properties': dict(component_elem.attrib)
                }
                typical_data['components'].append(component_data)
            
            self.logger.info(f"典型回路解析完成: {len(typical_data['components'])}个组件")
            return typical_data
            
        except Exception as e:
            self.logger.error(f"解析典型回路文件失败: {e}")
            return {}
    
    def save_xml_file(self, tree: ET.ElementTree, file_path: str) -> bool:
        """
        保存XML文件
        
        Args:
            tree: XML树对象
            file_path: 输出文件路径
            
        Returns:
            是否保存成功
        """
        try:
            self.logger.info(f"保存XML文件: {file_path}")
            
            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            tree.write(file_path, encoding='utf-8', xml_declaration=True)
            return True
        except Exception as e:
            self.logger.error(f"保存XML文件失败: {e}")
            return False
