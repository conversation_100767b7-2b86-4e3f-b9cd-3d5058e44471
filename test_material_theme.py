#!/usr/bin/env python3
"""
测试Material主题集成
验证qt-material是否正确集成到EWReborn项目中
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_material_theme_import():
    """测试Material主题模块导入"""
    try:
        from gui.material_theme import MaterialThemeManager, init_theme_manager
        print("✅ Material主题模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ Material主题模块导入失败: {e}")
        return False

def test_qt_material_availability():
    """测试qt-material库可用性"""
    try:
        import qt_material
        import qtawesome
        print("✅ qt-material和qtawesome库可用")
        
        # 测试获取主题列表
        themes = qt_material.list_themes()
        print(f"✅ 发现 {len(themes)} 个可用主题")
        print(f"   主题列表: {themes[:5]}...")  # 显示前5个主题
        
        return True
    except ImportError as e:
        print(f"❌ qt-material库不可用: {e}")
        print("   请运行: pip install qt-material qtawesome")
        return False

def test_theme_manager_creation():
    """测试主题管理器创建"""
    try:
        from gui.material_theme import MaterialThemeManager
        
        # 创建测试配置
        test_config = {
            'gui': {
                'material_theme': 'dark_teal.xml'
            }
        }
        
        # 创建主题管理器
        theme_manager = MaterialThemeManager(test_config)
        print("✅ 主题管理器创建成功")
        
        # 测试获取可用主题
        themes = theme_manager.get_available_themes()
        if themes:
            print(f"✅ 获取到 {len(themes)} 个可用主题")
        else:
            print("⚠️  未获取到可用主题（可能是qt-material未安装）")
        
        # 测试主题分类
        categories = theme_manager.get_theme_categories()
        print(f"✅ 主题分类: 深色主题 {len(categories.get('dark', []))} 个, 浅色主题 {len(categories.get('light', []))} 个")
        
        return True
    except Exception as e:
        print(f"❌ 主题管理器创建失败: {e}")
        return False

def test_main_window_integration():
    """测试主窗口集成"""
    try:
        from PySide6.QtWidgets import QApplication
        from utils.config_manager_simple import ConfigManager
        from gui.main_window import MainWindow
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 加载配置
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # 创建主窗口（不显示）
        main_window = MainWindow(config)
        print("✅ 主窗口创建成功，Material主题集成正常")
        
        # 测试主题管理器是否正确初始化
        if hasattr(main_window, 'theme_manager') and main_window.theme_manager:
            print("✅ 主题管理器已正确集成到主窗口")
            current_theme = main_window.theme_manager.get_current_theme()
            print(f"✅ 当前主题: {current_theme}")
        else:
            print("⚠️  主题管理器未正确集成到主窗口")
        
        return True
    except Exception as e:
        print(f"❌ 主窗口集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎨 EWReborn Material主题集成测试")
    print("=" * 50)
    
    tests = [
        ("Material主题模块导入", test_material_theme_import),
        ("qt-material库可用性", test_qt_material_availability),
        ("主题管理器创建", test_theme_manager_creation),
        ("主窗口集成", test_main_window_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试: {test_name}")
        print("-" * 30)
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Material主题集成成功")
        return 0
    else:
        print("⚠️  部分测试失败，请检查上述错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
