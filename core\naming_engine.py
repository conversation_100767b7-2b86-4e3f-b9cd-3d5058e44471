"""
命名引擎系统
提供智能命名、规则管理和冲突解决功能
"""

import re
import logging
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass
from enum import Enum
from collections import defaultdict

from core.logger import get_logger
from core.data_models import IOPoint, SignalType


class NamingElementType(Enum):
    """命名元素类型"""
    CABINET = "CABINET"
    RACK = "RACK"
    SLOT = "SLOT"
    CARD = "CARD"
    TERMINAL_BLOCK = "TERMINAL_BLOCK"
    CHANNEL = "CHANNEL"
    TAG = "TAG"


@dataclass
class NamingRule:
    """命名规则"""
    element_type: NamingElementType
    pattern: str
    description: str
    examples: List[str] = None
    validation_regex: str = ""
    auto_increment: bool = True
    prefix: str = ""
    suffix: str = ""
    min_length: int = 1
    max_length: int = 50
    
    def __post_init__(self):
        if self.examples is None:
            self.examples = []


@dataclass
class NamingContext:
    """命名上下文"""
    cabinet_name: str = ""
    rack_name: str = ""
    slot_name: str = ""
    signal_type: SignalType = SignalType.DI
    system: str = ""
    location: str = ""
    is_spare: bool = False
    sequence_number: int = 1
    custom_attributes: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.custom_attributes is None:
            self.custom_attributes = {}


@dataclass
class NamingResult:
    """命名结果"""
    success: bool = False
    generated_name: str = ""
    conflicts: List[str] = None
    warnings: List[str] = None
    suggestions: List[str] = None
    
    def __post_init__(self):
        if self.conflicts is None:
            self.conflicts = []
        if self.warnings is None:
            self.warnings = []
        if self.suggestions is None:
            self.suggestions = []


class NamingRuleManager:
    """命名规则管理器"""
    
    def __init__(self):
        """初始化命名规则管理器"""
        self.logger = get_logger(__name__)
        self.rules: Dict[NamingElementType, NamingRule] = {}
        self._load_default_rules()
    
    def _load_default_rules(self):
        """加载默认命名规则"""
        # 机柜命名规则
        cabinet_rule = NamingRule(
            element_type=NamingElementType.CABINET,
            pattern="{system}-{location}-{sequence:03d}",
            description="机柜命名：系统-位置-序号",
            examples=["SIS-CR-001", "DCS-FR-002"],
            validation_regex=r"^[A-Z]{2,4}-[A-Z]{2,4}-\d{3}$",
            auto_increment=True
        )
        self.rules[NamingElementType.CABINET] = cabinet_rule
        
        # 机架命名规则
        rack_rule = NamingRule(
            element_type=NamingElementType.RACK,
            pattern="{prefix}{sequence:02d}",
            description="机架命名：前缀+序号",
            examples=["R01", "C01", "R02"],
            validation_regex=r"^[RC]\d{2}$",
            auto_increment=True,
            prefix="R"
        )
        self.rules[NamingElementType.RACK] = rack_rule
        
        # 槽位命名规则
        slot_rule = NamingRule(
            element_type=NamingElementType.SLOT,
            pattern="{sequence:02d}",
            description="槽位命名：两位数字",
            examples=["01", "02", "15"],
            validation_regex=r"^\d{2}$",
            auto_increment=True
        )
        self.rules[NamingElementType.SLOT] = slot_rule
        
        # 卡件命名规则
        card_rule = NamingRule(
            element_type=NamingElementType.CARD,
            pattern="{rack_prefix}{rack_num}S{slot_num}",
            description="卡件命名：机架前缀+机架号+S+槽位号",
            examples=["R01S01", "C02S15"],
            validation_regex=r"^[RC]\d{2}S\d{2}$",
            auto_increment=False
        )
        self.rules[NamingElementType.CARD] = card_rule
        
        # 端子排命名规则
        terminal_block_rule = NamingRule(
            element_type=NamingElementType.TERMINAL_BLOCK,
            pattern="TB{sequence:03d}",
            description="端子排命名：TB+三位序号",
            examples=["TB001", "TB002", "TB015"],
            validation_regex=r"^TB\d{3}$",
            auto_increment=True,
            prefix="TB"
        )
        self.rules[NamingElementType.TERMINAL_BLOCK] = terminal_block_rule
        
        # 通道命名规则
        channel_rule = NamingRule(
            element_type=NamingElementType.CHANNEL,
            pattern="{sequence:02d}",
            description="通道命名：两位数字",
            examples=["01", "02", "32"],
            validation_regex=r"^\d{2}$",
            auto_increment=True
        )
        self.rules[NamingElementType.CHANNEL] = channel_rule
        
        self.logger.info(f"加载了 {len(self.rules)} 个默认命名规则")
    
    def get_rule(self, element_type: NamingElementType) -> Optional[NamingRule]:
        """
        获取命名规则
        
        Args:
            element_type: 元素类型
            
        Returns:
            命名规则或None
        """
        return self.rules.get(element_type)
    
    def set_rule(self, rule: NamingRule):
        """
        设置命名规则
        
        Args:
            rule: 命名规则
        """
        self.rules[rule.element_type] = rule
        self.logger.info(f"设置命名规则: {rule.element_type.value}")
    
    def validate_name(self, element_type: NamingElementType, name: str) -> bool:
        """
        验证名称是否符合规则
        
        Args:
            element_type: 元素类型
            name: 名称
            
        Returns:
            是否有效
        """
        rule = self.get_rule(element_type)
        if not rule or not rule.validation_regex:
            return True
        
        return bool(re.match(rule.validation_regex, name))


class NamingValidator:
    """命名验证器"""
    
    def __init__(self, rule_manager: NamingRuleManager):
        """
        初始化命名验证器
        
        Args:
            rule_manager: 命名规则管理器
        """
        self.rule_manager = rule_manager
        self.logger = get_logger(__name__)
    
    def validate_name(self, element_type: NamingElementType, name: str) -> Tuple[bool, List[str]]:
        """
        验证名称
        
        Args:
            element_type: 元素类型
            name: 名称
            
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        rule = self.rule_manager.get_rule(element_type)
        if not rule:
            return True, errors
        
        # 长度检查
        if len(name) < rule.min_length:
            errors.append(f"名称长度不能少于 {rule.min_length} 个字符")
        
        if len(name) > rule.max_length:
            errors.append(f"名称长度不能超过 {rule.max_length} 个字符")
        
        # 正则表达式验证
        if rule.validation_regex and not re.match(rule.validation_regex, name):
            errors.append(f"名称格式不符合规则: {rule.description}")
        
        return len(errors) == 0, errors
    
    def check_conflicts(self, element_type: NamingElementType, name: str, 
                       existing_names: Set[str]) -> List[str]:
        """
        检查命名冲突
        
        Args:
            element_type: 元素类型
            name: 名称
            existing_names: 已存在的名称集合
            
        Returns:
            冲突信息列表
        """
        conflicts = []
        
        if name in existing_names:
            conflicts.append(f"名称 '{name}' 已存在")
        
        # 检查相似名称
        similar_names = [existing for existing in existing_names 
                        if self._is_similar(name, existing)]
        
        if similar_names:
            conflicts.append(f"存在相似名称: {', '.join(similar_names)}")
        
        return conflicts
    
    def _is_similar(self, name1: str, name2: str) -> bool:
        """
        检查两个名称是否相似
        
        Args:
            name1: 名称1
            name2: 名称2
            
        Returns:
            是否相似
        """
        # 简单的相似度检查：编辑距离
        if abs(len(name1) - len(name2)) > 2:
            return False
        
        # 计算编辑距离
        distance = self._edit_distance(name1.lower(), name2.lower())
        return distance <= 2
    
    def _edit_distance(self, s1: str, s2: str) -> int:
        """
        计算编辑距离
        
        Args:
            s1: 字符串1
            s2: 字符串2
            
        Returns:
            编辑距离
        """
        m, n = len(s1), len(s2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        
        for i in range(m + 1):
            dp[i][0] = i
        for j in range(n + 1):
            dp[0][j] = j
        
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if s1[i-1] == s2[j-1]:
                    dp[i][j] = dp[i-1][j-1]
                else:
                    dp[i][j] = min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1]) + 1
        
        return dp[m][n]


class NamingConflictResolver:
    """命名冲突解决器"""
    
    def __init__(self, rule_manager: NamingRuleManager):
        """
        初始化冲突解决器
        
        Args:
            rule_manager: 命名规则管理器
        """
        self.rule_manager = rule_manager
        self.logger = get_logger(__name__)
    
    def resolve_conflict(self, element_type: NamingElementType, base_name: str, 
                        existing_names: Set[str]) -> str:
        """
        解决命名冲突
        
        Args:
            element_type: 元素类型
            base_name: 基础名称
            existing_names: 已存在的名称集合
            
        Returns:
            解决冲突后的名称
        """
        if base_name not in existing_names:
            return base_name
        
        rule = self.rule_manager.get_rule(element_type)
        
        # 如果支持自动递增
        if rule and rule.auto_increment:
            return self._generate_incremental_name(base_name, existing_names)
        else:
            return self._generate_suffix_name(base_name, existing_names)
    
    def _generate_incremental_name(self, base_name: str, existing_names: Set[str]) -> str:
        """
        生成递增名称
        
        Args:
            base_name: 基础名称
            existing_names: 已存在的名称集合
            
        Returns:
            新名称
        """
        # 提取基础部分和数字部分
        match = re.match(r'(.+?)(\d+)$', base_name)
        if match:
            prefix = match.group(1)
            start_num = int(match.group(2))
        else:
            prefix = base_name
            start_num = 1
        
        # 寻找可用的数字
        counter = start_num
        while True:
            new_name = f"{prefix}{counter:0{len(str(start_num))}d}"
            if new_name not in existing_names:
                return new_name
            counter += 1
    
    def _generate_suffix_name(self, base_name: str, existing_names: Set[str]) -> str:
        """
        生成后缀名称
        
        Args:
            base_name: 基础名称
            existing_names: 已存在的名称集合
            
        Returns:
            新名称
        """
        counter = 1
        while True:
            new_name = f"{base_name}_{counter}"
            if new_name not in existing_names:
                return new_name
            counter += 1


class NamingEngine:
    """命名引擎"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化命名引擎
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = get_logger(__name__)
        
        # 初始化组件
        self.rule_manager = NamingRuleManager()
        self.validator = NamingValidator(self.rule_manager)
        self.conflict_resolver = NamingConflictResolver(self.rule_manager)
        
        # 名称缓存
        self.name_cache: Dict[NamingElementType, Set[str]] = defaultdict(set)
        
        self.logger.info("命名引擎初始化完成")
    
    def generate_name(self, element_type: NamingElementType, 
                     context: NamingContext) -> NamingResult:
        """
        生成名称
        
        Args:
            element_type: 元素类型
            context: 命名上下文
            
        Returns:
            命名结果
        """
        result = NamingResult()
        
        try:
            rule = self.rule_manager.get_rule(element_type)
            if not rule:
                result.warnings.append(f"未找到 {element_type.value} 的命名规则")
                result.generated_name = f"DEFAULT_{context.sequence_number:03d}"
                result.success = True
                return result
            
            # 生成基础名称
            base_name = self._generate_base_name(rule, context)
            
            # 检查冲突
            existing_names = self.name_cache[element_type]
            conflicts = self.validator.check_conflicts(element_type, base_name, existing_names)
            
            if conflicts:
                result.conflicts.extend(conflicts)
                # 解决冲突
                resolved_name = self.conflict_resolver.resolve_conflict(
                    element_type, base_name, existing_names
                )
                result.generated_name = resolved_name
            else:
                result.generated_name = base_name
            
            # 验证最终名称
            is_valid, errors = self.validator.validate_name(element_type, result.generated_name)
            if not is_valid:
                result.warnings.extend(errors)
            
            # 添加到缓存
            self.name_cache[element_type].add(result.generated_name)
            
            result.success = True
            self.logger.debug(f"生成名称: {element_type.value} -> {result.generated_name}")
            
        except Exception as e:
            result.warnings.append(f"生成名称失败: {e}")
            self.logger.error(f"生成名称失败: {e}")
        
        return result
    
    def _generate_base_name(self, rule: NamingRule, context: NamingContext) -> str:
        """
        生成基础名称
        
        Args:
            rule: 命名规则
            context: 命名上下文
            
        Returns:
            基础名称
        """
        # 准备格式化参数
        format_params = {
            'sequence': context.sequence_number,
            'system': context.system,
            'location': context.location,
            'cabinet': context.cabinet_name,
            'rack': context.rack_name,
            'slot': context.slot_name,
            'signal_type': context.signal_type.value if context.signal_type else 'DI',
            'prefix': rule.prefix,
            'suffix': rule.suffix
        }
        
        # 添加自定义属性
        format_params.update(context.custom_attributes)
        
        # 格式化名称
        try:
            name = rule.pattern.format(**format_params)
        except KeyError as e:
            self.logger.warning(f"命名模式中缺少参数: {e}")
            name = f"{rule.prefix}{context.sequence_number:03d}{rule.suffix}"
        
        return name
    
    def register_existing_name(self, element_type: NamingElementType, name: str):
        """
        注册已存在的名称
        
        Args:
            element_type: 元素类型
            name: 名称
        """
        self.name_cache[element_type].add(name)
    
    def clear_cache(self, element_type: Optional[NamingElementType] = None):
        """
        清空名称缓存
        
        Args:
            element_type: 元素类型，None表示清空所有
        """
        if element_type:
            self.name_cache[element_type].clear()
        else:
            self.name_cache.clear()
        
        self.logger.info("名称缓存已清空")
    
    def get_naming_statistics(self) -> Dict[str, Any]:
        """
        获取命名统计信息
        
        Returns:
            统计信息字典
        """
        stats = {}
        for element_type, names in self.name_cache.items():
            stats[element_type.value] = {
                'count': len(names),
                'names': list(names)
            }
        
        return stats
