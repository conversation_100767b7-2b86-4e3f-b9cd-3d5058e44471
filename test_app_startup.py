#!/usr/bin/env python3
"""
测试应用程序启动
验证EWReborn应用程序能否正常启动并显示Material主题界面
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_app_startup():
    """测试应用程序启动"""
    try:
        from PySide6.QtWidgets import QApplication
        from utils.config_manager_simple import ConfigManager
        from gui.main_window import MainWindow
        
        print("🚀 启动EWReborn应用程序...")
        
        # 创建应用程序实例
        app = QApplication(sys.argv)
        app.setApplicationName("EWReborn")
        app.setApplicationVersion("1.0.0")
        
        # 加载配置
        print("📋 加载配置...")
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # 创建主窗口
        print("🏠 创建主窗口...")
        main_window = MainWindow(config)
        
        # 检查主题管理器
        if hasattr(main_window, 'theme_manager') and main_window.theme_manager:
            current_theme = main_window.theme_manager.get_current_theme()
            print(f"🎨 当前主题: {current_theme}")
            
            # 测试主题切换
            print("🔄 测试主题切换...")
            success = main_window.theme_manager.apply_theme('light_blue.xml', app)
            if success:
                print("✅ 主题切换成功（使用备用样式）")
            else:
                print("⚠️  主题切换失败，但这是预期的（qt-material未安装）")
        
        # 显示窗口（仅显示很短时间用于测试）
        print("👁️  显示窗口进行视觉测试...")
        main_window.show()
        
        # 处理事件循环一小段时间
        app.processEvents()
        
        print("✅ 应用程序启动测试成功！")
        print("💡 窗口已创建并可以正常显示")
        print("🎨 Material主题系统已集成（使用备用样式）")
        
        # 关闭窗口
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 应用程序启动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 EWReborn应用程序启动测试")
    print("=" * 50)
    
    success = test_app_startup()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试通过！应用程序可以正常启动")
        print("📝 注意：要获得完整的Material Design体验，请安装：")
        print("   pip install qt-material qtawesome")
        return 0
    else:
        print("❌ 测试失败，请检查上述错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
