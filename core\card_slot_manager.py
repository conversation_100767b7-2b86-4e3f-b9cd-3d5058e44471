"""
卡件槽位管理器
负责管理I/O卡件的槽位分配
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from core.logger import get_logger
from core.data_models import IOPoint, SignalType


class RackPrefix(Enum):
    """机架前缀"""
    R = "R"  # R前缀
    C = "C"  # C前缀


class CardAllocationStrategy(Enum):
    """卡件分配策略"""
    PRIORITY = "PRIORITY"          # 优先级分配
    LOAD_BALANCE = "LOAD_BALANCE"  # 负载均衡


@dataclass
class CardSlot:
    """卡件槽位数据类"""
    rack_name: str
    slot_number: int
    card_type: str = ""
    max_channels: int = 16
    used_channels: int = 0
    io_points: List[IOPoint] = None
    cabinet: str = ""
    
    def __post_init__(self):
        if self.io_points is None:
            self.io_points = []
    
    @property
    def available_channels(self) -> int:
        """可用通道数"""
        return self.max_channels - self.used_channels
    
    @property
    def is_full(self) -> bool:
        """是否已满"""
        return self.used_channels >= self.max_channels
    
    @property
    def utilization(self) -> float:
        """利用率百分比"""
        return (self.used_channels / self.max_channels * 100) if self.max_channels > 0 else 0


@dataclass
class CardSlotAllocationResult:
    """卡件槽位分配结果"""
    success: bool = False
    card_slots: List[CardSlot] = None
    allocated_points: List[IOPoint] = None
    failed_points: List[IOPoint] = None
    warnings: List[str] = None
    errors: List[str] = None
    summary: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.card_slots is None:
            self.card_slots = []
        if self.allocated_points is None:
            self.allocated_points = []
        if self.failed_points is None:
            self.failed_points = []
        if self.warnings is None:
            self.warnings = []
        if self.errors is None:
            self.errors = []
        if self.summary is None:
            self.summary = {}


class CardSlotManager:
    """卡件槽位管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化卡件槽位管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = get_logger(__name__)
        
        # 配置参数
        self.rack_prefix = RackPrefix.R
        self.etp_upper_suffix = "U"
        self.etp_lower_suffix = "L"
        self.allocation_strategy = CardAllocationStrategy.PRIORITY
        
        # 卡件类型映射
        self.card_type_mapping = {
            SignalType.AI: 'CPM16-AI3700',
            SignalType.AO: 'CPM16-AO3700',
            SignalType.DI: 'CPM16-DI3700',
            SignalType.DO: 'CPM16-DO3700'
        }
        
        # 卡件配置
        self.max_channels_per_card = 16
        self.max_slots_per_rack = 16
    
    def set_rack_prefix(self, prefix: RackPrefix):
        """
        设置机架前缀
        
        Args:
            prefix: 机架前缀
        """
        self.rack_prefix = prefix
        self.logger.info(f"设置机架前缀: {prefix.value}")
    
    def set_etp_suffixes(self, upper: str, lower: str):
        """
        设置ETP后缀
        
        Args:
            upper: 上层后缀
            lower: 下层后缀
        """
        self.etp_upper_suffix = upper
        self.etp_lower_suffix = lower
        self.logger.info(f"设置ETP后缀: 上层={upper}, 下层={lower}")
    
    def set_allocation_strategy(self, strategy: CardAllocationStrategy):
        """
        设置分配策略
        
        Args:
            strategy: 卡件分配策略
        """
        self.allocation_strategy = strategy
        self.logger.info(f"设置卡件分配策略: {strategy.value}")
    
    def allocate_card_slots(self, io_points: List[IOPoint], 
                           cabinets: List[Dict[str, Any]]) -> CardSlotAllocationResult:
        """
        分配卡件槽位
        
        Args:
            io_points: I/O点列表
            cabinets: 机柜列表
            
        Returns:
            卡件槽位分配结果
        """
        self.logger.info(f"开始卡件槽位分配，策略: {self.allocation_strategy.value}, I/O点数: {len(io_points)}")
        
        result = CardSlotAllocationResult()
        
        try:
            # 初始化卡件槽位
            card_slots = self._initialize_card_slots(cabinets)
            result.card_slots = card_slots
            
            # 按信号类型分组I/O点
            signal_groups = self._group_by_signal_type(io_points)
            
            # 为每个信号类型分配槽位
            for signal_type, points in signal_groups.items():
                self._allocate_signal_type_points(signal_type, points, card_slots, result)
            
            # 生成分配摘要
            result.summary = self._generate_summary(result)
            result.success = len(result.errors) == 0
            
            if result.success:
                self.logger.info("卡件槽位分配成功完成")
            else:
                self.logger.warning(f"卡件槽位分配完成，但有 {len(result.errors)} 个错误")
            
            return result
            
        except Exception as e:
            self.logger.error(f"卡件槽位分配异常: {e}")
            result.errors.append(f"卡件槽位分配异常: {e}")
            result.success = False
            return result
    
    def _initialize_card_slots(self, cabinets: List[Dict[str, Any]]) -> List[CardSlot]:
        """
        初始化卡件槽位
        
        Args:
            cabinets: 机柜列表
            
        Returns:
            卡件槽位列表
        """
        card_slots = []
        
        for cabinet in cabinets:
            cabinet_name = cabinet.get('name', '')
            rails = cabinet.get('rails', [])
            
            # 为每个导轨创建机架
            for rail_index, rail in enumerate(rails):
                rack_name = f"{self.rack_prefix.value}{rail_index + 1:02d}"
                
                # 为每个机架创建槽位
                for slot_num in range(1, self.max_slots_per_rack + 1):
                    card_slot = CardSlot(
                        rack_name=rack_name,
                        slot_number=slot_num,
                        max_channels=self.max_channels_per_card,
                        cabinet=cabinet_name
                    )
                    card_slots.append(card_slot)
                    
                    self.logger.debug(f"初始化卡件槽位: {cabinet_name}.{rack_name}.{slot_num:02d}")
        
        return card_slots
    
    def _group_by_signal_type(self, io_points: List[IOPoint]) -> Dict[SignalType, List[IOPoint]]:
        """
        按信号类型分组I/O点
        
        Args:
            io_points: I/O点列表
            
        Returns:
            按信号类型分组的字典
        """
        groups = {}
        
        for point in io_points:
            signal_type = point.signal_type
            
            if signal_type not in groups:
                groups[signal_type] = []
            
            groups[signal_type].append(point)
        
        return groups
    
    def _allocate_signal_type_points(self, signal_type: SignalType, 
                                   points: List[IOPoint], 
                                   card_slots: List[CardSlot], 
                                   result: CardSlotAllocationResult):
        """
        为特定信号类型的I/O点分配槽位
        
        Args:
            signal_type: 信号类型
            points: I/O点列表
            card_slots: 卡件槽位列表
            result: 分配结果对象
        """
        card_type = self.card_type_mapping.get(signal_type, 'CPM16-DI3700')
        self.logger.info(f"分配信号类型 {signal_type.value} 的 {len(points)} 个I/O点，卡件类型: {card_type}")
        
        # 查找或创建适合的卡件槽位
        suitable_slots = self._find_suitable_slots(card_type, card_slots)
        
        for point in points:
            allocated = False
            
            # 尝试分配到现有槽位
            for slot in suitable_slots:
                if slot.available_channels > 0:
                    # 分配成功
                    slot.io_points.append(point)
                    slot.used_channels += 1
                    slot.card_type = card_type
                    
                    # 更新I/O点的分配信息
                    point.cabinet = slot.cabinet
                    point.rack = slot.rack_name
                    point.slot = f"{slot.slot_number:02d}"
                    point.channel = f"{slot.used_channels:02d}"
                    
                    result.allocated_points.append(point)
                    allocated = True
                    
                    self.logger.debug(f"分配I/O点 {point.tag} 到槽位 {slot.cabinet}.{slot.rack_name}.{slot.slot_number:02d}.{slot.used_channels:02d}")
                    break
            
            if not allocated:
                # 分配失败
                result.failed_points.append(point)
                error_msg = f"无法为I/O点 {point.tag} ({signal_type.value}) 分配槽位"
                result.errors.append(error_msg)
                self.logger.error(error_msg)
    
    def _find_suitable_slots(self, card_type: str, card_slots: List[CardSlot]) -> List[CardSlot]:
        """
        查找适合的卡件槽位
        
        Args:
            card_type: 卡件类型
            card_slots: 卡件槽位列表
            
        Returns:
            适合的槽位列表
        """
        suitable_slots = []
        
        # 首先查找已有相同卡件类型的槽位
        for slot in card_slots:
            if slot.card_type == card_type and not slot.is_full:
                suitable_slots.append(slot)
        
        # 如果没有找到，查找空槽位
        if not suitable_slots:
            for slot in card_slots:
                if not slot.card_type and not slot.is_full:
                    suitable_slots.append(slot)
        
        # 根据分配策略排序
        if self.allocation_strategy == CardAllocationStrategy.PRIORITY:
            # 优先级分配：优先使用已有卡件的槽位
            suitable_slots.sort(key=lambda x: (x.card_type == "", x.used_channels))
        elif self.allocation_strategy == CardAllocationStrategy.LOAD_BALANCE:
            # 负载均衡：优先使用利用率低的槽位
            suitable_slots.sort(key=lambda x: x.utilization)
        
        return suitable_slots
    
    def _generate_summary(self, result: CardSlotAllocationResult) -> Dict[str, Any]:
        """
        生成分配摘要
        
        Args:
            result: 分配结果对象
            
        Returns:
            摘要字典
        """
        total_points = len(result.allocated_points) + len(result.failed_points)
        success_rate = (len(result.allocated_points) / total_points * 100) if total_points > 0 else 0
        
        # 统计槽位利用率
        slot_utilization = []
        used_slots = 0
        total_slots = len(result.card_slots)
        
        for slot in result.card_slots:
            if slot.used_channels > 0:
                used_slots += 1
                slot_utilization.append({
                    'cabinet': slot.cabinet,
                    'rack': slot.rack_name,
                    'slot': slot.slot_number,
                    'card_type': slot.card_type,
                    'utilization': slot.utilization,
                    'used_channels': slot.used_channels,
                    'max_channels': slot.max_channels
                })
        
        # 按卡件类型统计
        card_type_stats = {}
        for slot in result.card_slots:
            if slot.card_type:
                if slot.card_type not in card_type_stats:
                    card_type_stats[slot.card_type] = {
                        'slots_used': 0,
                        'total_channels': 0,
                        'used_channels': 0
                    }
                
                card_type_stats[slot.card_type]['slots_used'] += 1
                card_type_stats[slot.card_type]['total_channels'] += slot.max_channels
                card_type_stats[slot.card_type]['used_channels'] += slot.used_channels
        
        summary = {
            'total_points': total_points,
            'allocated_points': len(result.allocated_points),
            'failed_points': len(result.failed_points),
            'success_rate': success_rate,
            'total_slots': total_slots,
            'used_slots': used_slots,
            'slot_utilization_rate': (used_slots / total_slots * 100) if total_slots > 0 else 0,
            'strategy': self.allocation_strategy.value,
            'rack_prefix': self.rack_prefix.value,
            'slot_details': slot_utilization,
            'card_type_stats': card_type_stats
        }
        
        return summary
