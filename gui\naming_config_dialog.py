"""
自定义命名配置对话框
允许用户配置各种器件的命名规则
"""

from typing import Dict, List, Any, Optional
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QLabel, QLineEdit, QPushButton, QComboBox, QListWidget,
    QListWidgetItem, QGroupBox, QFormLayout, QTextEdit,
    QCheckBox, QSpinBox, QMessageBox, QScrollArea
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from core.naming_engine import NamingEngine, NamingRule, NamingElement, NamingElementType
from core.logger import get_logger
from gui.styles import APPLE_COLORS


class NamingConfigDialog(QDialog):
    """自定义命名配置对话框"""
    
    # 信号：配置已更新
    config_updated = Signal(dict)
    
    def __init__(self, parent=None, current_config: Dict[str, Any] = None):
        """
        初始化命名配置对话框

        Args:
            parent: 父窗口
            current_config: 当前配置
        """
        super().__init__(parent)
        self.logger = get_logger(__name__)
        self.current_config = current_config or {}

        # 延迟创建命名引擎，避免初始化问题
        self.naming_engine = None

        self.setWindowTitle("自定义命名规则配置")
        self.setModal(True)
        self.resize(800, 600)

        try:
            self._setup_ui()
            self._load_current_config()
            self._apply_styles()
        except Exception as e:
            self.logger.error(f"初始化命名配置对话框失败: {e}")
            raise
    
    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 器件类型列表
        self.device_types = [
            ('barrier', '安全栅'),
            ('relay', '继电器'),
            ('isolator', '隔离器'),
            ('surge_protector', '防雷栅'),
            ('terminal_block', '端子排'),
            ('tr_terminal', 'TR端子排')
        ]
        
        # 为每种器件类型创建配置页面
        for device_type, display_name in self.device_types:
            tab = self._create_device_config_tab(device_type, display_name)
            self.tab_widget.addTab(tab, display_name)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.preview_btn = QPushButton("预览")
        self.preview_btn.clicked.connect(self._preview_naming)
        button_layout.addWidget(self.preview_btn)
        
        button_layout.addStretch()
        
        self.reset_btn = QPushButton("重置为默认")
        self.reset_btn.clicked.connect(self._reset_to_default)
        button_layout.addWidget(self.reset_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self._save_config)
        button_layout.addWidget(self.ok_btn)
        
        layout.addLayout(button_layout)
    
    def _create_device_config_tab(self, device_type: str, display_name: str) -> QWidget:
        """
        创建器件配置标签页
        
        Args:
            device_type: 器件类型
            display_name: 显示名称
            
        Returns:
            配置标签页
        """
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 说明文本
        info_label = QLabel(f"配置{display_name}的命名规则")
        info_label.setFont(QFont("", 10, QFont.Bold))
        layout.addWidget(info_label)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 命名元素选择区域
        elements_group = QGroupBox("命名元素")
        elements_layout = QVBoxLayout(elements_group)
        
        # 可用元素列表
        available_elements_label = QLabel("可用命名元素:")
        elements_layout.addWidget(available_elements_label)
        
        self._create_available_elements_list(elements_layout, device_type)
        
        # 当前规则显示
        current_rule_group = QGroupBox("当前命名规则")
        current_rule_layout = QVBoxLayout(current_rule_group)
        
        # 规则预览
        rule_preview = QTextEdit()
        rule_preview.setMaximumHeight(100)
        rule_preview.setReadOnly(True)
        rule_preview.setObjectName(f"{device_type}_rule_preview")
        current_rule_layout.addWidget(rule_preview)
        
        # 规则编辑器
        rule_editor_group = QGroupBox("编辑规则")
        rule_editor_layout = QFormLayout(rule_editor_group)
        
        # 分隔符设置
        separator_edit = QLineEdit()
        separator_edit.setObjectName(f"{device_type}_separator")
        separator_edit.setPlaceholderText("元素间分隔符（可选）")
        separator_edit.textChanged.connect(lambda: self._update_rule_preview(device_type))
        rule_editor_layout.addRow("分隔符:", separator_edit)

        # 自定义前缀
        prefix_edit = QLineEdit()
        prefix_edit.setObjectName(f"{device_type}_prefix")
        prefix_edit.setPlaceholderText("固定前缀（可选）")
        prefix_edit.textChanged.connect(lambda: self._update_rule_preview(device_type))
        rule_editor_layout.addRow("前缀:", prefix_edit)

        # 自定义后缀
        suffix_edit = QLineEdit()
        suffix_edit.setObjectName(f"{device_type}_suffix")
        suffix_edit.setPlaceholderText("固定后缀（可选）")
        suffix_edit.textChanged.connect(lambda: self._update_rule_preview(device_type))
        rule_editor_layout.addRow("后缀:", suffix_edit)
        
        scroll_layout.addWidget(elements_group)
        scroll_layout.addWidget(current_rule_group)
        scroll_layout.addWidget(rule_editor_group)
        scroll_layout.addStretch()
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        return tab
    
    def _create_available_elements_list(self, layout: QVBoxLayout, device_type: str):
        """
        创建可用元素列表
        
        Args:
            layout: 布局
            device_type: 器件类型
        """
        # 元素描述映射
        element_descriptions = {
            NamingElementType.FIXED_TEXT: "固定文本",
            NamingElementType.TAG_NAME: "Tag名",
            NamingElementType.RACK_NUMBER: "机架编号",
            NamingElementType.SLOT_NUMBER: "槽位编号",
            NamingElementType.CHANNEL_NUMBER: "通道号",
            NamingElementType.CABINET_NUMBER: "机柜编号",
            NamingElementType.RAIL_NUMBER: "导轨号",
            NamingElementType.DEVICE_NUMBER: "器件编号",
            NamingElementType.CABINET_NAME: "机柜名",
            NamingElementType.CABINET_NAME_SHORT: "简化机柜名",
            NamingElementType.CABLE_NAME: "电缆名",
            NamingElementType.ETP_NAME: "ETP名",
            NamingElementType.ETP_SUFFIX: "ETP后缀",
            NamingElementType.SIGNAL_TYPE: "信号类型",
            NamingElementType.CARD_PART_NUMBER: "卡件型号"
        }
        
        elements_list = QListWidget()
        elements_list.setObjectName(f"{device_type}_elements_list")
        elements_list.setMaximumHeight(200)
        
        for element_type, description in element_descriptions.items():
            item = QListWidgetItem(f"{description} ({element_type.value})")
            item.setData(Qt.UserRole, element_type)
            elements_list.addItem(item)
        
        layout.addWidget(elements_list)
        
        # 添加/移除按钮
        button_layout = QHBoxLayout()
        
        add_btn = QPushButton("添加到规则")
        add_btn.setObjectName(f"{device_type}_add_element")
        add_btn.clicked.connect(lambda: self._add_element_to_rule(device_type))
        button_layout.addWidget(add_btn)
        
        remove_btn = QPushButton("从规则移除")
        remove_btn.setObjectName(f"{device_type}_remove_element")
        remove_btn.clicked.connect(lambda: self._remove_element_from_rule(device_type))
        button_layout.addWidget(remove_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def _add_element_to_rule(self, device_type: str):
        """添加元素到规则"""
        elements_list = self.findChild(QListWidget, f"{device_type}_elements_list")
        current_item = elements_list.currentItem()
        
        if current_item:
            element_type = current_item.data(Qt.UserRole)
            self.logger.info(f"添加元素到{device_type}规则: {element_type}")
            self._update_rule_preview(device_type)
    
    def _remove_element_from_rule(self, device_type: str):
        """从规则移除元素"""
        self.logger.info(f"从{device_type}规则移除元素")
        self._update_rule_preview(device_type)
    
    def _update_rule_preview(self, device_type: str):
        """更新规则预览"""
        rule_preview = self.findChild(QTextEdit, f"{device_type}_rule_preview")
        if not rule_preview:
            return

        try:
            # 获取当前配置
            separator_edit = self.findChild(QLineEdit, f"{device_type}_separator")
            prefix_edit = self.findChild(QLineEdit, f"{device_type}_prefix")
            suffix_edit = self.findChild(QLineEdit, f"{device_type}_suffix")

            separator = ""
            prefix = ""
            suffix = ""

            if separator_edit:
                try:
                    separator = separator_edit.text()
                except:
                    separator = ""

            if prefix_edit:
                try:
                    prefix = prefix_edit.text()
                except:
                    prefix = ""

            if suffix_edit:
                try:
                    suffix = suffix_edit.text()
                except:
                    suffix = ""

            # 延迟创建命名引擎
            if not self.naming_engine:
                try:
                    self.naming_engine = NamingEngine()
                except Exception as e:
                    rule_preview.setText(f"命名引擎初始化失败: {e}")
                    return

            # 获取默认规则
            try:
                default_rule = self.naming_engine.get_default_rule(device_type)
                if not default_rule:
                    rule_preview.setText("未找到默认规则")
                    return
            except Exception as e:
                rule_preview.setText(f"获取默认规则失败: {e}")
                return

            # 生成预览文本
            preview_parts = []
            if prefix:
                preview_parts.append(f"前缀: {prefix}")

            # 显示默认元素
            element_names = []
            for element in default_rule.elements:
                if element.element_type.value == 'rack_number':
                    element_names.append("机架号")
                elif element.element_type.value == 'slot_number':
                    element_names.append("槽位号")
                elif element.element_type.value == 'channel_number':
                    element_names.append("通道号(2位)")
                elif element.element_type.value == 'cabinet_number':
                    element_names.append("机柜号")
                elif element.element_type.value == 'rail_number':
                    element_names.append("导轨号")
                elif element.element_type.value == 'device_number':
                    element_names.append("器件号")
                elif element.element_type.value == 'etp_name':
                    element_names.append("ETP名")

            if element_names:
                elements_text = separator.join(element_names) if separator else "".join(element_names)
                preview_parts.append(f"元素: {elements_text}")

            if suffix:
                preview_parts.append(f"后缀: {suffix}")

            # 生成示例
            example = self._generate_example(device_type, prefix, separator, suffix)
            if example:
                preview_parts.append(f"示例: {example}")

            rule_preview.setText("\n".join(preview_parts))

        except Exception as e:
            rule_preview.setText(f"预览生成失败: {e}")

    def _generate_example(self, device_type: str, prefix: str, separator: str, suffix: str) -> str:
        """生成命名示例"""
        try:
            # 根据器件类型生成不同的示例
            if device_type in ['barrier', 'relay', 'isolator']:
                # 安全栅/继电器/隔离器: 前缀 + 机架号 + 槽位号 + 通道号
                parts = []
                if prefix:
                    parts.append(prefix)
                parts.extend(['1', '3', '05'])  # 示例: 机架1, 槽位3, 通道05

                if separator:
                    example = separator.join(parts)
                else:
                    example = ''.join(parts)

                if suffix:
                    example += suffix

                return example

            elif device_type == 'surge_protector':
                # 防雷栅: SP + 机架号 + 槽位号 + 通道号
                parts = []
                if prefix:
                    parts.append(prefix)
                parts.extend(['1', '5', '05'])  # 示例: 机架1, 槽位5, 通道05

                if separator:
                    example = separator.join(parts)
                else:
                    example = ''.join(parts)

                if suffix:
                    example += suffix

                return example

            elif device_type == 'terminal_block':
                # 端子排: TB + 机柜号 + 导轨号 + 器件号
                parts = []
                if prefix:
                    parts.append(prefix)
                parts.extend(['02', 'F', '03'])  # 示例: 机柜02, 导轨F, 器件03

                if separator:
                    example = separator.join(parts)
                else:
                    example = ''.join(parts)

                if suffix:
                    example += suffix

                return example

            elif device_type == 'tr_terminal':
                # TR端子排: TR + ETP名
                parts = []
                if prefix:
                    parts.append(prefix)
                parts.append('R1S3U')  # 示例ETP名

                if separator:
                    example = separator.join(parts)
                else:
                    example = ''.join(parts)

                if suffix:
                    example += suffix

                return example

            return ""

        except Exception as e:
            return f"示例生成失败: {e}"
    
    def _load_current_config(self):
        """加载当前配置"""
        # 从current_config中加载配置到界面
        if not self.current_config:
            return

        for device_type, _ in self.device_types:
            device_config = self.current_config.get(device_type, {})

            # 加载分隔符
            separator_edit = self.findChild(QLineEdit, f"{device_type}_separator")
            if separator_edit and 'separator' in device_config:
                separator_edit.setText(device_config['separator'])

            # 加载前缀
            prefix_edit = self.findChild(QLineEdit, f"{device_type}_prefix")
            if prefix_edit and 'prefix' in device_config:
                prefix_edit.setText(device_config['prefix'])

            # 加载后缀
            suffix_edit = self.findChild(QLineEdit, f"{device_type}_suffix")
            if suffix_edit and 'suffix' in device_config:
                suffix_edit.setText(device_config['suffix'])

            # 更新规则预览
            self._update_rule_preview(device_type)
    
    def _preview_naming(self):
        """预览命名结果"""
        try:
            # 收集当前所有配置
            config = self._collect_config()

            # 生成预览文本
            preview_text = "命名规则预览:\n\n"

            for device_type, display_name in self.device_types:
                device_config = config.get(device_type, {})
                prefix = device_config.get('prefix', '')
                separator = device_config.get('separator', '')
                suffix = device_config.get('suffix', '')

                example = self._generate_example(device_type, prefix, separator, suffix)
                preview_text += f"{display_name}: {example}\n"

            # 显示预览对话框
            preview_dialog = QDialog(self)
            preview_dialog.setWindowTitle("命名规则预览")
            preview_dialog.resize(400, 300)

            layout = QVBoxLayout(preview_dialog)

            preview_label = QTextEdit()
            preview_label.setPlainText(preview_text)
            preview_label.setReadOnly(True)
            layout.addWidget(preview_label)

            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(preview_dialog.accept)
            layout.addWidget(close_btn)

            preview_dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "预览失败", f"生成预览失败:\n{str(e)}")
    
    def _reset_to_default(self):
        """重置为默认配置"""
        reply = QMessageBox.question(
            self, "重置确认", 
            "确定要重置所有命名规则为默认设置吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self._load_default_config()
    
    def _load_default_config(self):
        """加载默认配置"""
        # 重置所有配置为默认值
        default_prefixes = {
            'barrier': 'BA',
            'relay': 'RY',
            'isolator': 'ISL',
            'surge_protector': 'SP',
            'terminal_block': 'TB',
            'tr_terminal': 'TR'
        }

        for device_type, _ in self.device_types:
            # 重置前缀
            prefix_edit = self.findChild(QLineEdit, f"{device_type}_prefix")
            if prefix_edit:
                prefix_edit.setText(default_prefixes.get(device_type, ''))

            # 清空分隔符
            separator_edit = self.findChild(QLineEdit, f"{device_type}_separator")
            if separator_edit:
                separator_edit.setText('')

            # 清空后缀
            suffix_edit = self.findChild(QLineEdit, f"{device_type}_suffix")
            if suffix_edit:
                suffix_edit.setText('')

            # 更新预览
            self._update_rule_preview(device_type)
    
    def _save_config(self):
        """保存配置"""
        try:
            # 收集所有配置
            config = self._collect_config()
            
            # 发送配置更新信号
            self.config_updated.emit(config)
            
            self.accept()
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            QMessageBox.critical(self, "保存失败", f"保存配置失败:\n{str(e)}")
    
    def _collect_config(self) -> Dict[str, Any]:
        """收集配置"""
        config = {}
        
        for device_type, _ in self.device_types:
            device_config = {}
            
            # 收集分隔符
            separator_edit = self.findChild(QLineEdit, f"{device_type}_separator")
            if separator_edit:
                try:
                    device_config['separator'] = separator_edit.text()
                except:
                    device_config['separator'] = ""

            # 收集前缀
            prefix_edit = self.findChild(QLineEdit, f"{device_type}_prefix")
            if prefix_edit:
                try:
                    device_config['prefix'] = prefix_edit.text()
                except:
                    device_config['prefix'] = ""

            # 收集后缀
            suffix_edit = self.findChild(QLineEdit, f"{device_type}_suffix")
            if suffix_edit:
                try:
                    device_config['suffix'] = suffix_edit.text()
                except:
                    device_config['suffix'] = ""
            
            config[device_type] = device_config
        
        return config
    
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {APPLE_COLORS['background']};
                color: {APPLE_COLORS['text']};
            }}
            
            QTabWidget::pane {{
                border: 1px solid {APPLE_COLORS['border']};
                background-color: {APPLE_COLORS['surface']};
            }}
            
            QTabBar::tab {{
                background-color: {APPLE_COLORS['surface']};
                color: {APPLE_COLORS['text']};
                padding: 8px 16px;
                margin-right: 2px;
            }}
            
            QTabBar::tab:selected {{
                background-color: {APPLE_COLORS['primary']};
                color: white;
            }}
            
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {APPLE_COLORS['border']};
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
