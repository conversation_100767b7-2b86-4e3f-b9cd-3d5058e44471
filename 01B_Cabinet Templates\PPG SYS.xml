<?xml version="1.0"?>
<ProfileComponent xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" Name="PPG SYS" Type="G60_CX_ESD_SYS_RACK_CAB" Count="1" Mapping="" Category="SafetyCabinetProfile">
  <ProfileProperties>
    <ProfileProperty Name="Width" PropertyType="integer" Value="800" Units="mm" />
    <ProfileProperty Name="Depth" PropertyType="integer" Value="800" Units="mm" />
    <ProfileProperty Name="Height" PropertyType="integer" Value="2000" Units="mm" />
    <ProfileProperty Name="Mass" PropertyType="integer" Value="2" Units="kgs" />
    <ProfileProperty Name="MainPower" PropertyType="integer" Value="125" Units="vdc" />
    <ProfileProperty Name="FieldPower" PropertyType="integer" Value="" Units="" />
    <ProfileProperty Name="UtilityPower" PropertyType="integer" Value="" Units="" />
    <ProfileProperty Name="Vented" PropertyType="string" Value="Yes" Units="" />
    <ProfileProperty Name="RoofMountedFans" PropertyType="integer" Value="2" Units="" />
    <ProfileProperty Name="DoorMountedFans" PropertyType="integer" Value="0" Units="" />
    <ProfileProperty Name="Type" PropertyType="string" Value="System" Units="" />
    <ProfileProperty Name="Plinth" PropertyType="integer" Value="100" Units="mm" />
    <ProfileProperty Name="CableEntry" PropertyType="string" Value="Bottom" Units="" />
    <ProfileProperty Name="Plinth" PropertyType="integer" Value="100" Units="mm" />
    <ProfileProperty Name="Description" PropertyType="string" Value="Tricon CX System cabinet" Units="" />
  </ProfileProperties>
  <ProfileSignatures>
    <ProfileSignature Name="Type" PropertyType="string" Value="System" Units="" Description="" />
    <ProfileSignature Name="PartNumber" PropertyType="string" Value="^G60{*}" Units="" Description="Regular expression" />
  </ProfileSignatures>
  <Components>
    <ProfileComponent Name="Rack1" Type="Upper Chassis" Count="1" Mapping="Rack">
      <ProfileProperties>
        <ProfileProperty Name="Description" PropertyType="string" Value="Front Chassis Location" Units="" />
        <ProfileProperty Name="Position" PropertyType="string" Value="FrontUpper" Units="" />
        <ProfileProperty Name="Orientation" PropertyType="string" Value="Vertical" Units="" />
        <ProfileProperty Name="PartType01" PropertyType="string" Value="MainProcessorChassis" Units="" />
        <ProfileProperty Name="PartNumber01" PropertyType="string" Value="8120E" Units="" />
        <ProfileProperty Name="PartQty01" PropertyType="string" Value="1" Units="" />
        <ProfileProperty Name="Length" PropertyType="integer" Value="600" Units="mm" />
      </ProfileProperties>
      <ProfileSignatures />
      <Components />
    </ProfileComponent>
    <ProfileComponent Name="Rack2" Type="Upper Chassis" Count="1" Mapping="Rack">
      <ProfileProperties>
        <ProfileProperty Name="Description" PropertyType="string" Value="Front Chassis Location" Units="" />
        <ProfileProperty Name="Position" PropertyType="string" Value="FrontMiddle" Units="" />
        <ProfileProperty Name="Orientation" PropertyType="string" Value="Vertical" Units="" />
        <ProfileProperty Name="PartType01" PropertyType="string" Value="Chassis" Units="" />
        <ProfileProperty Name="PartNumber01" PropertyType="string" Value="8111" Units="" />
        <ProfileProperty Name="PartQty01" PropertyType="string" Value="1" Units="" />
        <ProfileProperty Name="Length" PropertyType="integer" Value="600" Units="mm" />
      </ProfileProperties>
      <ProfileSignatures />
      <Components />
    </ProfileComponent>
    <ProfileComponent Name="Rack3" Type="Upper Chassis" Count="1" Mapping="Rack">
      <ProfileProperties>
        <ProfileProperty Name="Description" PropertyType="string" Value="Front Chassis Location" Units="" />
        <ProfileProperty Name="Position" PropertyType="string" Value="FrontLower" Units="" />
        <ProfileProperty Name="Orientation" PropertyType="string" Value="Vertical" Units="" />
        <ProfileProperty Name="PartType01" PropertyType="string" Value="RXMChassis" Units="" />
        <ProfileProperty Name="PartNumber01" PropertyType="string" Value="8112" Units="" />
        <ProfileProperty Name="PartQty01" PropertyType="string" Value="1" Units="" />
        <ProfileProperty Name="Length" PropertyType="integer" Value="600" Units="mm" />
      </ProfileProperties>
      <ProfileSignatures />
      <Components />
    </ProfileComponent>
  </Components>
</ProfileComponent>