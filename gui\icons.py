"""
苹果风格的图标资源
提供简单几何图标
"""

from PySide6.QtGui import QIcon, QPixmap, QPainter, QColor, QPen, QBrush, QPolygon
from PySide6.QtCore import QSize, QPoint, QByteArray
from PySide6.QtSvg import QSvgRenderer
from .styles import APPLE_COLORS


def create_icon_from_svg(svg_content: str, size: QSize = QSize(24, 24)) -> QIcon:
    """
    从SVG内容创建QIcon

    Args:
        svg_content: SVG内容字符串
        size: 图标尺寸

    Returns:
        QIcon对象
    """
    # 创建SVG渲染器
    svg_renderer = QSvgRenderer()
    svg_data = QByteArray(svg_content.encode('utf-8'))

    if not svg_renderer.load(svg_data):
        # 如果SVG加载失败，返回空图标
        return QIcon()

    # 创建像素图
    pixmap = QPixmap(size)
    pixmap.fill(QColor(0, 0, 0, 0))  # 透明背景

    # 渲染SVG到像素图
    painter = QPainter(pixmap)
    svg_renderer.render(painter)
    painter.end()

    return QIcon(pixmap)


def create_simple_icon(icon_type: str, size: QSize = QSize(24, 24), color: str = None) -> QIcon:
    """
    创建简单的几何图标

    Args:
        icon_type: 图标类型
        size: 图标尺寸
        color: 图标颜色

    Returns:
        QIcon对象
    """
    if color is None:
        color = APPLE_COLORS['primary']

    # 创建像素图
    pixmap = QPixmap(size)
    pixmap.fill(QColor(0, 0, 0, 0))  # 透明背景

    # 创建画笔
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.Antialiasing)

    pen = QPen(QColor(color))
    pen.setWidth(2)
    painter.setPen(pen)

    brush = QBrush(QColor(color))
    painter.setBrush(brush)

    # 根据类型绘制不同的图标
    center_x, center_y = size.width() // 2, size.height() // 2

    if icon_type == 'play':
        # 播放三角形
        points = [
            QPoint(center_x - 6, center_y - 8),
            QPoint(center_x - 6, center_y + 8),
            QPoint(center_x + 8, center_y)
        ]
        polygon = QPolygon(points)
        painter.drawPolygon(polygon)

    elif icon_type == 'stop':
        # 停止方块
        painter.drawRect(center_x - 6, center_y - 6, 12, 12)

    elif icon_type == 'download':
        # 下载箭头
        painter.drawLine(center_x, center_y - 8, center_x, center_y + 4)
        painter.drawLine(center_x - 4, center_y, center_x, center_y + 4)
        painter.drawLine(center_x + 4, center_y, center_x, center_y + 4)
        painter.drawLine(center_x - 8, center_y + 6, center_x + 8, center_y + 6)

    elif icon_type == 'folder':
        # 文件夹
        painter.setBrush(QBrush())  # 无填充
        painter.drawRect(center_x - 8, center_y - 4, 16, 10)
        painter.drawRect(center_x - 8, center_y - 6, 6, 2)

    elif icon_type == 'check':
        # 对勾
        painter.drawLine(center_x - 4, center_y, center_x - 1, center_y + 3)
        painter.drawLine(center_x - 1, center_y + 3, center_x + 6, center_y - 4)

    elif icon_type == 'settings':
        # 设置齿轮
        painter.drawEllipse(center_x - 3, center_y - 3, 6, 6)
        for i in range(8):
            angle = i * 45
            x1 = center_x + 6 * (1 if i % 2 == 0 else 0.7)
            y1 = center_y
            painter.drawLine(center_x, center_y, x1, y1)

    elif icon_type == 'info':
        # 信息圆圈
        painter.setBrush(QBrush())  # 无填充
        painter.drawEllipse(center_x - 8, center_y - 8, 16, 16)
        painter.drawLine(center_x, center_y - 2, center_x, center_y + 4)
        painter.drawPoint(center_x, center_y - 5)

    elif icon_type == 'clear':
        # 垃圾桶
        painter.setBrush(QBrush())  # 无填充
        painter.drawRect(center_x - 6, center_y - 2, 12, 8)
        painter.drawLine(center_x - 8, center_y - 4, center_x + 8, center_y - 4)
        painter.drawLine(center_x - 2, center_y - 6, center_x + 2, center_y - 6)

    painter.end()
    return QIcon(pixmap)


def get_play_icon() -> QIcon:
    """获取播放图标（开始分配）"""
    svg_content = f"""
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 5V19L19 12L8 5Z" fill="{APPLE_COLORS['success']}" stroke="{APPLE_COLORS['success']}" stroke-width="2" stroke-linejoin="round"/>
    </svg>
    """
    return create_icon_from_svg(svg_content)


def get_stop_icon() -> QIcon:
    """获取停止图标"""
    svg_content = f"""
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="6" y="6" width="12" height="12" fill="{APPLE_COLORS['error']}" stroke="{APPLE_COLORS['error']}" stroke-width="2" rx="2"/>
    </svg>
    """
    return create_icon_from_svg(svg_content)


def get_download_icon() -> QIcon:
    """获取下载图标（导出）"""
    svg_content = f"""
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="{APPLE_COLORS['primary']}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <polyline points="7,10 12,15 17,10" stroke="{APPLE_COLORS['primary']}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <line x1="12" y1="15" x2="12" y2="3" stroke="{APPLE_COLORS['primary']}" stroke-width="2" stroke-linecap="round"/>
    </svg>
    """
    return create_icon_from_svg(svg_content)


def get_folder_icon() -> QIcon:
    """获取文件夹图标（加载文件）"""
    svg_content = f"""
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M22 19C22 19.5304 21.7893 20.0391 21.4142 20.4142C21.0391 20.7893 20.5304 21 20 21H4C3.46957 21 2.96086 20.7893 2.58579 20.4142C2.21071 20.0391 2 19.5304 2 19V5C2 4.46957 2.21071 3.96086 2.58579 3.58579C2.96086 3.21071 3.46957 3 4 3H9L11 6H20C20.5304 6 21.0391 6.21071 21.4142 6.58579C21.7893 6.96086 22 7.46957 22 8V19Z" stroke="{APPLE_COLORS['warning']}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="{APPLE_COLORS['warning']}20"/>
    </svg>
    """
    return create_icon_from_svg(svg_content)


def get_check_icon() -> QIcon:
    """获取检查图标（验证）"""
    svg_content = f"""
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M20 6L9 17L4 12" stroke="{APPLE_COLORS['success']}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
    """
    return create_icon_from_svg(svg_content)


def get_settings_icon() -> QIcon:
    """获取设置图标"""
    svg_content = f"""
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="12" cy="12" r="3" stroke="{APPLE_COLORS['text_secondary']}" stroke-width="2"/>
        <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="{APPLE_COLORS['text_secondary']}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
    """
    return create_icon_from_svg(svg_content)


def get_info_icon() -> QIcon:
    """获取信息图标"""
    svg_content = f"""
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="12" cy="12" r="10" stroke="{APPLE_COLORS['info']}" stroke-width="2"/>
        <path d="M12 16V12" stroke="{APPLE_COLORS['info']}" stroke-width="2" stroke-linecap="round"/>
        <path d="M12 8H12.01" stroke="{APPLE_COLORS['info']}" stroke-width="2" stroke-linecap="round"/>
    </svg>
    """
    return create_icon_from_svg(svg_content)


def get_clear_icon() -> QIcon:
    """获取清除图标"""
    svg_content = f"""
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <polyline points="3,6 5,6 21,6" stroke="{APPLE_COLORS['text_tertiary']}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6" stroke="{APPLE_COLORS['text_tertiary']}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
    """
    return create_icon_from_svg(svg_content)


# 图标映射字典
ICON_MAP = {
    'play': get_play_icon,
    'stop': get_stop_icon,
    'download': get_download_icon,
    'folder': get_folder_icon,
    'check': get_check_icon,
    'settings': get_settings_icon,
    'info': get_info_icon,
    'clear': get_clear_icon,
}


def get_icon(name: str) -> QIcon:
    """
    获取指定名称的图标
    
    Args:
        name: 图标名称
        
    Returns:
        QIcon对象，如果找不到则返回空图标
    """
    if name in ICON_MAP:
        return ICON_MAP[name]()
    else:
        return QIcon()  # 返回空图标
