"""
苹果风格的GUI样式表
参考苹果公司的设计语言，提供现代、简洁、优雅的界面样式
"""

# 苹果风格的颜色定义
APPLE_COLORS = {
    # 主色调 - 苹果蓝
    'primary': '#007AFF',
    'primary_hover': '#0056CC',
    'primary_pressed': '#004499',
    
    # 背景色
    'background': '#F2F2F7',
    'surface': '#FFFFFF',
    'surface_secondary': '#F9F9F9',
    'surface_tertiary': '#EFEFF4',
    
    # 文本色
    'text_primary': '#000000',
    'text_secondary': '#3C3C43',
    'text_tertiary': '#8E8E93',
    'text_quaternary': '#C7C7CC',
    
    # 系统色
    'success': '#34C759',
    'warning': '#FF9500',
    'error': '#FF3B30',
    'info': '#5AC8FA',
    
    # 分隔线和边框
    'separator': '#C6C6C8',
    'border': '#D1D1D6',
    
    # 阴影
    'shadow': 'rgba(0, 0, 0, 0.1)',
    'shadow_light': 'rgba(0, 0, 0, 0.05)',
}

def get_apple_style():
    """获取苹果风格的完整样式表"""
    return f"""
    /* 全局样式 */
    QWidget {{
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        font-size: 13px;
        color: {APPLE_COLORS['text_primary']};
        background-color: {APPLE_COLORS['background']};
    }}
    
    /* 主窗口 */
    QMainWindow {{
        background-color: {APPLE_COLORS['background']};
        border: none;
    }}
    
    /* 选项卡控件 */
    QTabWidget::pane {{
        border: 1px solid {APPLE_COLORS['border']};
        border-radius: 10px;
        background-color: {APPLE_COLORS['surface']};
        margin-top: 5px;
    }}
    
    QTabWidget::tab-bar {{
        alignment: center;
    }}
    
    QTabBar::tab {{
        background-color: {APPLE_COLORS['surface_tertiary']};
        color: {APPLE_COLORS['text_secondary']};
        padding: 12px 24px;
        margin-right: 2px;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        border: 1px solid {APPLE_COLORS['border']};
        border-bottom: none;
        font-weight: 500;
        min-width: 100px;
    }}
    
    QTabBar::tab:selected {{
        background-color: {APPLE_COLORS['surface']};
        color: {APPLE_COLORS['primary']};
        font-weight: 600;
        border-color: {APPLE_COLORS['border']};
    }}
    
    QTabBar::tab:hover:!selected {{
        background-color: {APPLE_COLORS['surface_secondary']};
        color: {APPLE_COLORS['text_primary']};
    }}
    
    /* 按钮样式 */
    QPushButton {{
        background-color: {APPLE_COLORS['primary']};
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        font-size: 13px;
        min-height: 20px;
    }}
    
    QPushButton:hover {{
        background-color: {APPLE_COLORS['primary_hover']};
    }}
    
    QPushButton:pressed {{
        background-color: {APPLE_COLORS['primary_pressed']};
    }}
    
    QPushButton:disabled {{
        background-color: {APPLE_COLORS['text_quaternary']};
        color: {APPLE_COLORS['text_tertiary']};
    }}
    
    /* 次要按钮 */
    QPushButton[class="secondary"] {{
        background-color: {APPLE_COLORS['surface_tertiary']};
        color: {APPLE_COLORS['text_primary']};
        border: 1px solid {APPLE_COLORS['border']};
    }}
    
    QPushButton[class="secondary"]:hover {{
        background-color: {APPLE_COLORS['surface_secondary']};
        border-color: {APPLE_COLORS['separator']};
    }}
    
    /* 危险按钮 */
    QPushButton[class="danger"] {{
        background-color: {APPLE_COLORS['error']};
        color: white;
    }}
    
    QPushButton[class="danger"]:hover {{
        background-color: #E6342A;
    }}
    
    /* 成功按钮 */
    QPushButton[class="success"] {{
        background-color: {APPLE_COLORS['success']};
        color: white;
    }}
    
    QPushButton[class="success"]:hover {{
        background-color: #2FB84D;
    }}
    
    /* 输入框 */
    QLineEdit {{
        background-color: {APPLE_COLORS['surface']};
        border: 1px solid {APPLE_COLORS['border']};
        border-radius: 8px;
        padding: 8px 12px;
        font-size: 13px;
        color: {APPLE_COLORS['text_primary']};
    }}
    
    QLineEdit:focus {{
        border-color: {APPLE_COLORS['primary']};
        outline: none;
    }}
    
    QLineEdit:disabled {{
        background-color: {APPLE_COLORS['surface_tertiary']};
        color: {APPLE_COLORS['text_tertiary']};
    }}
    
    /* 文本编辑器 */
    QTextEdit {{
        background-color: {APPLE_COLORS['surface']};
        border: 1px solid {APPLE_COLORS['border']};
        border-radius: 8px;
        padding: 12px;
        font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
        font-size: 12px;
        line-height: 1.4;
    }}
    
    /* 组框 */
    QGroupBox {{
        font-weight: 600;
        font-size: 14px;
        color: {APPLE_COLORS['text_primary']};
        border: 1px solid {APPLE_COLORS['border']};
        border-radius: 10px;
        margin-top: 10px;
        padding-top: 10px;
        background-color: {APPLE_COLORS['surface']};
    }}
    
    QGroupBox::title {{
        subcontrol-origin: margin;
        left: 15px;
        padding: 0 8px 0 8px;
        background-color: {APPLE_COLORS['surface']};
        color: {APPLE_COLORS['text_primary']};
    }}
    
    /* 进度条 */
    QProgressBar {{
        border: none;
        border-radius: 6px;
        background-color: {APPLE_COLORS['surface_tertiary']};
        text-align: center;
        font-weight: 500;
        height: 12px;
    }}
    
    QProgressBar::chunk {{
        background-color: {APPLE_COLORS['primary']};
        border-radius: 6px;
    }}
    
    /* 表格 */
    QTableWidget {{
        background-color: {APPLE_COLORS['surface']};
        border: 1px solid {APPLE_COLORS['border']};
        border-radius: 10px;
        gridline-color: {APPLE_COLORS['separator']};
        selection-background-color: {APPLE_COLORS['primary']};
        alternate-background-color: {APPLE_COLORS['surface_secondary']};
    }}
    
    QTableWidget::item {{
        padding: 8px;
        border: none;
    }}
    
    QTableWidget::item:selected {{
        background-color: {APPLE_COLORS['primary']};
        color: white;
    }}
    
    QHeaderView::section {{
        background-color: {APPLE_COLORS['surface_tertiary']};
        color: {APPLE_COLORS['text_secondary']};
        padding: 10px;
        border: none;
        border-bottom: 1px solid {APPLE_COLORS['separator']};
        font-weight: 600;
    }}
    
    /* 下拉框 */
    QComboBox {{
        background-color: {APPLE_COLORS['surface']};
        border: 1px solid {APPLE_COLORS['border']};
        border-radius: 8px;
        padding: 8px 12px;
        min-width: 100px;
    }}
    
    QComboBox:hover {{
        border-color: {APPLE_COLORS['primary']};
    }}
    
    QComboBox::drop-down {{
        border: none;
        width: 20px;
    }}
    
    QComboBox::down-arrow {{
        image: none;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid {APPLE_COLORS['text_tertiary']};
        margin-right: 5px;
    }}
    
    /* 复选框 */
    QCheckBox {{
        spacing: 8px;
        color: {APPLE_COLORS['text_primary']};
    }}
    
    QCheckBox::indicator {{
        width: 18px;
        height: 18px;
        border-radius: 4px;
        border: 1px solid {APPLE_COLORS['border']};
        background-color: {APPLE_COLORS['surface']};
    }}
    
    QCheckBox::indicator:checked {{
        background-color: {APPLE_COLORS['primary']};
        border-color: {APPLE_COLORS['primary']};
        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
    }}
    
    /* 滚动条 */
    QScrollBar:vertical {{
        background-color: transparent;
        width: 12px;
        border-radius: 6px;
    }}
    
    QScrollBar::handle:vertical {{
        background-color: {APPLE_COLORS['text_quaternary']};
        border-radius: 6px;
        min-height: 20px;
        margin: 2px;
    }}
    
    QScrollBar::handle:vertical:hover {{
        background-color: {APPLE_COLORS['text_tertiary']};
    }}
    
    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
        height: 0px;
    }}
    
    /* 状态栏 */
    QStatusBar {{
        background-color: {APPLE_COLORS['surface_tertiary']};
        border-top: 1px solid {APPLE_COLORS['separator']};
        color: {APPLE_COLORS['text_secondary']};
        font-size: 12px;
    }}
    
    /* 菜单栏 */
    QMenuBar {{
        background-color: {APPLE_COLORS['surface']};
        border-bottom: 1px solid {APPLE_COLORS['separator']};
        padding: 4px;
    }}
    
    QMenuBar::item {{
        background-color: transparent;
        padding: 6px 12px;
        border-radius: 6px;
        color: {APPLE_COLORS['text_primary']};
    }}
    
    QMenuBar::item:selected {{
        background-color: {APPLE_COLORS['primary']};
        color: white;
    }}
    
    /* 工具栏 */
    QToolBar {{
        background-color: {APPLE_COLORS['surface']};
        border: none;
        spacing: 8px;
        padding: 8px;
    }}
    """

def get_card_style():
    """获取卡片样式"""
    return f"""
    QWidget[class="card"] {{
        background-color: {APPLE_COLORS['surface']};
        border: 1px solid {APPLE_COLORS['border']};
        border-radius: 12px;
        padding: 16px;
    }}
    """

def get_title_style(size='large'):
    """获取标题样式"""
    sizes = {
        'large': '20px',
        'medium': '16px',
        'small': '14px'
    }
    return f"""
    QLabel[class="title-{size}"] {{
        font-size: {sizes.get(size, '16px')};
        font-weight: 600;
        color: {APPLE_COLORS['text_primary']};
        margin-bottom: 8px;
    }}
    """
