"""
Excel处理工具 - 简化版本
提供Excel文件读写功能
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import pandas as pd

from core.logger import get_logger


class ExcelReader:
    """Excel读取器"""
    
    def __init__(self):
        """初始化Excel读取器"""
        self.logger = get_logger(__name__)
    
    def read_excel_file(self, file_path: str, sheet_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        读取Excel文件
        
        Args:
            file_path: Excel文件路径
            sheet_names: 要读取的工作表名称列表，None表示读取所有
            
        Returns:
            Excel数据字典
        """
        self.logger.info(f"读取Excel文件: {file_path}")
        
        try:
            import pandas as pd
            
            if not Path(file_path).exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 使用pandas读取Excel文件
            if sheet_names is None:
                # 读取所有工作表
                data = pd.read_excel(file_path, sheet_name=None, engine='openpyxl')
            else:
                # 读取指定工作表
                data = pd.read_excel(file_path, sheet_name=sheet_names, engine='openpyxl')
            
            self.logger.info(f"成功读取Excel文件，包含工作表: {list(data.keys()) if isinstance(data, dict) else 'single sheet'}")
            return data

        except Exception as e:
            self.logger.error(f"读取Excel文件失败: {e}")
            raise

    def read_iodb_file(self, file_path: str) -> Dict[str, Any]:
        """
        读取IODB文件

        Args:
            file_path: IODB文件路径

        Returns:
            IODB数据
        """
        data = self.read_excel_file(file_path)

        # 处理Excel数据
        processed_data = {
            'io_points': [],
            'cables': {},
            'summary': {
                'total_points': 0,
                'total_cables': 0,
                'signal_types': {},
                'systems': set(),
                'locations': set()
            }
        }

        try:
            # 假设IODB数据在第一个工作表中
            if isinstance(data, dict):
                # 获取第一个工作表
                sheet_name = list(data.keys())[0]
                df = data[sheet_name]
            else:
                df = data

            self.logger.info(f"IODB工作表包含 {len(df)} 行数据")

            # 处理每一行数据
            for index, row in df.iterrows():
                try:
                    # 创建IOPoint对象的数据 - 使用实际的列名
                    io_point_data = {
                        'tag': str(row.get('tagname', '')).strip(),
                        'cable_name': str(row.get('cable name', '')).strip(),
                        'pair_number': int(row.get('pair number', 0)) if pd.notna(row.get('pair number')) else 0,
                        'signal_type': str(row.get('signaltype', 'AI')).strip(),
                        'system': str(row.get('system', '')).strip(),
                        'location': str(row.get('location', '')).strip(),
                        'is_intrinsic': str(row.get('is', 'NIS')).strip().upper() == 'IS',
                        'cable_type': str(row.get('cable type', '')).strip(),
                        'wiring_typical': str(row.get('wiring typical', '')).strip(),
                        'description': str(row.get('description', '')).strip()
                    }

                    # 跳过空行
                    if not io_point_data['tag'] or not io_point_data['cable_name']:
                        continue

                    processed_data['io_points'].append(io_point_data)

                    # 更新统计信息
                    signal_type = io_point_data['signal_type']
                    if signal_type in processed_data['summary']['signal_types']:
                        processed_data['summary']['signal_types'][signal_type] += 1
                    else:
                        processed_data['summary']['signal_types'][signal_type] = 1

                    processed_data['summary']['systems'].add(io_point_data['system'])
                    processed_data['summary']['locations'].add(io_point_data['location'])

                except Exception as e:
                    self.logger.warning(f"处理第 {index + 1} 行数据时出错: {e}")
                    continue

            # 按电缆名称分组
            cables_dict = {}
            for io_point_data in processed_data['io_points']:
                cable_name = io_point_data['cable_name']
                if cable_name not in cables_dict:
                    cables_dict[cable_name] = []
                cables_dict[cable_name].append(io_point_data)

            processed_data['cables'] = cables_dict

            # 更新总计
            processed_data['summary']['total_points'] = len(processed_data['io_points'])
            processed_data['summary']['total_cables'] = len(cables_dict)

            self.logger.info(f"成功处理 {processed_data['summary']['total_points']} 个I/O点，{processed_data['summary']['total_cables']} 条电缆")

        except Exception as e:
            self.logger.error(f"处理IODB数据时发生错误: {e}")
            raise

        return processed_data

    def read_pidb_file(self, file_path: str) -> Dict[str, Any]:
        """
        读取PIDB文件

        Args:
            file_path: PIDB文件路径

        Returns:
            PIDB数据
        """
        data = self.read_excel_file(file_path, ['chassis', 'cabinet'])

        # 简化的处理
        processed_data = {
            'chassis': [],
            'cabinets': [],
            'cabinet_mapping': {}
        }

        return processed_data


class ExcelWriter:
    """Excel写入器"""
    
    def __init__(self):
        """初始化Excel写入器"""
        self.logger = get_logger(__name__)
    
    def write_excel_file(self, data: Dict[str, Any], file_path: str) -> bool:
        """
        写入Excel文件
        
        Args:
            data: 要写入的数据
            file_path: 输出文件路径
            
        Returns:
            是否写入成功
        """
        self.logger.info(f"写入Excel文件: {file_path}")
        
        try:
            import pandas as pd
            
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                for sheet_name, sheet_data in data.items():
                    if hasattr(sheet_data, 'to_excel'):
                        sheet_data.to_excel(writer, sheet_name=sheet_name, index=False)
            
            return True
        except Exception as e:
            self.logger.error(f"写入Excel文件失败: {e}")
            return False
