杂项文件夹下的IO.xlsx是我需要的报表形式，保留原本的报表，将原本报表命名为分配结果，新报表命名为IO分配表，并在GUI中修改输出相关内容
其中cover是封面，IOlist是报表本体，按照原格式和打印设置输出报表
在GUI中新增对器件的自定义命名，包括安全栅/继电器/隔离器/防雷栅/端子排/第二路端子排，自定义命名允许用户通过以下内容进行组合：
固定的自定义文本、tag或tag所用器件对应的机架编号、槽位编号、通道号、机柜编号、导轨号、器件在导轨上的编号、机柜名、简化机柜名、电缆名、ETP名、ETP上/下卡、信号类型、tag名、卡件型号。
其中机架编号可能为1位或2位，示例：5 10.槽位编号为一位。通道号格式为00，示例：04.导轨号为对应的典型机柜下Rail_后的内容。机柜名示例1103-SIS-SYS-101，简化机柜名示例SYS-101.ETP上/下卡按照自定义后缀输出，默认为U或者L。信号类型示例AI，AO，DI，DO
cover中的相关信息从PIDB文件的project工作表读取。
IOlist的前6行是表头，前4行中项目名称 用户名称等信息从PIDB文件的project工作表读取。
从第7行开始填写以下内容：其中定义的器件命名规则为默认命名规则，当用户定义了自定义命名规则时使用自定义命名规则。
F列是该点的位号，即Tag名
G列为Tag的Description
A列是编号，从1开始对整个报表进行编号。
B列是信号类型，包括AI，AO，DI，DO，此处填写IO点的信号类型
C列填写卡件型号，可以从该点的典型回路中机架下的Slots下一层内容的PartNumber属性读到，如3721
D列填写ETP名，如R1S3U，机架号可能有两位，例如R10S5L
E列填写tag在卡件中的通道，为1-32的数字
H列到O列不需要填写
P列填写柜名
Q列填写安全栅/继电器/隔离器型号，在该tag所属的典型回路中寻找HardwareType为Barrier或Relay或Isolator的器件，填写其PartNumber属性。若典型回路中没有HardwareType为Barrier或Relay或Isolator的器件，则寻找ETP下层是否存在Slots，若存在则填写Slots下一层的PartNumber属性，若不存在则说明该tag不存在相关器件，单元格置空
R列填写防雷栅型号，在该tag所属的典型回路中寻找HardwareType为SurgeProtector的器件，填写其PartNumber属性，若不存在则置空，若除表头整列都为空则隐藏该列
S列填写ETP型号，在该tag所属的典型回路中寻找HardwareType为ETP的器件，填写其PartNumber属性
T列填写安全栅/继电器/隔离器编号，命名规则是器件前缀+机架编号+槽位编号+通道号。Barrier前缀为BA，Relay前缀为RY，Isolator前缀为ISL，机架和槽位号可从ETP名中提取，如R1S3U，机架号为1，槽位号为3，通道号格式为00.例如R1S3U的第5通道有隔离器，则命名为ISL1305.注意机架号可能为两位，这时编号部分为5位，例如BA10511，说明R10S5卡件的第11通道的tag有安全栅.若不存在则置空。
U列填写防雷栅编号，命名规则是器件前缀SP+机架编号+槽位编号+通道号。例如SP1505。若不存在则置空，若除表头整列都为空则隐藏该列
V列填写tag的典型回路名
W列填写端子排名，命名规则是前缀TB+机柜编号+导轨号+端子排在导轨上的编号。其中机柜编号从PIDB中的cabinet工作表读取机柜对应的CabinetNo，导轨号为对应的典型机柜下Rail_后的内容。例如TB02F03
X列填写端子号，由于一个点需要占用两片，这里需要连携第一片端子号-第二片端子号，例如1-2，13-14.
Y列填写TR端子排名，仅当对应的典型回路中MarshallingCabinet下存在TR部件时需要填写。命名规则为TR+ETP名，例如TRR1S3U。
Z列填写TR端子号，仅当对应的典型回路中MarshallingCabinet下存在TR部件时需要填写。由于一个点需要占用两片，这里需要连携第一片端子号-第二片端子号，例如1-2，13-14.
AA列填写电缆名，没有则置空
AB列填写pair number，没有则置空
AC AD列置空



ENHANCED
基于杂项文件夹下的IO.xlsx报表模板，创建一个新的IO分配表功能，具体要求如下：

## 报表管理要求
1. 保留现有报表功能，将其重命名为"分配结果"
2. 新增"IO分配表"功能，使用IO.xlsx作为模板
3. 在GUI界面中添加相应的输出选项和控制

## 报表结构要求
- **cover工作表**：作为封面，相关信息从PIDB文件的project工作表读取
- **IOlist工作表**：作为报表主体，按照原格式和打印设置输出

## IOlist工作表详细规范

### 表头结构（第1-6行）
- 前4行：项目名称、用户名称等信息从PIDB文件的project工作表读取
- 第5-6行：列标题行

### 数据填充规范（从第7行开始）
按以下列顺序填充数据：

**A列 - 编号**：从1开始的连续序号
**B列 - 信号类型**：AI/AO/DI/DO（从IO点的信号类型获取）
**C列 - 卡件型号**：从典型回路→机架→Slots→PartNumber属性读取（如：3721）
**D列 - ETP名**：格式如R1S3U或R10S5L（机架号可能1-2位）
**E列 - 通道号**：1-32的数字
**F列 - 位号**：Tag名
**G列 - 描述**：Tag的Description
**H-O列**：保持空白
**P列 - 柜名**：机柜名称
**Q列 - 安全栅/继电器/隔离器型号**：
- 优先从典型回路中查找HardwareType为Barrier/Relay/Isolator的器件的PartNumber
- 若无，则查找ETP下层Slots的PartNumber
- 若都无则置空
**R列 - 防雷栅型号**：从典型回路中查找HardwareType为SurgeProtector的器件的PartNumber，无则置空，整列空则隐藏
**S列 - ETP型号**：从典型回路中查找HardwareType为ETP的器件的PartNumber
**T列 - 安全栅/继电器/隔离器编号**：
- 命名规则：前缀+机架编号+槽位编号+通道号（2位格式）
- 前缀：Barrier=BA, Relay=RY, Isolator=ISL
- 示例：ISL1305（R1S3槽位第05通道），BA10511（R10S5槽位第11通道）
**U列 - 防雷栅编号**：前缀SP+机架编号+槽位编号+通道号，如SP1505，整列空则隐藏
**V列 - 典型回路名**：tag所属的典型回路名称
**W列 - 端子排名**：TB+机柜编号+导轨号+端子排编号，如TB02F03
- 机柜编号从PIDB的cabinet工作表的CabinetNo字段获取
- 导轨号为典型机柜下Rail_后的内容
**X列 - 端子号**：连续两片端子号格式，如1-2, 13-14
**Y列 - TR端子排名**：仅当典型回路MarshallingCabinet下存在TR部件时填写，格式TR+ETP名，如TRR1S3U
**Z列 - TR端子号**：仅当存在TR部件时填写，格式同X列
**AA列 - 电缆名**：有则填写，无则置空
**AB列 - pair number**：有则填写，无则置空
**AC-AD列**：保持空白

## GUI自定义命名功能要求
为以下器件类型添加自定义命名配置：
- 安全栅/继电器/隔离器/防雷栅/端子排/第二路端子排

### 可用命名元素
用户可组合以下元素创建自定义命名规则：
- 固定自定义文本
- tag名
- 机架编号（1-2位，如：5, 10）
- 槽位编号（1位）
- 通道号（2位格式，如：04）
- 机柜编号
- 导轨号（典型机柜Rail_后的内容）
- 器件在导轨上的编号
- 机柜名（如：1103-SIS-SYS-101）
- 简化机柜名（如：SYS-101）
- 电缆名
- ETP名
- ETP上/下卡（默认后缀U/L，可自定义）
- 信号类型（AI/AO/DI/DO）
- 卡件型号

### 数据源说明
- **cover信息**：从PIDB文件project工作表读取
- **IOlist前4行信息**：从PIDB文件project工作表读取
- **机柜编号**：从PIDB文件cabinet工作表的CabinetNo字段读取
- **默认命名规则**：当用户未定义自定义规则时使用系统默认规则
- **自定义命名规则**：当用户定义了自定义规则时优先使用自定义规则