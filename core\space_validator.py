"""
空间验证器
负责验证和分配物理空间约束
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from core.logger import get_logger
from core.data_models import IOPoint, SignalType


@dataclass
class SpaceRequirement:
    """空间需求数据类"""
    io_point: IOPoint
    required_width: float = 0.0
    required_height: float = 0.0
    component_type: str = ""
    typical_name: str = ""


@dataclass
class RailAllocation:
    """导轨分配数据类"""
    rail_name: str
    cabinet_name: str
    total_width: float = 0.0
    used_width: float = 0.0
    available_width: float = 0.0
    allocated_components: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.allocated_components is None:
            self.allocated_components = []
        self.available_width = self.total_width - self.used_width


@dataclass
class SpaceValidationResult:
    """空间验证结果"""
    success: bool = False
    rail_allocations: List[RailAllocation] = None
    failed_allocations: List[SpaceRequirement] = None
    warnings: List[str] = None
    errors: List[str] = None
    summary: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.rail_allocations is None:
            self.rail_allocations = []
        if self.failed_allocations is None:
            self.failed_allocations = []
        if self.warnings is None:
            self.warnings = []
        if self.errors is None:
            self.errors = []
        if self.summary is None:
            self.summary = {}


class SpaceValidator:
    """空间验证器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化空间验证器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = get_logger(__name__)
        
        # 默认组件尺寸配置
        self.default_component_sizes = {
            'CPM16-AI3700': {'width': 12.5, 'height': 100},
            'CPM16-AO3700': {'width': 12.5, 'height': 100},
            'CPM16-DI3700': {'width': 12.5, 'height': 100},
            'CPM16-DO3700': {'width': 12.5, 'height': 100},
            'ETP': {'width': 6.2, 'height': 100},
            'default': {'width': 10.0, 'height': 100}
        }
        
        # 默认导轨容量
        self.default_rail_width = 500.0  # mm
    
    def validate_and_allocate(self, io_points: List[IOPoint], 
                             cabinets: List[Dict[str, Any]], 
                             wiring_typicals: Dict[str, Any]) -> SpaceValidationResult:
        """
        验证和分配空间
        
        Args:
            io_points: I/O点列表
            cabinets: 机柜列表
            wiring_typicals: 典型回路字典
            
        Returns:
            空间验证结果
        """
        self.logger.info(f"开始空间验证和分配，I/O点数: {len(io_points)}, 机柜数: {len(cabinets)}")
        
        result = SpaceValidationResult()
        
        try:
            # 1. 初始化导轨分配
            rail_allocations = self._initialize_rail_allocations(cabinets)
            result.rail_allocations = rail_allocations
            
            # 2. 计算每个I/O点的空间需求
            space_requirements = self._calculate_space_requirements(io_points, wiring_typicals)
            
            # 3. 执行空间分配
            allocation_success = self._allocate_space_requirements(space_requirements, rail_allocations, result)
            
            # 4. 生成分配摘要
            result.summary = self._generate_allocation_summary(rail_allocations, space_requirements, result)
            
            result.success = allocation_success and len(result.errors) == 0
            
            if result.success:
                self.logger.info("空间验证和分配成功完成")
            else:
                self.logger.warning(f"空间验证和分配完成，但有 {len(result.errors)} 个错误")
            
            return result
            
        except Exception as e:
            self.logger.error(f"空间验证和分配异常: {e}")
            result.errors.append(f"空间验证异常: {e}")
            result.success = False
            return result
    
    def _initialize_rail_allocations(self, cabinets: List[Dict[str, Any]]) -> List[RailAllocation]:
        """
        初始化导轨分配
        
        Args:
            cabinets: 机柜列表
            
        Returns:
            导轨分配列表
        """
        rail_allocations = []
        
        for cabinet in cabinets:
            cabinet_name = cabinet.get('name', '')
            rails = cabinet.get('rails', [])
            
            for rail in rails:
                rail_name = rail.get('name', '')
                rail_width = rail.get('width', self.default_rail_width)
                
                allocation = RailAllocation(
                    rail_name=rail_name,
                    cabinet_name=cabinet_name,
                    total_width=rail_width,
                    used_width=0.0
                )
                rail_allocations.append(allocation)
                
                self.logger.debug(f"初始化导轨分配: {cabinet_name}.{rail_name}, 总宽度: {rail_width}mm")
        
        return rail_allocations
    
    def _calculate_space_requirements(self, io_points: List[IOPoint], 
                                    wiring_typicals: Dict[str, Any]) -> List[SpaceRequirement]:
        """
        计算空间需求
        
        Args:
            io_points: I/O点列表
            wiring_typicals: 典型回路字典
            
        Returns:
            空间需求列表
        """
        space_requirements = []
        
        for io_point in io_points:
            # 根据信号类型确定组件类型
            component_type = self._determine_component_type(io_point)
            
            # 获取组件尺寸
            component_size = self.default_component_sizes.get(
                component_type, 
                self.default_component_sizes['default']
            )
            
            requirement = SpaceRequirement(
                io_point=io_point,
                required_width=component_size['width'],
                required_height=component_size['height'],
                component_type=component_type,
                typical_name=""  # 可以从典型回路中获取
            )
            
            space_requirements.append(requirement)
            self.logger.debug(f"计算空间需求: {io_point.tag} -> {component_type}, 宽度: {requirement.required_width}mm")
        
        return space_requirements
    
    def _determine_component_type(self, io_point: IOPoint) -> str:
        """
        确定组件类型
        
        Args:
            io_point: I/O点对象
            
        Returns:
            组件类型字符串
        """
        signal_type = io_point.signal_type
        
        # 根据信号类型映射到组件类型
        type_mapping = {
            SignalType.AI: 'CPM16-AI3700',
            SignalType.AO: 'CPM16-AO3700',
            SignalType.DI: 'CPM16-DI3700',
            SignalType.DO: 'CPM16-DO3700'
        }
        
        return type_mapping.get(signal_type, 'CPM16-DI3700')
    
    def _allocate_space_requirements(self, space_requirements: List[SpaceRequirement], 
                                   rail_allocations: List[RailAllocation], 
                                   result: SpaceValidationResult) -> bool:
        """
        分配空间需求
        
        Args:
            space_requirements: 空间需求列表
            rail_allocations: 导轨分配列表
            result: 验证结果对象
            
        Returns:
            分配是否成功
        """
        allocation_success = True
        
        for requirement in space_requirements:
            allocated = False
            
            # 尝试在每个导轨上分配
            for rail_allocation in rail_allocations:
                if rail_allocation.available_width >= requirement.required_width:
                    # 分配成功
                    rail_allocation.used_width += requirement.required_width
                    rail_allocation.available_width = rail_allocation.total_width - rail_allocation.used_width
                    
                    # 记录分配信息
                    allocation_info = {
                        'io_point': requirement.io_point,
                        'component_type': requirement.component_type,
                        'width': requirement.required_width,
                        'position': rail_allocation.used_width - requirement.required_width
                    }
                    rail_allocation.allocated_components.append(allocation_info)
                    
                    # 更新I/O点的分配信息
                    requirement.io_point.cabinet = rail_allocation.cabinet_name
                    requirement.io_point.rack = rail_allocation.rail_name
                    
                    allocated = True
                    self.logger.debug(f"成功分配: {requirement.io_point.tag} -> {rail_allocation.cabinet_name}.{rail_allocation.rail_name}")
                    break
            
            if not allocated:
                # 分配失败
                result.failed_allocations.append(requirement)
                error_msg = f"无法为I/O点 {requirement.io_point.tag} 分配空间，需要宽度: {requirement.required_width}mm"
                result.errors.append(error_msg)
                allocation_success = False
                self.logger.error(error_msg)
        
        return allocation_success
    
    def _generate_allocation_summary(self, rail_allocations: List[RailAllocation], 
                                   space_requirements: List[SpaceRequirement], 
                                   result: SpaceValidationResult) -> Dict[str, Any]:
        """
        生成分配摘要
        
        Args:
            rail_allocations: 导轨分配列表
            space_requirements: 空间需求列表
            result: 验证结果对象
            
        Returns:
            分配摘要字典
        """
        total_requirements = len(space_requirements)
        successful_allocations = total_requirements - len(result.failed_allocations)
        
        # 计算导轨利用率
        rail_utilization = []
        for rail in rail_allocations:
            utilization = (rail.used_width / rail.total_width * 100) if rail.total_width > 0 else 0
            rail_utilization.append({
                'cabinet': rail.cabinet_name,
                'rail': rail.rail_name,
                'utilization': utilization,
                'used_width': rail.used_width,
                'total_width': rail.total_width
            })
        
        summary = {
            'total_requirements': total_requirements,
            'successful_allocations': successful_allocations,
            'failed_allocations': len(result.failed_allocations),
            'success_rate': (successful_allocations / total_requirements * 100) if total_requirements > 0 else 0,
            'rail_utilization': rail_utilization,
            'total_rails': len(rail_allocations)
        }
        
        return summary
