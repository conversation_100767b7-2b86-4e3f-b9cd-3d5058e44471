"""
XML编辑器组件
集成现有的XML编辑器功能到主程序中
"""

import logging
import sys
import os
from typing import Dict, Any
from pathlib import Path

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QMessageBox, QSplitter
)
from PySide6.QtCore import Qt

from core.logger import get_logger


class XMLEditorWidget(QWidget):
    """XML编辑器组件"""
    
    def __init__(self, config: Dict[str, Any], parent=None):
        """
        初始化XML编辑器组件
        
        Args:
            config: 应用程序配置
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.config = config
        self.logger = get_logger(__name__)
        self.xml_editor_window = None
        
        # 初始化UI
        self._setup_ui()
        self._setup_xml_editor()
        
        self.logger.info("XML编辑器组件初始化完成")
    
    def _setup_ui(self):
        """设置用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题和说明
        title_label = QLabel("XML配置文件编辑器")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        main_layout.addWidget(title_label)
        
        desc_label = QLabel(
            "此模块集成了现有的XML编辑器功能，用于编辑机柜配置文件和典型回路文件。\n"
            "支持拖放操作、属性编辑、组件管理等功能。"
        )
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #666; margin-bottom: 20px;")
        main_layout.addWidget(desc_label)
        
        # 操作按钮区域
        button_layout = QHBoxLayout()
        
        self.open_cabinet_btn = QPushButton("打开机柜配置文件")
        self.open_cabinet_btn.setMinimumHeight(40)
        button_layout.addWidget(self.open_cabinet_btn)
        
        self.open_wiring_btn = QPushButton("打开典型回路文件")
        self.open_wiring_btn.setMinimumHeight(40)
        button_layout.addWidget(self.open_wiring_btn)
        
        self.launch_editor_btn = QPushButton("启动独立编辑器")
        self.launch_editor_btn.setMinimumHeight(40)
        button_layout.addWidget(self.launch_editor_btn)
        
        main_layout.addLayout(button_layout)
        
        # XML编辑器容器（预留）
        self.editor_container = QWidget()
        self.editor_container.setMinimumHeight(400)
        self.editor_container.setStyleSheet(
            "QWidget { border: 2px dashed #ccc; background-color: #f9f9f9; }"
        )
        
        container_layout = QVBoxLayout(self.editor_container)
        placeholder_label = QLabel("XML编辑器将在此处显示")
        placeholder_label.setAlignment(Qt.AlignCenter)
        placeholder_label.setStyleSheet("color: #999; font-size: 14px;")
        container_layout.addWidget(placeholder_label)
        
        main_layout.addWidget(self.editor_container)
        
        # 连接信号
        self.open_cabinet_btn.clicked.connect(self._open_cabinet_files)
        self.open_wiring_btn.clicked.connect(self._open_wiring_files)
        self.launch_editor_btn.clicked.connect(self._launch_independent_editor)
    
    def _setup_xml_editor(self):
        """设置XML编辑器"""
        try:
            # 尝试导入现有的XML编辑器模块
            project_root = Path(__file__).parent.parent
            xmleditor_path = project_root / "xmleditor"
            
            if xmleditor_path.exists():
                # 添加xmleditor路径到sys.path
                if str(xmleditor_path) not in sys.path:
                    sys.path.insert(0, str(xmleditor_path))
                
                self.logger.info("XML编辑器模块路径已添加")
            else:
                self.logger.warning("XML编辑器模块路径不存在")
                
        except Exception as e:
            self.logger.error(f"设置XML编辑器失败: {e}")
    
    def _open_cabinet_files(self):
        """打开机柜配置文件"""
        self.logger.info("打开机柜配置文件")
        
        try:
            cabinet_path = self.config.get('data_paths.cabinet_profiles', '')
            if not cabinet_path or not os.path.exists(cabinet_path):
                QMessageBox.warning(
                    self, "路径错误", 
                    f"机柜配置文件路径不存在: {cabinet_path}\n请在配置中设置正确的路径。"
                )
                return
            
            # TODO: 实现打开机柜配置文件的逻辑
            # 这里应该调用XML编辑器来加载机柜配置文件
            self._launch_xml_editor_with_files(cabinet_path, "CabinetProfile")
            
        except Exception as e:
            self.logger.error(f"打开机柜配置文件失败: {e}")
            QMessageBox.critical(self, "错误", f"打开机柜配置文件失败:\n{str(e)}")
    
    def _open_wiring_files(self):
        """打开典型回路文件"""
        self.logger.info("打开典型回路文件")
        
        try:
            wiring_path = self.config.get('data_paths.wiring_typical', '')
            if not wiring_path or not os.path.exists(wiring_path):
                QMessageBox.warning(
                    self, "路径错误", 
                    f"典型回路文件路径不存在: {wiring_path}\n请在配置中设置正确的路径。"
                )
                return
            
            # TODO: 实现打开典型回路文件的逻辑
            # 这里应该调用XML编辑器来加载典型回路文件
            self._launch_xml_editor_with_files(wiring_path, "WiringTypical")
            
        except Exception as e:
            self.logger.error(f"打开典型回路文件失败: {e}")
            QMessageBox.critical(self, "错误", f"打开典型回路文件失败:\n{str(e)}")
    
    def _launch_independent_editor(self):
        """启动独立的XML编辑器"""
        self.logger.info("启动独立XML编辑器")
        
        try:
            # 导入并启动现有的XML编辑器
            from xmleditor.main_editor import MainWindow as XMLMainWindow
            from PySide6.QtWidgets import QApplication
            
            # 如果已经有编辑器窗口打开，先关闭
            if self.xml_editor_window:
                self.xml_editor_window.close()
            
            # 创建新的编辑器窗口
            self.xml_editor_window = XMLMainWindow()
            self.xml_editor_window.show()
            
            self.logger.info("独立XML编辑器启动成功")
            
        except ImportError as e:
            self.logger.error(f"导入XML编辑器模块失败: {e}")
            QMessageBox.critical(
                self, "模块错误", 
                f"无法导入XML编辑器模块:\n{str(e)}\n\n请确保xmleditor模块存在且可访问。"
            )
        except Exception as e:
            self.logger.error(f"启动独立XML编辑器失败: {e}")
            QMessageBox.critical(self, "启动错误", f"启动独立XML编辑器失败:\n{str(e)}")
    
    def _launch_xml_editor_with_files(self, file_path: str, file_type: str):
        """启动XML编辑器并加载指定文件"""
        try:
            # 这是一个占位符实现
            # 实际实现应该调用XML编辑器并自动加载指定路径的文件
            
            QMessageBox.information(
                self, "功能开发中", 
                f"自动加载{file_type}文件的功能正在开发中。\n"
                f"文件路径: {file_path}\n\n"
                f"请使用'启动独立编辑器'按钮手动打开编辑器。"
            )
            
        except Exception as e:
            self.logger.error(f"启动XML编辑器失败: {e}")
            QMessageBox.critical(self, "错误", f"启动XML编辑器失败:\n{str(e)}")
    
    def embed_xml_editor(self):
        """嵌入XML编辑器到当前组件中"""
        """
        这个方法用于将现有的XML编辑器嵌入到当前组件中
        目前作为占位符，后续可以实现真正的嵌入功能
        """
        try:
            # TODO: 实现XML编辑器的嵌入功能
            # 这需要修改现有的XML编辑器代码，使其可以作为组件嵌入
            
            self.logger.info("嵌入XML编辑器功能正在开发中")
            
        except Exception as e:
            self.logger.error(f"嵌入XML编辑器失败: {e}")
    
    def closeEvent(self, event):
        """组件关闭事件"""
        if self.xml_editor_window:
            self.xml_editor_window.close()
        event.accept()
