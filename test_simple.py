#!/usr/bin/env python3
"""
简单测试
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_imports():
    """测试基本导入"""
    try:
        print("测试基本模块导入...")
        
        # 测试标准库
        import logging
        print("✓ logging")
        
        # 测试第三方库
        import pandas as pd
        print("✓ pandas")
        
        from PySide6.QtWidgets import QApplication
        print("✓ PySide6")
        
        # 测试项目模块
        from core.data_models import IOPoint
        print("✓ core.data_models")
        
        from core.logger import get_logger
        print("✓ core.logger")
        
        print("\n所有基本导入成功！")
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("EWReborn 简单测试")
    print("=" * 30)
    
    success = test_basic_imports()
    if success:
        print("\n测试通过！")
    else:
        print("\n测试失败！")
