我需要通过py制作一个自动化领域分配I/O点的软件，使用pyside6制作gui。已有的代码为xml查看和编辑功能，这部分的gui需要在后续融入到整个程序统一的gui。
输入资料有PIDB,IODB，典型机柜，典型回路。其中PIDB和IODB以excel形式存在；典型机柜和典型回路以xml形式存在
其中PIDB的chassis工作表反映了机架的信息及其所在的机柜，cabinet工作表反映了机柜名及其对应的编号，区域和所应用的典型机柜模板。PIDB如04B_PIDB文件夹中的PIDB.xlsx所示
IODB反映了IO点（即tag）所在的电缆及电缆信息，是本安还是非本安，信号类型，所在系统，所在区域，所属系统，及其所属的典型回路。IODB如04A_IODB文件夹中的IODB.xlsx所示
典型机柜如01B_CabinetProfile文件夹中PPG SYS和PPG BAR所示，机柜中有导轨和机架两种类型。典型回路如01C_WiringTypical文件夹所示。
典型的对应关系在IODB和PIDB中有所体现。
首先要验证IODB。
tag不可有重名。cable不可有重名pair number。pair number不可超过pair size。cable的is,size, system,location,signaltype,cabletype必须一致，否则报错。做好log信息和报错信息的输出。
第二部分是分配。
按照cable name及其pair number的升序顺序进行分配，将IO点按照典型回路中的部件分配入机柜中的机架和导轨，注意导轨长度的限制。
一个cable中的IO点必须分配在一个机柜中。
导轨中的IOtype属性必须与cable一致。
导轨的IOtype中，Mixed代表AI AO DI DO;Analog代表AI AO;Digital代表DI DO，例如Rail是Digital，则代表和DI DO的cable都匹配。
导轨中的Intrinsic（is）属性必须与cable一致。
导轨的Intrinsic（is）中，Mixed代表IS NIS，例如Rail是Mixed，则代表和IS NIS的cable都匹配。
parttype属性匹配是可选项，在gui中创建勾选框。
parttype代表导轨可插入的器件类型，可能有多种，由后缀区分，partnum代表该器件的编号，若勾选则必须与典型回路中器件的Partnumber对应。
插入时应当首先插入IsLandedComponent属性为true的器件。
倒数第二个插入ETP，倒数第一个插入卡件。卡件和ETP的通道对应关系由Connectors定义。
如果cable中有tag分配失败，整个cable都要rollback重新进行分配
有任何需要与我确认或补充的信息，请提出来。

Size 列：请明确 "Size" 具体指电缆的什么参数？是线对数量（Pair Size 已有），还是例如导线截面积或其他规格？
指的就是Pair Size
System vs 所属系统：这两个字段有何区别？在电缆属性一致性校验时，应使用哪一个，或者两者都需要？
System 就是 所属系统
Cable IOType 的确定：一个 "Cable Name" 的 IOType (Analog, Digital, Mixed) 是如何确定的？是根据其下所有 Tag 的 Signal Type 聚合而来吗？例如，如果一个电缆下既有 AI 又有 DI 点，其 IOType 是 "Mixed" 吗？还是说电缆本身在 IODB 中有直接的 IOType 字段？（目前看描述，似乎是基于 Signal Type）
同一cable中的所有tag的Signal Type都应当是一样的，这一点应当已经在第一步的验证中完成。
关于 PIDB.xlsx - component.csv:

此文件的具体用途是什么？它是否提供了XML文件中没有的，且对分配逻辑至关重要的组件信息或映射关系？
这个文件是后续制作BOM（部件清单）功能所需要的，若分配功能不需要则无视
关于典型机柜和典型回路XML:

组件尺寸: 为了匹配导轨长度限制，典型回路XML中的每个组件 (如FTB, Barrier, Card, ETP) 是否有明确的 Length 或 Width 属性来表示其在导轨上占用的空间？如果没有，如何计算？
这一点在xml中已经存在，每个部件都有对应的Length 或 Width 属性。且这两个属性数值一致，可以使用任意一个
Rail PartTypeXX 与 Loop Component PartNumber 匹配:
描述中提到 "parttype代表导轨可插入的器件类型，可能有多种，由后缀区分"。PartType01, PartType02 等是否就是这些多种类型？后缀具体是如何在 PartTypeXX 字段中体现的？
确实以PartType01, PartType02的形式体现。并且PartNum01代表PartType01的型号, PartType02
当 PartType 匹配勾选框被选中时，是要求典型回路中组件的 PartNumber 与导轨的某一个 PartNumberXX (如PartNumber01) 严格相等，并且该组件的类型 (如何定义组件类型？基于HardwareType?) 与对应的 PartTypeXX (如PartType01) 匹配吗？
典型回路的HardwareType属性必须要与典型机柜的Parttype（如PartType01）对应，这一点是必须项；典型回路的Partnumber属性要与典型机柜的Partnum（如PartNum01）对应，这一点是可选项，由gui勾选框控制。
Racks 和 Chassis: I/O卡件 (Card) 通常安装在Chassis（底板）中，而Chassis安装在Rack（机架）中。典型回路XML中定义的"Card"是如何与典型机柜XML中的 "Rack" 或安装在Rack上的 "Chassis" 关联起来的？
典型回路中的chassis按照”典型回路的HardwareType属性必须要与典型机柜的Parttype（如PartType01）对应 “的规则插入典型机柜中的Rack，一个chassis对应一个rack。
card按照SlotsApplicable属性分配入机架的slot中，MAIN机架3-7slot可用，EXP机架1-8slot可用,RXM机架2-7slot可用
分配逻辑是先确定机柜，再确定导轨放端子/隔离栅，再确定机架/底板放卡件吗？
是的，这里要注意机柜分为系统柜SYS（只有机架），辅助接线柜MAR（只有导轨没有机架），和混合柜（既有导轨也有机架）。对于器件被放入辅助接线柜的情况，其对应的卡件和可用放入系统柜中，但如果器件被放入混合柜，其对应的卡件必须放入本柜的卡件。
ETP (Engineering Terminal Panel): ETP 是安装在导轨上还是机架/Chassis上？其尺寸如何考虑？
ETP安装在导轨上，其尺寸在典型回路xml中有定义，例如示例AI IS BABP中CPM16-AI3700就是ETP,其HardwareType为ETP,Height为285.
Connectors: Connectors 属性具体是如何定义卡件和ETP之间的通道对应关系的？它的格式是怎样的？
例如J2U_ConnectedChannels属性为1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16，则代表其对应1-16通道
J2L_ConnectedChannels属性为17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32，则代表其对应17-32通道。
这些信息在示例典型回路xml中都有体现

分配逻辑细节:

确定目标机柜: 如何从一个 Cable Name (及其包含的I/O点) 找到它应该被分配到的具体 Cabinet Name 和应用的 Typical Cabinet Template？是通过 IODB 中的 Location 字段关联到 PIDB - cabinet.csv 中的 Area，然后找到对应的 Cabinet Name 和 Typical Cabinet Template 吗？
Typical Cabinet Template决定了 Cabinet Name有哪些导轨或机架，及其长度。至于其他的area等信息，都只是作为将来获取机柜相关信息的存在，cable和cabinet的area必须一致。
导轨和机架的选择: 在一个选定的机柜内，如果有多个满足条件的导轨或机架，选择哪一个？是否有优先级或者特定顺序？
优先选择当前剩余长度更多的导轨，至于机架，按照rack在xml中的排列顺序依次分配进入。
导轨长度计算: "插入时应当首先插入IsLandedComponent属性为true的器件。倒数第二个插入ETP，倒数第一个插入卡件。" 这个顺序是针对一个完整典型回路的组件在单一导轨上的排列顺序吗？如果一个典型回路的组件需要分布在多个导轨上（虽然不常见），或者卡件在机架中，这个顺序如何应用？
典型回路中只有IsLandedComponent属性为true的器件，ETP和card是确定存在的，在中间可能有安全栅，继电器，隔离器等等器件，但这些器件只要在IsLandedComponent属性为true的器件之后，ETP之前进行分配即可。
一个Cable中的IO点必须分配在一个机柜中: 这点很明确。如果一个机柜内空间不足以容纳一个完整Cable的所有回路组件，是否报错？
当前机柜无法进行分配可以分配到warning层级，并开始轮询下一个符合条件的机柜进行分配，如果所有机柜都不满足条件或失败，则报错为error。报错信息必须包含cable的名称以及tag名。
IOType 细化: 如果一个Rail的 IOType 是 "Mixed"，而一个Cable下所有点的 Signal Type 都是 "AI"，这是否匹配？（按描述应该是匹配的）。
是匹配的
Intrinsic 细化: 如果一个Rail的 Intrinsic 是 "Mixed"，而一个Cable的 IS 属性是 "IS"，这是否匹配？（按描述应该是匹配的）。
是匹配的


日志和报错:

需要输出哪些具体的log信息？（例如，每个分配步骤的决策，成功分配的记录等）
按照你的决策来
报错信息的格式和详细程度有何要求？
按照之前的描述，分为warning和error层级。需要包括重要的信息，比如cable，tag和正在分配的cabinet名称等等。使用户可用通过报错得知分配出错的原因即可，尽可能详细

请详细阅读xml文件，示例xml文件展开的信息如图，对照xml和图片确定读取xml的规则
在明确xml的属性后根据这些信息进一步向我确认不明确和需要补充的信息。

机架类型识别 (MAIN, EXP, EXM):

用户指定了不同类型机架 (MAIN, EXP, EXM) 的可用卡槽范围。在典型机柜XML (如 PPG SYS.xml) 中，一个 <ProfileComponent Name="Rack1" Type="Upper Chassis"...> 是如何被识别为 "MAIN", "EXP", 或 "EXM" 类型的？
是通过其 Name 属性 (例如，如果 Name 包含 "MAIN", "EXP", "EXM")？
是通过其 Type 属性 (例如，实际的 Type 值可能是 "MainRack", "ExpansionRack")？ (目前示例显示 "Upper Chassis", "Lower Chassis")
还是通过机柜XML中该Rack组件下的某个特定 ProfileProperty 来定义的 (例如 <ProfileProperty Name="RackFunction" Value="MAIN" />)？
MAIN对应MainProcessorChassis，EXP对应Chassis，RXM对应RXMChassis

卡件 SlotsApplicable 属性的精确含义:

在典型回路XML中，对于一个卡件 (e.g., CARD1)，其 SlotsApplicable 属性 (例如 Value="1" 或可能是 Value="3-4" 或 Value="1,5") 具体是如何规定其在Chassis中的插槽占用的？
如果 Value="1"，是指它占用1个槽位，分配时从机架允许的槽位范围 (如MAIN Rack的3-7槽) 中选择一个可用的单槽？
如果它需要多个槽位，该属性会如何表示 (例如 Value="2" 表示占用2个连续槽位)？
用户提到的 "MAIN机架3-7slot可用"，是指这些编号的槽位是单槽宽度的基本单位吗？
回复：SlotsApplicable的实际值示例1L,2L,3L,4L,5L,6L,7L,8L，这就是EXP的SlotsApplicable，代表卡件可以插入EXP机架的1到8槽


电缆 IOType 的确定 (再确认):

用户确认："同一cable中的所有tag的Signal Type都应当是一样的，这一点应当已经在第一步的验证中完成。"
因此，一个电缆的 IOType 将是 "Analog" (如果其TAG的 SignalType 是 AI 或 AO) 或 "Digital" (如果其TAG的 SignalType 是 DI 或 DO)。
导轨的 IOType="Mixed" 可以接受 "Analog" 或 "Digital" 的电缆。
导轨的 IOType="Analog" 只能接受 "Analog" 的电缆。
导轨的 IOType="Digital" 只能接受 "Digital" 的电缆。
这套逻辑现在比较清晰。
回复：实际上需要具体区分到AI AO DI DO，而不是仅仅区分Analog或Digital。例如导轨的IOType为AI 的情况，导轨仅仅可以插入AI的tag

导轨 Intrinsic 属性值 "IS, NIS" 的处理:

在 PPG BAR.xml 的 Rail_FTB 示例中，<ProfileProperty Name="Intrinsic" Value="IS, NIS" />。这是否等同于 Value="Mixed" 的含义，即该导轨可以同时容纳本安 ("IS") 和非本安 ("NIS") 的回路组件？(逻辑上似乎是这样)。
是的，IS,NIS代表IS和NIS都符合条件，实际上和Mixed起到了相同作用。

混合机柜中卡件分配的失败处理:

规则："如果器件被放入混合柜，其对应的卡件必须放入本柜的卡件。"
如果一个电缆的导轨组件成功分配到了一个“混合型”机柜的导轨上，但该“混合型”机柜内没有合适的机架空间来安放此电缆对应的卡件（例如，机架满了，或者没有符合 PartType 匹配的机架），那么：
是否意味着该电缆在这个“混合型”机柜中的整体分配尝试失败？
是的，你的理解很正确。

此时，是否应该回滚该电缆在这个“混合型”机柜中已分配的导轨组件，然后尝试将整个电缆（包括其导轨组件和机架组件）分配到另一个符合条件的机柜（可能是另一个混合柜，或一个MAR柜+一个SYS柜的组合）？（根据"如果cable中有tag分配失败，整个cable都要rollback重新进行分配"的补充规则，听起来是这样。）
是的，你的理解很正确。

是否还有其他问题或者缺失的信息需要我提供？

