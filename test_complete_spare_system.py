"""
完整的Spare点系统验证测试
验证所有spare点相关功能的完整性和正确性
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_data_models():
    """测试数据模型扩展"""
    print("1. 测试数据模型扩展...")
    
    from core.data_models import IOPoint, Cable, SignalType
    
    # 测试IOPoint的is_spare字段
    point = IOPoint(tag="TEST", is_spare=True)
    assert hasattr(point, 'is_spare'), "IOPoint缺少is_spare字段"
    assert point.is_spare == True, "is_spare字段设置失败"
    print("   ✅ IOPoint.is_spare字段正常")
    
    # 测试Cable的新方法
    cable = Cable(name="TEST", pair_size=5)
    cable.io_points = [
        IOPoint(pair_number=1),
        IOPoint(pair_number=3),
        IOPoint(pair_number=1)  # 重复
    ]
    
    used_pairs = cable.get_used_pairs()
    available_pairs = cable.get_available_pairs()
    
    assert sorted(used_pairs) == [1, 3], f"get_used_pairs错误: {used_pairs}"
    assert sorted(available_pairs) == [2, 4, 5], f"get_available_pairs错误: {available_pairs}"
    print("   ✅ Cable.get_used_pairs()和get_available_pairs()方法正常")


def test_spare_manager():
    """测试Spare点管理器"""
    print("\n2. 测试Spare点管理器...")
    
    from core.spare_manager import SparePointManager, SpareConfiguration, SpareStrategy
    from core.data_models import IOPoint, SignalType
    
    # 测试配置
    config = {
        'spare_settings': {
            'default_spare_limit': 2,
            'enable_cable_spare': True,
            'enable_etp_spare': True,
            'spare_naming_prefix': 'SPARE_',
            'spare_description': '-'
        }
    }
    
    manager = SparePointManager(config)
    assert manager.spare_config.default_spare_limit == 2, "配置加载失败"
    print("   ✅ SparePointManager初始化正常")
    
    # 测试spare点生成
    test_cables = [{
        'name': 'TEST_CABLE',
        'pair_size': 4,
        'io_points': [
            IOPoint(tag="TAG1", pair_number=1, wiring_typical="DI NIS N"),
            IOPoint(tag="TAG2", pair_number=2, wiring_typical="DI NIS N")
        ]
    }]
    
    result = manager.generate_spare_points(
        cables=test_cables,
        terminal_blocks=[],
        wiring_typicals={},
        cabinets=[],
        strategy=SpareStrategy.CABLE_FORM
    )
    
    assert result.success, f"Spare点生成失败: {result.errors}"
    assert len(result.spare_points) == 2, f"Spare点数量错误: {len(result.spare_points)}"
    
    for spare in result.spare_points:
        assert spare.is_spare, "Spare点is_spare标志错误"
        assert spare.tag.startswith('SPARE_'), f"Spare点命名错误: {spare.tag}"
        assert spare.description == '-', f"Spare点描述错误: {spare.description}"
    
    print("   ✅ Spare点生成功能正常")


def test_allocator_integration():
    """测试分配器集成"""
    print("\n3. 测试分配器集成...")
    
    from core.allocator import IOAllocator
    from core.data_models import IOPoint, SignalType
    from utils.config_manager_simple import ConfigManager
    
    # 加载配置
    config_manager = ConfigManager()
    config = config_manager.load_config()
    
    # 创建分配器
    allocator = IOAllocator(config)
    assert hasattr(allocator, 'spare_manager'), "分配器缺少spare_manager"
    print("   ✅ 分配器集成spare管理器正常")
    
    # 测试分配流程
    test_cables = [{
        'name': 'TEST_CABLE',
        'pair_size': 3,
        'io_points': [IOPoint(tag="TAG1", pair_number=1, wiring_typical="DI NIS N")]
    }]
    
    test_cabinets = [{
        'name': 'TEST_CABINET',
        'rails': [{'name': 'Rail1', 'width': 500.0}]
    }]
    
    result = allocator.allocate_io_points(
        cables=test_cables,
        cabinets=test_cabinets,
        wiring_typicals={}
    )
    
    assert result.success, f"分配失败: {result.errors}"
    
    # 检查是否生成了spare点
    spare_points = [p for p in result.allocated_points if hasattr(p, 'is_spare') and p.is_spare]
    assert len(spare_points) > 0, "未生成spare点"
    print(f"   ✅ 分配流程正常，生成了{len(spare_points)}个spare点")


def test_terminal_block_extension():
    """测试端子排扩展"""
    print("\n4. 测试端子排数据模型扩展...")
    
    from core.terminal_block_manager import TerminalBlock
    from core.data_models import IOPoint
    
    # 创建端子排
    tb = TerminalBlock(name="TB001", spare_limit=3)
    assert hasattr(tb, 'spare_limit'), "TerminalBlock缺少spare_limit字段"
    assert tb.spare_limit == 3, "spare_limit设置错误"
    print("   ✅ TerminalBlock.spare_limit字段正常")
    
    # 测试spare相关属性
    tb.io_points = [
        IOPoint(tag="TAG1", is_spare=False),
        IOPoint(tag="SPARE_1", is_spare=True),
        IOPoint(tag="SPARE_2", is_spare=True)
    ]
    tb.current_points = 3
    
    assert tb.spare_count == 2, f"spare_count计算错误: {tb.spare_count}"
    assert tb.needs_spare_points == True, f"needs_spare_points判断错误: {tb.needs_spare_points}"
    print("   ✅ TerminalBlock spare相关属性正常")


def test_report_builder():
    """测试报表构建器"""
    print("\n5. 测试报表构建器...")
    
    from core.io_report_builder import IOReportBuilder, ReportTemplate
    from core.data_models import IOPoint, SignalType
    
    template = ReportTemplate("test.xlsx", "测试模板")
    builder = IOReportBuilder(template)
    
    # 测试spare点格式化
    regular_point = IOPoint(tag="TAG001", description="Sensor", is_spare=False)
    spare_point = IOPoint(tag="SPARE_1", description="Spare", is_spare=True)
    
    assert builder._format_tag_for_display(regular_point) == "TAG001", "常规点tag格式化错误"
    assert builder._format_tag_for_display(spare_point) == "SPARE", "Spare点tag格式化错误"
    
    assert builder._format_description_for_display(regular_point) == "Sensor", "常规点描述格式化错误"
    assert builder._format_description_for_display(spare_point) == "-", "Spare点描述格式化错误"
    
    print("   ✅ 报表构建器spare点处理正常")


def test_configuration():
    """测试配置管理"""
    print("\n6. 测试配置管理...")
    
    from utils.config_manager_simple import ConfigManager
    
    config_manager = ConfigManager()
    config = config_manager.load_config()
    
    # 检查spare配置
    spare_settings = config.get('spare_settings', {})
    assert 'default_spare_limit' in spare_settings, "配置缺少default_spare_limit"
    assert 'enable_cable_spare' in spare_settings, "配置缺少enable_cable_spare"
    assert 'enable_etp_spare' in spare_settings, "配置缺少enable_etp_spare"
    assert 'spare_naming_prefix' in spare_settings, "配置缺少spare_naming_prefix"
    assert 'spare_description' in spare_settings, "配置缺少spare_description"
    assert 'etp_spare_limits' in spare_settings, "配置缺少etp_spare_limits"
    
    print("   ✅ 配置管理spare设置正常")


def test_gui_components():
    """测试GUI组件"""
    print("\n7. 测试GUI组件...")
    
    try:
        # 这里只是检查导入是否正常，不实际创建GUI
        from gui.config_widget import ConfigWidget
        print("   ✅ GUI配置组件导入正常")
        
        # 检查是否有spare相关的方法
        methods = dir(ConfigWidget)
        spare_methods = [m for m in methods if 'spare' in m.lower()]
        assert len(spare_methods) > 0, "GUI组件缺少spare相关方法"
        print(f"   ✅ GUI组件包含{len(spare_methods)}个spare相关方法")
        
    except ImportError as e:
        print(f"   ⚠️  GUI组件导入失败（可能缺少PySide6）: {e}")


def main():
    """主测试函数"""
    print("=" * 60)
    print("完整的Spare点系统验证测试")
    print("=" * 60)
    
    try:
        test_data_models()
        test_spare_manager()
        test_allocator_integration()
        test_terminal_block_extension()
        test_report_builder()
        test_configuration()
        test_gui_components()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！Spare点系统功能完整！")
        print("=" * 60)
        
        print("\n✅ 已实现的功能:")
        print("   • IOPoint数据模型扩展（is_spare字段）")
        print("   • Cable数据模型扩展（get_used_pairs/get_available_pairs方法）")
        print("   • SparePointManager核心管理器")
        print("   • CableSpareStrategy电缆策略")
        print("   • EtpSpareStrategy ETP策略")
        print("   • IOAllocator集成spare点生成")
        print("   • TerminalBlock扩展（spare_limit等字段）")
        print("   • IOReportBuilder报表处理")
        print("   • 配置管理spare设置")
        print("   • GUI配置界面")
        
        print("\n🔧 功能特性:")
        print("   • 自动检测电缆空线对并生成spare点")
        print("   • 根据ETP容量和配置生成spare点")
        print("   • 智能典型回路类型推断")
        print("   • 可配置的spare下限和命名规则")
        print("   • 报表中spare点特殊显示格式")
        print("   • 完整的错误处理和日志记录")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)
