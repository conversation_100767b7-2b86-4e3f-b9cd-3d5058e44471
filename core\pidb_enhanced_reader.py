"""
PIDB增强读取器
提供增强的PIDB数据读取和处理功能
"""

import logging
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import re

from core.logger import get_logger
from core.data_models import IOPoint, SignalType


@dataclass
class PidbColumn:
    """PIDB列定义"""
    name: str
    alias: List[str]
    data_type: str
    required: bool = False
    default_value: Any = None
    validation_pattern: str = ""
    description: str = ""


@dataclass
class PidbValidationResult:
    """PIDB验证结果"""
    success: bool = False
    errors: List[str] = None
    warnings: List[str] = None
    statistics: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []
        if self.statistics is None:
            self.statistics = {}


@dataclass
class PidbProcessingResult:
    """PIDB处理结果"""
    success: bool = False
    io_points: List[IOPoint] = None
    cables: Dict[str, Any] = None
    statistics: Dict[str, Any] = None
    errors: List[str] = None
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.io_points is None:
            self.io_points = []
        if self.cables is None:
            self.cables = {}
        if self.statistics is None:
            self.statistics = {}
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []


class PidbColumnMapper:
    """PIDB列映射器"""
    
    def __init__(self):
        """初始化列映射器"""
        self.logger = get_logger(__name__)
        self.column_definitions = self._create_column_definitions()
    
    def _create_column_definitions(self) -> Dict[str, PidbColumn]:
        """创建列定义"""
        definitions = {}
        
        # Tag名称列
        tag_col = PidbColumn(
            name="tag",
            alias=["tagname", "tag_name", "point_name", "instrument_tag"],
            data_type="string",
            required=True,
            validation_pattern=r"^[A-Z0-9_-]+$",
            description="I/O点标签名称"
        )
        definitions["tag"] = tag_col
        
        # 描述列
        desc_col = PidbColumn(
            name="description",
            alias=["desc", "description", "instrument_description", "point_description"],
            data_type="string",
            required=False,
            default_value="",
            description="I/O点描述"
        )
        definitions["description"] = desc_col
        
        # 信号类型列
        signal_type_col = PidbColumn(
            name="signal_type",
            alias=["signal_type", "signaltype", "io_type", "point_type"],
            data_type="string",
            required=True,
            validation_pattern=r"^(AI|AO|DI|DO)$",
            description="信号类型"
        )
        definitions["signal_type"] = signal_type_col
        
        # 系统列
        system_col = PidbColumn(
            name="system",
            alias=["system", "subsystem", "plant_area", "unit"],
            data_type="string",
            required=False,
            default_value="",
            description="所属系统"
        )
        definitions["system"] = system_col
        
        # 位置列
        location_col = PidbColumn(
            name="location",
            alias=["location", "area", "plant_location", "physical_location"],
            data_type="string",
            required=False,
            default_value="",
            description="物理位置"
        )
        definitions["location"] = location_col
        
        # 本安类型列
        intrinsic_col = PidbColumn(
            name="is_intrinsic",
            alias=["is", "intrinsic", "is_intrinsic", "safety_type"],
            data_type="string",
            required=False,
            default_value="NIS",
            validation_pattern=r"^(IS|NIS)$",
            description="本安类型"
        )
        definitions["is_intrinsic"] = intrinsic_col
        
        # 电缆名称列
        cable_name_col = PidbColumn(
            name="cable_name",
            alias=["cable_name", "cable", "cable_tag", "cable_number"],
            data_type="string",
            required=False,
            default_value="",
            description="电缆名称"
        )
        definitions["cable_name"] = cable_name_col
        
        # 线对号列
        pair_number_col = PidbColumn(
            name="pair_number",
            alias=["pair_number", "pair", "wire_pair", "core_number"],
            data_type="integer",
            required=False,
            default_value=1,
            validation_pattern=r"^\d+$",
            description="线对号"
        )
        definitions["pair_number"] = pair_number_col
        
        # 电缆类型列
        cable_type_col = PidbColumn(
            name="cable_type",
            alias=["cable_type", "cable_specification", "cable_spec"],
            data_type="string",
            required=False,
            default_value="",
            description="电缆类型"
        )
        definitions["cable_type"] = cable_type_col
        
        # 典型回路列
        wiring_typical_col = PidbColumn(
            name="wiring_typical",
            alias=["wiring_typical", "typical_circuit", "circuit_type", "wiring_type"],
            data_type="string",
            required=False,
            default_value="",
            description="典型回路"
        )
        definitions["wiring_typical"] = wiring_typical_col
        
        return definitions
    
    def map_columns(self, df_columns: List[str]) -> Dict[str, str]:
        """
        映射DataFrame列名到标准列名
        
        Args:
            df_columns: DataFrame的列名列表
            
        Returns:
            映射字典 {原列名: 标准列名}
        """
        mapping = {}
        df_columns_lower = [col.lower().strip() for col in df_columns]
        
        for standard_name, column_def in self.column_definitions.items():
            # 检查精确匹配
            if standard_name in df_columns_lower:
                original_index = df_columns_lower.index(standard_name)
                mapping[df_columns[original_index]] = standard_name
                continue
            
            # 检查别名匹配
            for alias in column_def.alias:
                alias_lower = alias.lower().strip()
                if alias_lower in df_columns_lower:
                    original_index = df_columns_lower.index(alias_lower)
                    mapping[df_columns[original_index]] = standard_name
                    break
        
        self.logger.info(f"映射了 {len(mapping)} 个列: {mapping}")
        return mapping
    
    def get_required_columns(self) -> List[str]:
        """
        获取必需的列名
        
        Returns:
            必需列名列表
        """
        return [name for name, col_def in self.column_definitions.items() if col_def.required]


class PidbDataValidator:
    """PIDB数据验证器"""
    
    def __init__(self, column_mapper: PidbColumnMapper):
        """
        初始化数据验证器
        
        Args:
            column_mapper: 列映射器
        """
        self.column_mapper = column_mapper
        self.logger = get_logger(__name__)
    
    def validate_dataframe(self, df: pd.DataFrame, column_mapping: Dict[str, str]) -> PidbValidationResult:
        """
        验证DataFrame数据
        
        Args:
            df: 数据框
            column_mapping: 列映射
            
        Returns:
            验证结果
        """
        result = PidbValidationResult()
        
        try:
            # 检查必需列
            required_columns = self.column_mapper.get_required_columns()
            mapped_columns = set(column_mapping.values())
            
            missing_required = set(required_columns) - mapped_columns
            if missing_required:
                result.errors.extend([f"缺少必需列: {col}" for col in missing_required])
            
            # 数据质量检查
            self._validate_data_quality(df, column_mapping, result)
            
            # 生成统计信息
            result.statistics = self._generate_statistics(df, column_mapping)
            
            result.success = len(result.errors) == 0
            
        except Exception as e:
            result.errors.append(f"验证过程异常: {e}")
            self.logger.error(f"PIDB验证失败: {e}")
        
        return result
    
    def _validate_data_quality(self, df: pd.DataFrame, column_mapping: Dict[str, str], 
                              result: PidbValidationResult):
        """
        验证数据质量
        
        Args:
            df: 数据框
            column_mapping: 列映射
            result: 验证结果
        """
        reverse_mapping = {v: k for k, v in column_mapping.items()}
        
        for standard_name, column_def in self.column_mapper.column_definitions.items():
            if standard_name not in reverse_mapping:
                continue
            
            original_col = reverse_mapping[standard_name]
            if original_col not in df.columns:
                continue
            
            series = df[original_col]
            
            # 检查空值
            null_count = series.isnull().sum()
            if null_count > 0:
                if column_def.required:
                    result.errors.append(f"必需列 '{standard_name}' 存在 {null_count} 个空值")
                else:
                    result.warnings.append(f"列 '{standard_name}' 存在 {null_count} 个空值")
            
            # 数据类型检查
            if column_def.data_type == "integer":
                non_numeric = series.dropna().apply(lambda x: not str(x).isdigit()).sum()
                if non_numeric > 0:
                    result.warnings.append(f"列 '{standard_name}' 存在 {non_numeric} 个非数字值")
            
            # 正则表达式验证
            if column_def.validation_pattern:
                pattern = re.compile(column_def.validation_pattern)
                invalid_values = series.dropna().apply(
                    lambda x: not pattern.match(str(x)) if pd.notna(x) else False
                ).sum()
                if invalid_values > 0:
                    result.warnings.append(
                        f"列 '{standard_name}' 存在 {invalid_values} 个格式不符合规则的值"
                    )
    
    def _generate_statistics(self, df: pd.DataFrame, column_mapping: Dict[str, str]) -> Dict[str, Any]:
        """
        生成统计信息
        
        Args:
            df: 数据框
            column_mapping: 列映射
            
        Returns:
            统计信息字典
        """
        stats = {
            'total_rows': len(df),
            'total_columns': len(df.columns),
            'mapped_columns': len(column_mapping),
            'column_statistics': {}
        }
        
        reverse_mapping = {v: k for k, v in column_mapping.items()}
        
        for standard_name in self.column_mapper.column_definitions.keys():
            if standard_name in reverse_mapping:
                original_col = reverse_mapping[standard_name]
                if original_col in df.columns:
                    series = df[original_col]
                    stats['column_statistics'][standard_name] = {
                        'null_count': series.isnull().sum(),
                        'unique_count': series.nunique(),
                        'data_type': str(series.dtype)
                    }
        
        return stats


class PidbEnhancedReader:
    """PIDB增强读取器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化PIDB增强读取器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = get_logger(__name__)
        
        # 初始化组件
        self.column_mapper = PidbColumnMapper()
        self.validator = PidbDataValidator(self.column_mapper)
        
        self.logger.info("PIDB增强读取器初始化完成")
    
    def read_pidb_file(self, file_path: str) -> PidbProcessingResult:
        """
        读取PIDB文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            处理结果
        """
        result = PidbProcessingResult()
        
        try:
            self.logger.info(f"开始读取PIDB文件: {file_path}")
            
            # 检查文件是否存在
            if not Path(file_path).exists():
                result.errors.append(f"文件不存在: {file_path}")
                return result
            
            # 读取Excel文件
            df = pd.read_excel(file_path, sheet_name=0)  # 读取第一个工作表
            self.logger.info(f"读取到 {len(df)} 行数据，{len(df.columns)} 列")
            
            # 映射列名
            column_mapping = self.column_mapper.map_columns(df.columns.tolist())
            
            # 验证数据
            validation_result = self.validator.validate_dataframe(df, column_mapping)
            result.errors.extend(validation_result.errors)
            result.warnings.extend(validation_result.warnings)
            
            if not validation_result.success:
                self.logger.error("PIDB数据验证失败")
                return result
            
            # 处理数据
            processing_result = self._process_dataframe(df, column_mapping)
            result.io_points = processing_result.io_points
            result.cables = processing_result.cables
            result.statistics.update(processing_result.statistics)
            result.errors.extend(processing_result.errors)
            result.warnings.extend(processing_result.warnings)
            
            result.success = len(result.errors) == 0
            
            if result.success:
                self.logger.info(f"PIDB文件处理成功，生成 {len(result.io_points)} 个I/O点")
            else:
                self.logger.error(f"PIDB文件处理失败，错误数: {len(result.errors)}")
            
        except Exception as e:
            result.errors.append(f"读取PIDB文件异常: {e}")
            self.logger.error(f"读取PIDB文件异常: {e}")
        
        return result
    
    def _process_dataframe(self, df: pd.DataFrame, column_mapping: Dict[str, str]) -> PidbProcessingResult:
        """
        处理DataFrame数据
        
        Args:
            df: 数据框
            column_mapping: 列映射
            
        Returns:
            处理结果
        """
        result = PidbProcessingResult()
        reverse_mapping = {v: k for k, v in column_mapping.items()}
        
        try:
            io_points = []
            cables = {}
            
            for index, row in df.iterrows():
                try:
                    # 创建IOPoint对象
                    io_point = self._create_io_point_from_row(row, reverse_mapping)
                    io_points.append(io_point)
                    
                    # 收集电缆信息
                    if io_point.cable_name:
                        if io_point.cable_name not in cables:
                            cables[io_point.cable_name] = {
                                'name': io_point.cable_name,
                                'io_points': [],
                                'cable_type': io_point.cable_type,
                                'system': io_point.system,
                                'location': io_point.location
                            }
                        cables[io_point.cable_name]['io_points'].append(io_point)
                
                except Exception as e:
                    result.warnings.append(f"处理第 {index + 1} 行数据失败: {e}")
                    continue
            
            result.io_points = io_points
            result.cables = cables
            result.success = True
            
            # 生成统计信息
            result.statistics = {
                'total_io_points': len(io_points),
                'total_cables': len(cables),
                'signal_type_distribution': self._get_signal_type_distribution(io_points),
                'system_distribution': self._get_system_distribution(io_points)
            }
            
        except Exception as e:
            result.errors.append(f"处理DataFrame异常: {e}")
            self.logger.error(f"处理DataFrame异常: {e}")
        
        return result
    
    def _create_io_point_from_row(self, row: pd.Series, reverse_mapping: Dict[str, str]) -> IOPoint:
        """
        从行数据创建IOPoint对象
        
        Args:
            row: 行数据
            reverse_mapping: 反向列映射
            
        Returns:
            IOPoint对象
        """
        # 获取列定义的默认值
        def get_value(standard_name: str, default_value: Any = ""):
            if standard_name in reverse_mapping:
                original_col = reverse_mapping[standard_name]
                value = row.get(original_col, default_value)
                return value if pd.notna(value) else default_value
            return default_value
        
        # 创建IOPoint
        io_point = IOPoint(
            tag=str(get_value('tag', '')),
            description=str(get_value('description', '')),
            signal_type=SignalType(get_value('signal_type', 'DI')),
            system=str(get_value('system', '')),
            location=str(get_value('location', '')),
            is_intrinsic=str(get_value('is_intrinsic', 'NIS')).upper() == 'IS',
            cable_name=str(get_value('cable_name', '')),
            pair_number=int(get_value('pair_number', 1)) if str(get_value('pair_number', 1)).isdigit() else 1,
            cable_type=str(get_value('cable_type', '')),
            wiring_typical=str(get_value('wiring_typical', ''))
        )
        
        return io_point
    
    def _get_signal_type_distribution(self, io_points: List[IOPoint]) -> Dict[str, int]:
        """
        获取信号类型分布
        
        Args:
            io_points: I/O点列表
            
        Returns:
            信号类型分布字典
        """
        distribution = {}
        for point in io_points:
            signal_type = point.signal_type.value if hasattr(point.signal_type, 'value') else str(point.signal_type)
            distribution[signal_type] = distribution.get(signal_type, 0) + 1
        return distribution
    
    def _get_system_distribution(self, io_points: List[IOPoint]) -> Dict[str, int]:
        """
        获取系统分布
        
        Args:
            io_points: I/O点列表
            
        Returns:
            系统分布字典
        """
        distribution = {}
        for point in io_points:
            system = point.system or 'Unknown'
            distribution[system] = distribution.get(system, 0) + 1
        return distribution
