"""
灵活的命名配置对话框
支持用户自由组合各种命名元素
"""

from typing import Dict, Any, List
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QPushButton, QGroupBox, QTextEdit,
    QMessageBox, QTabWidget, QWidget, QListWidget, QListWidgetItem,
    QComboBox, QSpinBox, QCheckBox, QSplitter
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from core.logger import get_logger


class NamingElement:
    """命名元素类"""

    def __init__(self, element_type: str, display_name: str, description: str = ""):
        self.element_type = element_type
        self.display_name = display_name
        self.description = description
        self.custom_text = ""  # 用于固定文本元素
        self.format_spec = ""  # 用于格式化（如通道号的02d）
        self.custom_suffix = ""  # 用于ETP上/下卡自定义后缀


class FlexibleNamingDialog(QDialog):
    """灵活的命名配置对话框"""

    # 信号：配置已更新
    config_updated = Signal(dict)

    def __init__(self, parent=None, current_config: Dict[str, Any] = None):
        """
        初始化灵活命名配置对话框

        Args:
            parent: 父窗口
            current_config: 当前配置
        """
        super().__init__(parent)
        self.logger = get_logger(__name__)
        self.current_config = current_config or {}

        self.setWindowTitle("自定义命名规则配置")
        self.setModal(True)
        self.resize(900, 700)

        # 器件类型列表
        self.device_types = [
            ('barrier', '安全栅'),
            ('relay', '继电器'),
            ('isolator', '隔离器'),
            ('surge_protector', '防雷栅'),
            ('terminal_block', '端子排'),
            ('tr_terminal', 'TR端子排')
        ]

        # 可用命名元素
        self.available_elements = self._create_available_elements()

        # 存储每个器件类型的命名规则
        self.device_rules = {}

        self._setup_ui()
        self._load_config()

    def _create_available_elements(self) -> List[NamingElement]:
        """创建可用的命名元素列表"""
        elements = [
            NamingElement("fixed_text", "固定文本", "用户自定义的固定文本"),
            NamingElement("tag_name", "Tag名", "IO点的Tag名称"),
            NamingElement("rack_number", "机架编号", "机架编号（1-2位，如：5, 10）"),
            NamingElement("slot_number", "槽位编号", "槽位编号（1位）"),
            NamingElement("channel_number", "通道号", "通道号（2位格式，如：04）"),
            NamingElement("cabinet_number", "机柜编号", "机柜编号"),
            NamingElement("rail_number", "导轨号", "典型机柜Rail_后的内容"),
            NamingElement("device_number", "器件编号", "器件在导轨上的编号"),
            NamingElement("cabinet_name", "机柜名", "完整机柜名（如：1103-SIS-SYS-101）"),
            NamingElement("cabinet_name_short", "简化机柜名", "简化机柜名（如：SYS-101）"),
            NamingElement("cable_name", "电缆名", "电缆名称"),
            NamingElement("etp_name", "ETP名", "ETP名称"),
            NamingElement("etp_suffix", "ETP上/下卡", "ETP上/下卡后缀（默认U/L，可自定义）"),
            NamingElement("signal_type", "信号类型", "信号类型（AI/AO/DI/DO）"),
            NamingElement("card_part_number", "卡件型号", "卡件型号")
        ]
        return elements

    def _get_default_prefix(self, device_type: str) -> str:
        """获取器件类型的默认前缀"""
        default_prefixes = {
            'barrier': 'BA',
            'relay': 'RY',
            'isolator': 'ISL',
            'surge_protector': 'SP',
            'terminal_block': 'TB',
            'tr_terminal': 'TR'
        }
        return default_prefixes.get(device_type, 'DEV')

    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)

        # 说明标签
        info_label = QLabel("自定义器件命名规则 - 拖拽元素到右侧构建命名规则")
        info_label.setFont(QFont("", 10, QFont.Bold))
        layout.addWidget(info_label)

        # 创建标签页
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)

        # 为每种器件类型创建配置页面
        self.config_widgets = {}
        for device_type, display_name in self.device_types:
            tab = self._create_flexible_device_tab(device_type, display_name)
            tab_widget.addTab(tab, display_name)

        # 按钮区域
        button_layout = QHBoxLayout()

        preview_btn = QPushButton("预览所有规则")
        preview_btn.clicked.connect(self._preview_all_rules)
        button_layout.addWidget(preview_btn)

        reset_btn = QPushButton("重置为默认")
        reset_btn.clicked.connect(self._reset_to_default)
        button_layout.addWidget(reset_btn)

        button_layout.addStretch()

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self._save_config)
        button_layout.addWidget(ok_btn)

        layout.addLayout(button_layout)

    def _create_flexible_device_tab(self, device_type: str, display_name: str) -> QWidget:
        """
        创建灵活的器件配置标签页

        Args:
            device_type: 器件类型
            display_name: 显示名称

        Returns:
            配置标签页
        """
        tab = QWidget()
        layout = QHBoxLayout(tab)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)

        # 左侧：可用元素列表
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        left_label = QLabel("可用命名元素")
        left_label.setFont(QFont("", 9, QFont.Bold))
        left_layout.addWidget(left_label)

        available_list = QListWidget()
        available_list.setMaximumWidth(200)

        # 添加可用元素
        for element in self.available_elements:
            item = QListWidgetItem(f"{element.display_name}")
            item.setData(Qt.UserRole, element)
            item.setToolTip(element.description)
            available_list.addItem(item)

        left_layout.addWidget(available_list)

        # 添加元素按钮
        add_btn = QPushButton("添加 →")
        add_btn.clicked.connect(lambda: self._add_element_to_rule(device_type))
        left_layout.addWidget(add_btn)

        splitter.addWidget(left_widget)

        # 右侧：当前命名规则
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        right_label = QLabel(f"{display_name}命名规则")
        right_label.setFont(QFont("", 9, QFont.Bold))
        right_layout.addWidget(right_label)

        # 当前规则列表
        rule_list = QListWidget()
        rule_list.setMaximumHeight(150)
        right_layout.addWidget(rule_list)

        # 元素编辑区域
        edit_group = QGroupBox("元素编辑")
        edit_layout = QFormLayout(edit_group)

        # 固定文本编辑框
        fixed_text_edit = QLineEdit()
        fixed_text_edit.setPlaceholderText("输入固定文本内容")
        fixed_text_edit.setEnabled(False)
        edit_layout.addRow("固定文本:", fixed_text_edit)

        # ETP后缀选择
        etp_suffix_combo = QComboBox()
        etp_suffix_combo.addItems(["U (上卡)", "L (下卡)", "自定义..."])
        etp_suffix_combo.setEnabled(False)
        edit_layout.addRow("ETP后缀:", etp_suffix_combo)

        # 自定义ETP后缀
        custom_etp_edit = QLineEdit()
        custom_etp_edit.setPlaceholderText("自定义ETP后缀")
        custom_etp_edit.setEnabled(False)
        edit_layout.addRow("自定义后缀:", custom_etp_edit)

        # 更新按钮
        update_btn = QPushButton("更新元素")
        update_btn.setEnabled(False)
        update_btn.clicked.connect(lambda: self._update_selected_element(device_type))
        edit_layout.addRow("", update_btn)

        right_layout.addWidget(edit_group)

        # 规则操作按钮
        rule_btn_layout = QHBoxLayout()

        up_btn = QPushButton("上移")
        up_btn.clicked.connect(lambda: self._move_element_up(device_type))
        rule_btn_layout.addWidget(up_btn)

        down_btn = QPushButton("下移")
        down_btn.clicked.connect(lambda: self._move_element_down(device_type))
        rule_btn_layout.addWidget(down_btn)

        remove_btn = QPushButton("移除")
        remove_btn.clicked.connect(lambda: self._remove_element_from_rule(device_type))
        rule_btn_layout.addWidget(remove_btn)

        right_layout.addLayout(rule_btn_layout)

        # 预览区域
        preview_group = QGroupBox("命名预览")
        preview_layout = QVBoxLayout(preview_group)

        preview_text = QTextEdit()
        preview_text.setMaximumHeight(80)
        preview_text.setReadOnly(True)
        preview_layout.addWidget(preview_text)

        right_layout.addWidget(preview_group)

        splitter.addWidget(right_widget)

        # 设置分割器比例
        splitter.setSizes([250, 450])

        # 保存控件引用
        self.config_widgets[device_type] = {
            'available_list': available_list,
            'rule_list': rule_list,
            'preview': preview_text,
            'add_btn': add_btn,
            'up_btn': up_btn,
            'down_btn': down_btn,
            'remove_btn': remove_btn,
            'fixed_text_edit': fixed_text_edit,
            'etp_suffix_combo': etp_suffix_combo,
            'custom_etp_edit': custom_etp_edit,
            'update_btn': update_btn
        }

        # 连接规则列表选择事件
        rule_list.itemSelectionChanged.connect(lambda: self._on_rule_selection_changed(device_type))

        # 连接ETP后缀选择事件
        etp_suffix_combo.currentTextChanged.connect(lambda text: self._on_etp_suffix_changed(device_type, text))

        # 初始化设备规则
        self.device_rules[device_type] = []

        return tab

    def _add_element_to_rule(self, device_type: str):
        """添加元素到命名规则"""
        try:
            widgets = self.config_widgets[device_type]
            available_list = widgets['available_list']
            rule_list = widgets['rule_list']

            current_item = available_list.currentItem()
            if not current_item:
                QMessageBox.information(self, "提示", "请先选择一个命名元素")
                return

            element = current_item.data(Qt.UserRole)

            # 创建元素副本
            new_element = NamingElement(element.element_type, element.display_name, element.description)

            # 设置默认值
            if element.element_type == "fixed_text":
                # 根据器件类型设置默认前缀
                default_prefix = self._get_default_prefix(device_type)
                new_element.custom_text = default_prefix
                new_element.display_name = f"固定文本: {new_element.custom_text}"
            elif element.element_type == "etp_suffix":
                new_element.custom_suffix = "U"  # 默认上卡
                new_element.display_name = f"ETP后缀: {new_element.custom_suffix}"

            # 添加到规则列表
            self.device_rules[device_type].append(new_element)

            # 更新UI
            self._update_rule_list(device_type)
            self._update_preview(device_type)

        except Exception as e:
            self.logger.error(f"添加元素失败: {e}")
            QMessageBox.critical(self, "错误", f"添加元素失败: {e}")

    def _remove_element_from_rule(self, device_type: str):
        """从命名规则中移除元素"""
        try:
            widgets = self.config_widgets[device_type]
            rule_list = widgets['rule_list']

            current_row = rule_list.currentRow()
            if current_row < 0:
                QMessageBox.information(self, "提示", "请先选择要移除的元素")
                return

            # 从规则中移除
            del self.device_rules[device_type][current_row]

            # 更新UI
            self._update_rule_list(device_type)
            self._update_preview(device_type)

        except Exception as e:
            self.logger.error(f"移除元素失败: {e}")
            QMessageBox.critical(self, "错误", f"移除元素失败: {e}")

    def _move_element_up(self, device_type: str):
        """上移元素"""
        try:
            widgets = self.config_widgets[device_type]
            rule_list = widgets['rule_list']

            current_row = rule_list.currentRow()
            if current_row <= 0:
                return

            # 交换元素位置
            rules = self.device_rules[device_type]
            rules[current_row], rules[current_row - 1] = rules[current_row - 1], rules[current_row]

            # 更新UI
            self._update_rule_list(device_type)
            rule_list.setCurrentRow(current_row - 1)
            self._update_preview(device_type)

        except Exception as e:
            self.logger.error(f"上移元素失败: {e}")

    def _move_element_down(self, device_type: str):
        """下移元素"""
        try:
            widgets = self.config_widgets[device_type]
            rule_list = widgets['rule_list']

            current_row = rule_list.currentRow()
            rules = self.device_rules[device_type]

            if current_row < 0 or current_row >= len(rules) - 1:
                return

            # 交换元素位置
            rules[current_row], rules[current_row + 1] = rules[current_row + 1], rules[current_row]

            # 更新UI
            self._update_rule_list(device_type)
            rule_list.setCurrentRow(current_row + 1)
            self._update_preview(device_type)

        except Exception as e:
            self.logger.error(f"下移元素失败: {e}")

    def _update_rule_list(self, device_type: str):
        """更新规则列表显示"""
        try:
            widgets = self.config_widgets[device_type]
            rule_list = widgets['rule_list']

            rule_list.clear()

            for element in self.device_rules[device_type]:
                item = QListWidgetItem(element.display_name)
                item.setData(Qt.UserRole, element)
                rule_list.addItem(item)

        except Exception as e:
            self.logger.error(f"更新规则列表失败: {e}")

    def _on_rule_selection_changed(self, device_type: str):
        """规则选择变化事件"""
        try:
            widgets = self.config_widgets[device_type]
            rule_list = widgets['rule_list']
            fixed_text_edit = widgets['fixed_text_edit']
            etp_suffix_combo = widgets['etp_suffix_combo']
            custom_etp_edit = widgets['custom_etp_edit']
            update_btn = widgets['update_btn']

            current_row = rule_list.currentRow()

            if current_row >= 0 and current_row < len(self.device_rules[device_type]):
                element = self.device_rules[device_type][current_row]

                # 根据元素类型启用相应的编辑控件
                if element.element_type == "fixed_text":
                    fixed_text_edit.setEnabled(True)
                    fixed_text_edit.setText(element.custom_text)
                    etp_suffix_combo.setEnabled(False)
                    custom_etp_edit.setEnabled(False)
                    update_btn.setEnabled(True)

                elif element.element_type == "etp_suffix":
                    fixed_text_edit.setEnabled(False)
                    fixed_text_edit.clear()
                    etp_suffix_combo.setEnabled(True)

                    # 设置当前选择
                    if element.custom_suffix == "U":
                        etp_suffix_combo.setCurrentText("U (上卡)")
                        custom_etp_edit.setEnabled(False)
                    elif element.custom_suffix == "L":
                        etp_suffix_combo.setCurrentText("L (下卡)")
                        custom_etp_edit.setEnabled(False)
                    else:
                        etp_suffix_combo.setCurrentText("自定义...")
                        custom_etp_edit.setEnabled(True)
                        custom_etp_edit.setText(element.custom_suffix)

                    update_btn.setEnabled(True)

                else:
                    # 其他元素类型不可编辑
                    fixed_text_edit.setEnabled(False)
                    fixed_text_edit.clear()
                    etp_suffix_combo.setEnabled(False)
                    custom_etp_edit.setEnabled(False)
                    update_btn.setEnabled(False)
            else:
                # 没有选择任何元素
                fixed_text_edit.setEnabled(False)
                fixed_text_edit.clear()
                etp_suffix_combo.setEnabled(False)
                custom_etp_edit.setEnabled(False)
                update_btn.setEnabled(False)

        except Exception as e:
            self.logger.error(f"处理规则选择变化失败: {e}")

    def _on_etp_suffix_changed(self, device_type: str, text: str):
        """ETP后缀选择变化事件"""
        try:
            widgets = self.config_widgets[device_type]
            custom_etp_edit = widgets['custom_etp_edit']

            if text == "自定义...":
                custom_etp_edit.setEnabled(True)
                custom_etp_edit.setFocus()
            else:
                custom_etp_edit.setEnabled(False)
                custom_etp_edit.clear()

        except Exception as e:
            self.logger.error(f"处理ETP后缀变化失败: {e}")

    def _update_selected_element(self, device_type: str):
        """更新选中的元素"""
        try:
            widgets = self.config_widgets[device_type]
            rule_list = widgets['rule_list']
            fixed_text_edit = widgets['fixed_text_edit']
            etp_suffix_combo = widgets['etp_suffix_combo']
            custom_etp_edit = widgets['custom_etp_edit']

            current_row = rule_list.currentRow()
            if current_row < 0 or current_row >= len(self.device_rules[device_type]):
                return

            element = self.device_rules[device_type][current_row]

            if element.element_type == "fixed_text":
                new_text = fixed_text_edit.text().strip()
                if new_text:
                    element.custom_text = new_text
                    element.display_name = f"固定文本: {new_text}"
                else:
                    QMessageBox.warning(self, "警告", "固定文本不能为空")
                    return

            elif element.element_type == "etp_suffix":
                current_selection = etp_suffix_combo.currentText()
                if current_selection == "U (上卡)":
                    element.custom_suffix = "U"
                elif current_selection == "L (下卡)":
                    element.custom_suffix = "L"
                elif current_selection == "自定义...":
                    custom_suffix = custom_etp_edit.text().strip()
                    if custom_suffix:
                        element.custom_suffix = custom_suffix
                    else:
                        QMessageBox.warning(self, "警告", "自定义ETP后缀不能为空")
                        return

                element.display_name = f"ETP后缀: {element.custom_suffix}"

            # 更新UI
            self._update_rule_list(device_type)
            rule_list.setCurrentRow(current_row)  # 保持选择
            self._update_preview(device_type)

            QMessageBox.information(self, "成功", "元素更新成功")

        except Exception as e:
            self.logger.error(f"更新元素失败: {e}")
            QMessageBox.critical(self, "错误", f"更新元素失败: {e}")

    def _get_fixed_text_input(self) -> tuple:
        """获取固定文本输入"""
        from PySide6.QtWidgets import QInputDialog

        text, ok = QInputDialog.getText(
            self, "固定文本", "请输入固定文本内容:",
            QLineEdit.Normal, ""
        )
        return text, ok

    def _get_etp_suffix_input(self) -> tuple:
        """获取ETP后缀输入"""
        from PySide6.QtWidgets import QInputDialog

        items = ["U (上卡)", "L (下卡)", "自定义..."]
        item, ok = QInputDialog.getItem(
            self, "ETP后缀", "请选择ETP上/下卡后缀:",
            items, 0, False
        )

        if not ok:
            return "", False

        if item == "U (上卡)":
            return "U", True
        elif item == "L (下卡)":
            return "L", True
        else:  # 自定义
            custom_text, custom_ok = QInputDialog.getText(
                self, "自定义ETP后缀", "请输入自定义后缀:",
                QLineEdit.Normal, ""
            )
            return custom_text if custom_ok else "", custom_ok

    def _update_preview(self, device_type: str):
        """更新预览"""
        try:
            widgets = self.config_widgets[device_type]
            preview_text_widget = widgets['preview']

            # 生成示例
            example = self._generate_flexible_example(device_type)

            # 生成规则描述
            rules = self.device_rules[device_type]
            if not rules:
                preview_text = "请添加命名元素构建命名规则"
            else:
                rule_desc = " + ".join([element.display_name for element in rules])
                preview_text = f"规则: {rule_desc}\n示例: {example}"

            preview_text_widget.setText(preview_text)

        except Exception as e:
            self.logger.warning(f"更新预览失败: {e}")

    def _generate_flexible_example(self, device_type: str) -> str:
        """生成灵活的命名示例"""
        try:
            rules = self.device_rules[device_type]
            if not rules:
                return ""

            example_parts = []

            for element in rules:
                if element.element_type == "fixed_text":
                    example_parts.append(element.custom_text)
                elif element.element_type == "tag_name":
                    example_parts.append("AI_TEMP_001")
                elif element.element_type == "rack_number":
                    example_parts.append("1")
                elif element.element_type == "slot_number":
                    example_parts.append("3")
                elif element.element_type == "channel_number":
                    example_parts.append("05")
                elif element.element_type == "cabinet_number":
                    example_parts.append("02")
                elif element.element_type == "rail_number":
                    example_parts.append("F")
                elif element.element_type == "device_number":
                    example_parts.append("03")
                elif element.element_type == "cabinet_name":
                    example_parts.append("1103-SIS-SYS-101")
                elif element.element_type == "cabinet_name_short":
                    example_parts.append("SYS-101")
                elif element.element_type == "cable_name":
                    example_parts.append("CABLE_001")
                elif element.element_type == "etp_name":
                    example_parts.append("R1S3U")
                elif element.element_type == "etp_suffix":
                    example_parts.append(element.custom_suffix or "U")
                elif element.element_type == "signal_type":
                    example_parts.append("AI")
                elif element.element_type == "card_part_number":
                    example_parts.append("3721")
                else:
                    example_parts.append(f"[{element.display_name}]")

            return "".join(example_parts)

        except Exception as e:
            self.logger.warning(f"生成示例失败: {e}")
            return "示例生成失败"
    
    def _generate_example(self, device_type: str, prefix: str, separator: str, suffix: str) -> str:
        """生成命名示例"""
        try:
            # 根据器件类型生成不同的示例
            if device_type in ['barrier', 'relay', 'isolator']:
                parts = [prefix] if prefix else []
                parts.extend(['1', '3', '05'])  # 机架1, 槽位3, 通道05
                
                if separator:
                    example = separator.join(parts)
                else:
                    example = ''.join(parts)
                
                if suffix:
                    example += suffix
                    
                return example
                
            elif device_type == 'surge_protector':
                parts = [prefix] if prefix else []
                parts.extend(['1', '5', '05'])  # 机架1, 槽位5, 通道05
                
                if separator:
                    example = separator.join(parts)
                else:
                    example = ''.join(parts)
                
                if suffix:
                    example += suffix
                    
                return example
                
            elif device_type == 'terminal_block':
                parts = [prefix] if prefix else []
                parts.extend(['02', 'F', '03'])  # 机柜02, 导轨F, 器件03
                
                if separator:
                    example = separator.join(parts)
                else:
                    example = ''.join(parts)
                
                if suffix:
                    example += suffix
                    
                return example
                
            elif device_type == 'tr_terminal':
                parts = [prefix] if prefix else []
                parts.append('R1S3U')  # 示例ETP名
                
                if separator:
                    example = separator.join(parts)
                else:
                    example = ''.join(parts)
                
                if suffix:
                    example += suffix
                    
                return example
            
            return "示例"
            
        except Exception as e:
            return f"生成失败: {e}"
    
    def _load_config(self):
        """加载当前配置"""
        try:
            for device_type, _ in self.device_types:
                device_config = self.current_config.get(device_type, {})

                # 加载命名规则元素
                elements_config = device_config.get('elements', [])
                rules = []

                for element_config in elements_config:
                    element = NamingElement(
                        element_config.get('type', ''),
                        element_config.get('display_name', ''),
                        element_config.get('description', '')
                    )
                    element.custom_text = element_config.get('custom_text', '')
                    element.custom_suffix = element_config.get('custom_suffix', '')
                    rules.append(element)

                self.device_rules[device_type] = rules

                # 更新UI
                self._update_rule_list(device_type)
                self._update_preview(device_type)

        except Exception as e:
            self.logger.warning(f"加载配置失败: {e}")
            # 如果加载失败，使用默认配置
            self._load_default_config()

    def _load_default_config(self):
        """加载默认配置"""
        try:
            # 默认命名规则（使用空字符串让系统自动分配默认前缀）
            default_rules = {
                'barrier': [
                    ('fixed_text', '', ''),  # 将自动使用BA
                    ('rack_number', '', ''),
                    ('slot_number', '', ''),
                    ('channel_number', '', '')
                ],
                'relay': [
                    ('fixed_text', '', ''),  # 将自动使用RY
                    ('rack_number', '', ''),
                    ('slot_number', '', ''),
                    ('channel_number', '', '')
                ],
                'isolator': [
                    ('fixed_text', '', ''),  # 将自动使用ISL
                    ('rack_number', '', ''),
                    ('slot_number', '', ''),
                    ('channel_number', '', '')
                ],
                'surge_protector': [
                    ('fixed_text', '', ''),  # 将自动使用SP
                    ('rack_number', '', ''),
                    ('slot_number', '', ''),
                    ('channel_number', '', '')
                ],
                'terminal_block': [
                    ('fixed_text', '', ''),  # 将自动使用TB
                    ('cabinet_number', '', ''),
                    ('rail_number', '', ''),
                    ('device_number', '', '')
                ],
                'tr_terminal': [
                    ('fixed_text', '', ''),  # 将自动使用TR
                    ('etp_name', '', '')
                ]
            }

            for device_type, rule_configs in default_rules.items():
                rules = []
                for element_type, custom_text, custom_suffix in rule_configs:
                    # 找到对应的元素定义
                    element_def = next((e for e in self.available_elements if e.element_type == element_type), None)
                    if element_def:
                        element = NamingElement(element_def.element_type, element_def.display_name, element_def.description)
                        if element_type == 'fixed_text':
                            # 如果没有指定自定义文本，使用默认前缀
                            if not custom_text:
                                custom_text = self._get_default_prefix(device_type)
                            element.custom_text = custom_text
                            element.display_name = f"固定文本: {custom_text}"
                        elif element_type == 'etp_suffix':
                            element.custom_suffix = custom_suffix or 'U'
                            element.display_name = f"ETP后缀: {element.custom_suffix}"
                        rules.append(element)

                self.device_rules[device_type] = rules
                self._update_rule_list(device_type)
                self._update_preview(device_type)

        except Exception as e:
            self.logger.error(f"加载默认配置失败: {e}")

    def _preview_all_rules(self):
        """预览所有配置"""
        try:
            preview_text = "所有器件命名规则预览:\n\n"

            for device_type, display_name in self.device_types:
                example = self._generate_flexible_example(device_type)
                rules = self.device_rules[device_type]

                if rules:
                    rule_desc = " + ".join([element.display_name for element in rules])
                    preview_text += f"{display_name}:\n"
                    preview_text += f"  规则: {rule_desc}\n"
                    preview_text += f"  示例: {example}\n\n"
                else:
                    preview_text += f"{display_name}: 未配置\n\n"

            # 显示预览对话框
            preview_dialog = QDialog(self)
            preview_dialog.setWindowTitle("所有命名规则预览")
            preview_dialog.resize(500, 400)

            layout = QVBoxLayout(preview_dialog)

            preview_label = QTextEdit()
            preview_label.setPlainText(preview_text)
            preview_label.setReadOnly(True)
            layout.addWidget(preview_label)

            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(preview_dialog.accept)
            layout.addWidget(close_btn)

            preview_dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "预览失败", f"生成预览失败:\n{str(e)}")
    
    def _reset_to_default(self):
        """重置为默认配置"""
        reply = QMessageBox.question(
            self, "重置确认",
            "确定要重置所有命名规则为默认设置吗？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self._load_default_config()

    def _collect_config(self) -> Dict[str, Any]:
        """收集配置"""
        config = {}

        for device_type, _ in self.device_types:
            rules = self.device_rules[device_type]

            elements_config = []
            for element in rules:
                element_config = {
                    'type': element.element_type,
                    'display_name': element.display_name,
                    'description': element.description,
                    'custom_text': element.custom_text,
                    'custom_suffix': element.custom_suffix
                }
                elements_config.append(element_config)

            device_config = {
                'elements': elements_config
            }

            config[device_type] = device_config

        return config

    def _save_config(self):
        """保存配置"""
        try:
            config = self._collect_config()
            self.config_updated.emit(config)
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存配置失败:\n{str(e)}")


# 为了向后兼容，保留SimpleNamingDialog别名
SimpleNamingDialog = FlexibleNamingDialog
