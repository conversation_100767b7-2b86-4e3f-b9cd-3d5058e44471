"""
项目管理对话框
提供新建项目、打开项目等对话框界面
"""

import logging
from pathlib import Path
from typing import Optional, Tuple

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QTextEdit, QPushButton, QFileDialog,
    QMessageBox, QGroupBox, QListWidget, QListWidgetItem
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from core.logger import get_logger
from .styles import APPLE_COLORS, get_apple_style
from .icons import get_icon


class NewProjectDialog(QDialog):
    """新建项目对话框"""
    
    def __init__(self, parent=None):
        """
        初始化新建项目对话框
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        # 对话框属性
        self.setWindowTitle("新建项目")
        self.setFixedSize(500, 400)
        self.setModal(True)
        
        # 项目信息
        self.project_name = ""
        self.project_path = ""
        self.project_description = ""
        
        # 初始化UI
        self._setup_ui()
        self._apply_styles()
        self._connect_signals()
        
        # 设置默认路径
        self._set_default_path()
        
        self.logger.info("新建项目对话框初始化完成")
    
    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title_label = QLabel("创建新的EWReborn项目")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 项目信息组
        info_group = QGroupBox("项目信息")
        info_layout = QGridLayout(info_group)
        info_layout.setSpacing(15)
        
        # 项目名称
        info_layout.addWidget(QLabel("项目名称:"), 0, 0)
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入项目名称")
        info_layout.addWidget(self.name_edit, 0, 1)
        
        # 项目路径
        info_layout.addWidget(QLabel("保存位置:"), 1, 0)
        path_layout = QHBoxLayout()
        self.path_edit = QLineEdit()
        self.path_edit.setReadOnly(True)
        self.browse_btn = QPushButton(get_icon('folder'), "浏览...")
        self.browse_btn.setMaximumWidth(100)
        
        path_layout.addWidget(self.path_edit)
        path_layout.addWidget(self.browse_btn)
        info_layout.addLayout(path_layout, 1, 1)
        
        # 项目描述
        info_layout.addWidget(QLabel("项目描述:"), 2, 0, Qt.AlignTop)
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("请输入项目描述（可选）")
        self.description_edit.setMaximumHeight(80)
        info_layout.addWidget(self.description_edit, 2, 1)
        
        layout.addWidget(info_group)
        
        # 项目预览
        preview_group = QGroupBox("项目预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_label = QLabel("项目将创建在: ")
        self.preview_label.setStyleSheet(f"color: {APPLE_COLORS['text_secondary']};")
        preview_layout.addWidget(self.preview_label)
        
        layout.addWidget(preview_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setMinimumWidth(80)
        button_layout.addWidget(self.cancel_btn)
        
        self.create_btn = QPushButton(get_icon('check'), "创建项目")
        self.create_btn.setMinimumWidth(100)
        self.create_btn.setEnabled(False)
        button_layout.addWidget(self.create_btn)
        
        layout.addLayout(button_layout)
    
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet(get_apple_style())
        
        # 特殊样式
        self.create_btn.setProperty("class", "success")
        self.cancel_btn.setProperty("class", "secondary")
    
    def _connect_signals(self):
        """连接信号和槽"""
        self.name_edit.textChanged.connect(self._on_name_changed)
        self.browse_btn.clicked.connect(self._browse_path)
        self.description_edit.textChanged.connect(self._update_preview)
        
        self.create_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
    
    def _set_default_path(self):
        """设置默认路径"""
        default_path = Path.home() / "Documents" / "EWReborn Projects"
        self.project_path = str(default_path)
        self.path_edit.setText(self.project_path)
        self._update_preview()
    
    def _on_name_changed(self, text: str):
        """项目名称改变时的处理"""
        self.project_name = text.strip()
        self.create_btn.setEnabled(bool(self.project_name and self.project_path))
        self._update_preview()
    
    def _browse_path(self):
        """浏览保存路径"""
        path = QFileDialog.getExistingDirectory(
            self, "选择项目保存位置", self.project_path
        )
        
        if path:
            self.project_path = path
            self.path_edit.setText(path)
            self._update_preview()
    
    def _update_preview(self):
        """更新项目预览"""
        if self.project_name and self.project_path:
            full_path = Path(self.project_path) / f"{self.project_name}.ewproj"
            self.preview_label.setText(f"项目将创建在: {full_path}")
        else:
            self.preview_label.setText("项目将创建在: ")
    
    def get_project_info(self) -> Tuple[str, str, str]:
        """
        获取项目信息
        
        Returns:
            (项目名称, 项目路径, 项目描述)
        """
        return (
            self.project_name,
            self.project_path,
            self.description_edit.toPlainText().strip()
        )


class RecentProjectsDialog(QDialog):
    """最近项目对话框"""
    
    project_selected = Signal(str)  # 项目选择信号
    
    def __init__(self, recent_projects: list, parent=None):
        """
        初始化最近项目对话框
        
        Args:
            recent_projects: 最近项目列表
            parent: 父窗口
        """
        super().__init__(parent)
        self.logger = get_logger(__name__)
        self.recent_projects = recent_projects
        
        # 对话框属性
        self.setWindowTitle("打开最近项目")
        self.setFixedSize(600, 400)
        self.setModal(True)
        
        # 初始化UI
        self._setup_ui()
        self._apply_styles()
        self._connect_signals()
        self._load_recent_projects()
        
        self.logger.info("最近项目对话框初始化完成")
    
    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title_label = QLabel("选择要打开的项目")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 项目列表
        list_group = QGroupBox("最近项目")
        list_layout = QVBoxLayout(list_group)
        
        self.project_list = QListWidget()
        self.project_list.setAlternatingRowColors(True)
        list_layout.addWidget(self.project_list)
        
        layout.addWidget(list_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.browse_btn = QPushButton(get_icon('folder'), "浏览其他项目...")
        button_layout.addWidget(self.browse_btn)
        
        button_layout.addStretch()
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setMinimumWidth(80)
        button_layout.addWidget(self.cancel_btn)
        
        self.open_btn = QPushButton(get_icon('check'), "打开项目")
        self.open_btn.setMinimumWidth(100)
        self.open_btn.setEnabled(False)
        button_layout.addWidget(self.open_btn)
        
        layout.addLayout(button_layout)
    
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet(get_apple_style())
        
        # 特殊样式
        self.open_btn.setProperty("class", "success")
        self.cancel_btn.setProperty("class", "secondary")
        self.browse_btn.setProperty("class", "secondary")
    
    def _connect_signals(self):
        """连接信号和槽"""
        self.project_list.itemSelectionChanged.connect(self._on_selection_changed)
        self.project_list.itemDoubleClicked.connect(self._on_item_double_clicked)
        
        self.browse_btn.clicked.connect(self._browse_project)
        self.open_btn.clicked.connect(self._open_selected_project)
        self.cancel_btn.clicked.connect(self.reject)
    
    def _load_recent_projects(self):
        """加载最近项目列表"""
        self.project_list.clear()
        
        for project_path in self.recent_projects:
            project_file = Path(project_path)
            
            # 检查文件是否存在
            if project_file.exists():
                item = QListWidgetItem()
                item.setText(f"{project_file.stem}")
                item.setToolTip(str(project_file))
                item.setData(Qt.UserRole, str(project_file))
                
                # 添加项目信息
                try:
                    import json
                    with open(project_file, 'r', encoding='utf-8') as f:
                        project_data = json.load(f)
                    
                    project_info = project_data.get("project_info", {})
                    description = project_info.get("description", "")
                    modified_date = project_info.get("modified_date", "")
                    
                    if description:
                        item.setText(f"{project_file.stem}\n{description}")
                    
                    if modified_date:
                        item.setToolTip(f"{project_file}\n最后修改: {modified_date}")
                
                except Exception as e:
                    self.logger.warning(f"读取项目信息失败: {e}")
                
                self.project_list.addItem(item)
        
        if self.project_list.count() == 0:
            item = QListWidgetItem("没有最近打开的项目")
            item.setFlags(Qt.NoItemFlags)
            self.project_list.addItem(item)
    
    def _on_selection_changed(self):
        """选择改变时的处理"""
        has_selection = bool(self.project_list.currentItem() and 
                           self.project_list.currentItem().flags() & Qt.ItemIsEnabled)
        self.open_btn.setEnabled(has_selection)
    
    def _on_item_double_clicked(self, item):
        """项目双击时的处理"""
        if item.flags() & Qt.ItemIsEnabled:
            self._open_selected_project()
    
    def _browse_project(self):
        """浏览其他项目"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "打开项目文件", "", "EWReborn项目文件 (*.ewproj)"
        )
        
        if file_path:
            self.project_selected.emit(file_path)
            self.accept()
    
    def _open_selected_project(self):
        """打开选中的项目"""
        current_item = self.project_list.currentItem()
        if current_item and current_item.flags() & Qt.ItemIsEnabled:
            project_path = current_item.data(Qt.UserRole)
            self.project_selected.emit(project_path)
            self.accept()
