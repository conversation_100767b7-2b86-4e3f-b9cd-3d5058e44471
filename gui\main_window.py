"""
主窗口
EWReborn应用程序的主窗口界面
"""

import logging
from typing import Dict, Any, Optional
from pathlib import Path

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QMenuBar, QStatusBar, QToolBar,
    QMessageBox, QProgressBar, QLabel, QSplitter, QFrame,
    QApplication
)
from PySide6.QtCore import Qt, QTimer, Signal, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QAction, QIcon, QKeySequence, QPalette

from .allocation_widget import AllocationWidget
from .config_widget import ConfigWidget
from .xml_editor_widget import XMLEditorWidget
from .material_theme import MaterialThemeManager, init_theme_manager
from .project_dialog import ProjectDialog
from core.logger import get_logger
from core.project_manager import ProjectManager


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 信号定义
    status_message = Signal(str, int)  # 状态消息信号
    progress_update = Signal(int, str)  # 进度更新信号
    
    def __init__(self, config: Dict[str, Any], parent=None):
        """
        初始化主窗口
        
        Args:
            config: 应用程序配置
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.config = config
        self.logger = get_logger(__name__)

        # 初始化项目管理器
        self.project_manager = ProjectManager(config)

        # 初始化主题管理器
        self.theme_manager = init_theme_manager(config)

        # 初始化UI
        self._setup_ui()
        self._setup_menu_bar()
        self._setup_tool_bar()
        self._setup_status_bar()
        self._connect_signals()

        # 应用配置
        self._apply_config()

        self.logger.info("主窗口初始化完成")
    
    def _setup_ui(self):
        """设置用户界面"""
        # 设置窗口属性
        self.setWindowTitle(self.config.get('application.name', 'EWReborn'))
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建选项卡控件
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 创建各个选项卡
        self._create_allocation_tab()
        self._create_xml_editor_tab()
        self._create_config_tab()
        
        # 设置默认选项卡
        self.tab_widget.setCurrentIndex(0)
    
    def _create_allocation_tab(self):
        """创建I/O分配选项卡"""
        try:
            self.allocation_widget = AllocationWidget(self.config, self)
            self.tab_widget.addTab(self.allocation_widget, "I/O点分配")
            
            # 连接分配相关信号
            self.allocation_widget.progress_updated.connect(self.progress_update)
            self.allocation_widget.status_message.connect(self.status_message)
            
        except Exception as e:
            self.logger.error(f"创建I/O分配选项卡失败: {e}")
            # 创建占位符
            placeholder = QLabel("I/O分配功能暂时不可用")
            placeholder.setAlignment(Qt.AlignCenter)
            self.tab_widget.addTab(placeholder, "I/O点分配")
    
    def _create_xml_editor_tab(self):
        """创建XML编辑器选项卡"""
        try:
            self.xml_editor_widget = XMLEditorWidget(self.config, self)
            self.tab_widget.addTab(self.xml_editor_widget, "XML编辑器")
            
        except Exception as e:
            self.logger.error(f"创建XML编辑器选项卡失败: {e}")
            # 创建占位符
            placeholder = QLabel("XML编辑器功能暂时不可用")
            placeholder.setAlignment(Qt.AlignCenter)
            self.tab_widget.addTab(placeholder, "XML编辑器")
    
    def _create_config_tab(self):
        """创建配置选项卡"""
        try:
            self.config_widget = ConfigWidget(self.config, self)
            self.tab_widget.addTab(self.config_widget, "配置")
            
            # 连接配置更新信号
            self.config_widget.config_changed.connect(self._on_config_changed)
            
        except Exception as e:
            self.logger.error(f"创建配置选项卡失败: {e}")
            # 创建占位符
            placeholder = QLabel("配置功能暂时不可用")
            placeholder.setAlignment(Qt.AlignCenter)
            self.tab_widget.addTab(placeholder, "配置")
    
    def _setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 新建项目
        new_action = QAction("新建项目(&N)", self)
        new_action.setShortcut(QKeySequence.New)
        new_action.triggered.connect(self._new_project)
        file_menu.addAction(new_action)
        
        # 打开项目
        open_action = QAction("打开项目(&O)", self)
        open_action.setShortcut(QKeySequence.Open)
        open_action.triggered.connect(self._open_project)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        # 保存
        save_action = QAction("保存(&S)", self)
        save_action.setShortcut(QKeySequence.Save)
        save_action.triggered.connect(self._save_project)
        file_menu.addAction(save_action)
        
        # 另存为
        save_as_action = QAction("另存为(&A)", self)
        save_as_action.setShortcut(QKeySequence.SaveAs)
        save_as_action.triggered.connect(self._save_as_project)
        file_menu.addAction(save_as_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")
        
        # 数据验证
        validate_action = QAction("数据验证(&V)", self)
        validate_action.triggered.connect(self._validate_data)
        tools_menu.addAction(validate_action)
        
        # 清理缓存
        clear_cache_action = QAction("清理缓存(&C)", self)
        clear_cache_action.triggered.connect(self._clear_cache)
        tools_menu.addAction(clear_cache_action)

        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")

        # 主题子菜单
        theme_menu = view_menu.addMenu("主题(&T)")
        self._setup_theme_menu(theme_menu)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")

        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)

    def _setup_theme_menu(self, theme_menu):
        """设置主题菜单"""
        if not self.theme_manager:
            return

        # 获取主题分类
        theme_categories = self.theme_manager.get_theme_categories()
        current_theme = self.theme_manager.get_current_theme()

        # 深色主题子菜单
        if theme_categories.get('dark'):
            dark_menu = theme_menu.addMenu("深色主题(&D)")
            for theme in theme_categories['dark']:
                action = QAction(theme.replace('dark_', '').replace('.xml', '').title(), self)
                action.setCheckable(True)
                action.setChecked(theme == current_theme)
                action.triggered.connect(lambda checked, t=theme: self._change_theme(t))
                dark_menu.addAction(action)

        # 浅色主题子菜单
        if theme_categories.get('light'):
            light_menu = theme_menu.addMenu("浅色主题(&L)")
            for theme in theme_categories['light']:
                action = QAction(theme.replace('light_', '').replace('.xml', '').title(), self)
                action.setCheckable(True)
                action.setChecked(theme == current_theme)
                action.triggered.connect(lambda checked, t=theme: self._change_theme(t))
                light_menu.addAction(action)

    def _setup_tool_bar(self):
        """设置工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setMovable(False)
        
        # 添加常用操作按钮
        # 这里可以根据需要添加工具栏按钮
        pass
    
    def _setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = self.statusBar()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # 版本信息
        version_label = QLabel(f"v{self.config.get('application.version', '1.0.0')}")
        self.status_bar.addPermanentWidget(version_label)
    
    def _connect_signals(self):
        """连接信号和槽"""
        self.status_message.connect(self._update_status_message)
        self.progress_update.connect(self._update_progress)

        # 连接主题管理器信号
        if self.theme_manager:
            self.theme_manager.theme_changed.connect(self._on_theme_changed)
    
    def _apply_config(self):
        """应用配置"""
        # 窗口大小
        window_config = self.config.get('application.window', {})
        width = window_config.get('width', 1400)
        height = window_config.get('height', 900)
        min_width = window_config.get('min_width', 1000)
        min_height = window_config.get('min_height', 600)
        
        self.resize(width, height)
        self.setMinimumSize(min_width, min_height)
        
        # 居中显示
        self._center_window()

        # 应用Material主题
        self._apply_material_theme()
    
    def _center_window(self):
        """将窗口居中显示"""
        screen = self.screen().availableGeometry()
        window = self.frameGeometry()
        window.moveCenter(screen.center())
        self.move(window.topLeft())

    def _apply_material_theme(self):
        """应用Material Design主题"""
        try:
            if not self.theme_manager:
                self.logger.warning("主题管理器未初始化，跳过主题应用")
                return

            # 获取QApplication实例
            app = QApplication.instance()
            if not app:
                self.logger.error("无法获取QApplication实例")
                return

            # 应用当前主题
            current_theme = self.theme_manager.get_current_theme()
            success = self.theme_manager.apply_theme(current_theme, app)

            if success:
                # 设置窗口标题
                original_title = self.windowTitle()
                if not original_title.startswith("🎨"):
                    self.setWindowTitle("🎨 " + original_title)

                self.logger.info(f"Material主题应用成功: {current_theme}")
            else:
                self.logger.error("Material主题应用失败")

        except Exception as e:
            self.logger.error(f"应用Material主题失败: {e}")



    def _update_window_title(self):
        """更新窗口标题"""
        base_title = self.config.get('application.name', 'EWReborn')

        if self.project_manager.is_project_open():
            project_info = self.project_manager.get_current_project_info()
            if project_info:
                title = f"⚡ {base_title} - {project_info.name}"
            else:
                title = f"⚡ {base_title} - 项目已打开"
        else:
            title = f"⚡ {base_title}"

        self.setWindowTitle(title)

    def _update_project_status(self):
        """更新项目状态"""
        if self.project_manager.is_project_open():
            project_info = self.project_manager.get_current_project_info()
            if project_info:
                status_text = f"项目: {project_info.name}"
            else:
                status_text = "项目已打开"
        else:
            status_text = "就绪"

        self.status_bar.showMessage(status_text)

    def _collect_project_data(self) -> dict:
        """收集当前项目数据"""
        if not self.project_manager.has_current_project:
            return {}

        # 获取当前项目数据
        project_data = self.project_manager.current_project.copy()

        # 这里可以添加从各个界面收集数据的逻辑
        # 例如从分配界面收集设置等

        return project_data

    def _load_project_data(self, project_data: dict):
        """加载项目数据到界面"""
        try:
            # 这里可以添加将项目数据加载到各个界面的逻辑
            # 例如设置分配参数、加载数据文件路径等

            self.logger.info("项目数据加载到界面完成")

        except Exception as e:
            self.logger.error(f"加载项目数据到界面失败: {e}")

    # 槽函数
    def _update_status_message(self, message: str, timeout: int = 0):
        """更新状态栏消息"""
        self.status_label.setText(message)
        if timeout > 0:
            QTimer.singleShot(timeout, lambda: self.status_label.setText("就绪"))

    def _update_progress(self, value: int, text: str = ""):
        """更新进度条"""
        if value < 0:
            # 隐藏进度条
            self.progress_bar.setVisible(False)
        else:
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(value)
            if text:
                self.progress_bar.setFormat(f"{text} %p%")
            else:
                self.progress_bar.setFormat("%p%")

    def _on_log_message(self, level: str, message: str, timestamp: float):
        """处理日志消息"""
        # 可以在这里实现日志消息的GUI显示
        # 例如在状态栏显示重要消息
        if level in ['ERROR', 'CRITICAL']:
            self._update_status_message(f"错误: {message}", 5000)
        elif level == 'WARNING':
            self._update_status_message(f"警告: {message}", 3000)

    def _on_config_changed(self, config: Dict[str, Any]):
        """配置更改处理"""
        self.config = config
        self.logger.info("配置已更新")
        # 可以在这里重新应用配置

    # 项目变更处理
    def _on_project_changed(self, project_path: str):
        """项目变更处理"""
        self.logger.info(f"项目已变更: {project_path}")

        # 更新界面
        self._update_window_title()
        self._update_project_status()

        # 刷新数据加载界面
        if hasattr(self, 'allocation_widget'):
            self.allocation_widget.refresh_data_paths()

    # 菜单动作处理
    def _new_project(self):
        """新建项目"""
        self.logger.info("新建项目")

        try:
            # 检查是否有项目打开
            if self.project_manager.is_project_open():
                reply = QMessageBox.question(
                    self, "关闭当前项目",
                    "将关闭当前项目，是否继续？",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.No:
                    return

            # 显示项目管理对话框（新建项目标签页）
            dialog = ProjectDialog(self.project_manager, self)
            dialog.tab_widget.setCurrentIndex(0)  # 切换到新建项目标签页
            dialog.project_changed.connect(self._on_project_changed)

            dialog.exec()

        except Exception as e:
            error_msg = f"新建项目失败: {str(e)}"
            QMessageBox.critical(self, "新建项目失败", error_msg)
            self.logger.error(error_msg)

    def _open_project(self):
        """打开项目"""
        self.logger.info("打开项目")

        try:
            # 检查是否有项目打开
            if self.project_manager.is_project_open():
                reply = QMessageBox.question(
                    self, "关闭当前项目",
                    "将关闭当前项目，是否继续？",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.No:
                    return

            # 显示项目管理对话框（打开项目标签页）
            dialog = ProjectDialog(self.project_manager, self)
            dialog.tab_widget.setCurrentIndex(1)  # 切换到打开项目标签页
            dialog.project_changed.connect(self._on_project_changed)

            dialog.exec()

        except Exception as e:
            error_msg = f"打开项目失败: {str(e)}"
            QMessageBox.critical(self, "打开项目失败", error_msg)
            self.logger.error(error_msg)

    def _browse_project_file(self):
        """浏览项目文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "打开项目文件", "", "EWReborn项目文件 (*.ewproj)"
        )

        if file_path:
            self._load_project_file(file_path)

    def _load_project_file(self, file_path: str):
        """加载项目文件"""
        try:
            # 打开项目
            project_data = self.project_manager.open_project(file_path)

            # 更新界面
            self._update_window_title()
            self._update_project_status()
            self._load_project_data(project_data)

            # 显示成功消息
            project_name = project_data["project_info"]["name"]
            QMessageBox.information(
                self, "项目打开成功",
                f"项目 '{project_name}' 打开成功！"
            )

            self.logger.info(f"项目打开成功: {project_name}")

        except Exception as e:
            error_msg = f"打开项目文件失败: {str(e)}"
            QMessageBox.critical(self, "打开项目失败", error_msg)
            self.logger.error(error_msg)

    def _save_project(self):
        """保存项目"""
        self.logger.info("保存项目")

        try:
            if not self.project_manager.has_current_project:
                QMessageBox.warning(self, "保存项目", "没有打开的项目可以保存")
                return

            # 收集当前项目数据
            project_data = self._collect_project_data()

            # 保存项目
            if self.project_manager.save_project(project_data):
                self._update_window_title()
                self._update_project_status()
                QMessageBox.information(self, "保存成功", "项目保存成功！")
                self.logger.info("项目保存成功")
            else:
                QMessageBox.critical(self, "保存失败", "项目保存失败")

        except Exception as e:
            error_msg = f"保存项目失败: {str(e)}"
            QMessageBox.critical(self, "保存项目失败", error_msg)
            self.logger.error(error_msg)

    def _save_as_project(self):
        """另存为项目"""
        self.logger.info("另存为项目")
        # TODO: 实现另存为项目功能
        QMessageBox.information(self, "另存为", "另存为功能正在开发中")

    def _validate_data(self):
        """数据验证"""
        self.logger.info("开始数据验证")
        # 切换到分配选项卡并执行验证
        self.tab_widget.setCurrentIndex(0)
        if hasattr(self.allocation_widget, 'validate_data'):
            self.allocation_widget.validate_data()

    def _clear_cache(self):
        """清理缓存"""
        self.logger.info("清理缓存")
        # TODO: 实现清理缓存功能
        QMessageBox.information(self, "清理缓存", "缓存已清理")

    def _show_about(self):
        """显示关于对话框"""
        about_text = f"""
        <h3>{self.config.get('application.name', 'EWReborn')}</h3>
        <p>版本: {self.config.get('application.version', '1.0.0')}</p>
        <p>自动化I/O点领域分配系统</p>
        <p>用于工业控制系统中I/O点到机柜导轨和机架的自动化分配</p>
        <p><b>主要功能:</b></p>
        <ul>
        <li>IODB数据验证</li>
        <li>自动I/O点分配</li>
        <li>XML配置文件编辑</li>
        <li>分配结果报告</li>
        </ul>
        <p>© 2024 EWReborn Development Team</p>
        """
        QMessageBox.about(self, "关于", about_text)

    def _change_theme(self, theme_name: str):
        """切换主题"""
        if not self.theme_manager:
            return

        try:
            app = QApplication.instance()
            success = self.theme_manager.apply_theme(theme_name, app)

            if success:
                # 保存主题配置
                self.theme_manager.save_theme_config()
                self.logger.info(f"主题切换成功: {theme_name}")
            else:
                self.logger.error(f"主题切换失败: {theme_name}")

        except Exception as e:
            self.logger.error(f"切换主题时发生错误: {e}")

    def _on_theme_changed(self, theme_name: str):
        """主题更改事件处理"""
        self.logger.info(f"主题已更改为: {theme_name}")

        # 更新菜单项的选中状态
        self._update_theme_menu_state(theme_name)

    def _update_theme_menu_state(self, current_theme: str):
        """更新主题菜单的选中状态"""
        # 这里可以添加更新菜单项选中状态的逻辑
        pass

    def closeEvent(self, event):
        """窗口关闭事件"""
        reply = QMessageBox.question(
            self,
            "确认退出",
            "确定要退出应用程序吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.logger.info("用户确认退出应用程序")
            event.accept()
        else:
            event.ignore()
