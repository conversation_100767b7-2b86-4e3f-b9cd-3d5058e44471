"""
测试项目管理功能
"""

import sys
import tempfile
import shutil
from pathlib import Path
from core.project_manager import ProjectManager
from utils.config_manager_simple import Config<PERSON>anager


def test_project_manager():
    """测试项目管理器功能"""
    print("=== 测试项目管理器功能 ===")
    
    # 加载配置
    config_manager = ConfigManager()
    config = config_manager.load_config()
    
    # 创建项目管理器
    project_manager = ProjectManager(config)
    
    # 创建临时目录用于测试
    temp_dir = Path(tempfile.mkdtemp())
    print(f"测试目录: {temp_dir}")
    
    try:
        print("\n1. 测试项目创建...")
        project_name = "TestProject"
        description = "这是一个测试项目"
        
        success = project_manager.create_new_project(project_name, str(temp_dir), description)
        print(f"✓ 项目创建: {'成功' if success else '失败'}")
        
        if success:
            project_path = temp_dir / project_name
            print(f"  项目路径: {project_path}")
            
            # 验证项目结构
            validation_result = project_manager.validate_project_structure(str(project_path))
            print("  项目结构验证:")
            for folder, exists in validation_result.items():
                print(f"    {'✓' if exists else '✗'} {folder}")
        
        print("\n2. 测试项目打开...")
        if success:
            project_path = temp_dir / project_name
            open_success = project_manager.open_project(str(project_path))
            print(f"✓ 项目打开: {'成功' if open_success else '失败'}")
            
            if open_success:
                project_info = project_manager.get_current_project_info()
                if project_info:
                    print(f"  项目名称: {project_info.name}")
                    print(f"  项目路径: {project_info.path}")
                    print(f"  项目描述: {project_info.description}")
                    print(f"  创建时间: {project_info.created_date}")
                
                # 测试数据路径
                data_paths = project_manager.get_project_data_paths()
                print("  数据路径:")
                for key, path in data_paths.items():
                    exists = Path(path).exists()
                    print(f"    {key}: {path} {'✓' if exists else '✗'}")
        
        print("\n3. 测试项目状态...")
        print(f"✓ 项目是否打开: {project_manager.is_project_open()}")
        
        print("\n4. 测试项目关闭...")
        project_manager.close_project()
        print(f"✓ 项目关闭后状态: {'已关闭' if not project_manager.is_project_open() else '仍打开'}")
        
    finally:
        # 清理临时目录
        try:
            shutil.rmtree(temp_dir)
            print(f"\n✓ 清理临时目录: {temp_dir}")
        except Exception as e:
            print(f"\n✗ 清理临时目录失败: {e}")
    
    print("\n=== 项目管理器测试完成 ===")


if __name__ == "__main__":
    test_project_manager()
