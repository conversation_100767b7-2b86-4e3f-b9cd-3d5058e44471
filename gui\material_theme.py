"""
Material Design主题管理模块
基于qt-material库提供统一的主题管理功能
"""

import logging
from typing import Dict, Any, Optional, List
from pathlib import Path

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QObject, Signal

try:
    from qt_material import apply_stylesheet, list_themes
    import qtawesome as qta
    QT_MATERIAL_AVAILABLE = True
except ImportError:
    QT_MATERIAL_AVAILABLE = False
    logging.warning("qt-material或qtawesome未安装，将使用备用样式系统")

from core.logger import get_logger


class MaterialThemeManager(QObject):
    """Material Design主题管理器"""
    
    # 信号定义
    theme_changed = Signal(str)  # 主题更改信号
    
    def __init__(self, config: Dict[str, Any], parent=None):
        """
        初始化主题管理器
        
        Args:
            config: 应用程序配置
            parent: 父对象
        """
        super().__init__(parent)
        
        self.config = config
        self.logger = get_logger(__name__)
        self.current_theme = None
        
        # 检查qt-material可用性
        if not QT_MATERIAL_AVAILABLE:
            self.logger.error("qt-material库不可用，请安装: pip install qt-material qtawesome")
            return
        
        # 初始化主题配置
        self._init_theme_config()
        
        self.logger.info("Material主题管理器初始化完成")
    
    def _init_theme_config(self):
        """初始化主题配置"""
        # 获取默认主题设置
        gui_config = self.config.get('gui', {})
        self.current_theme = gui_config.get('material_theme', 'dark_teal.xml')
        
        # 确保主题文件存在
        if self.current_theme not in self.get_available_themes():
            self.current_theme = 'dark_teal.xml'
            self.logger.warning(f"指定的主题不存在，使用默认主题: {self.current_theme}")
    
    def get_available_themes(self) -> List[str]:
        """
        获取所有可用的主题列表
        
        Returns:
            主题名称列表
        """
        if not QT_MATERIAL_AVAILABLE:
            return []
        
        try:
            return list_themes()
        except Exception as e:
            self.logger.error(f"获取主题列表失败: {e}")
            return ['dark_teal.xml', 'light_blue.xml']  # 备用主题
    
    def get_theme_categories(self) -> Dict[str, List[str]]:
        """
        获取按类别分组的主题
        
        Returns:
            按深色/浅色分组的主题字典
        """
        themes = self.get_available_themes()
        
        dark_themes = [t for t in themes if t.startswith('dark_')]
        light_themes = [t for t in themes if t.startswith('light_')]
        
        return {
            'dark': dark_themes,
            'light': light_themes
        }
    
    def apply_theme(self, theme_name: str, app: Optional[QApplication] = None) -> bool:
        """
        应用指定的主题

        Args:
            theme_name: 主题名称
            app: QApplication实例，如果为None则自动获取

        Returns:
            是否应用成功
        """
        if app is None:
            app = QApplication.instance()
            if app is None:
                self.logger.error("无法获取QApplication实例")
                return False

        if not QT_MATERIAL_AVAILABLE:
            # 使用备用样式系统
            return self._apply_fallback_theme(theme_name, app)

        try:
            # 检查主题是否存在
            if theme_name not in self.get_available_themes():
                self.logger.error(f"主题不存在: {theme_name}")
                return False

            # 应用主题
            extra_config = self._get_extra_config(theme_name)

            # 对于浅色主题，启用invert_secondary
            invert_secondary = theme_name.startswith('light_')

            apply_stylesheet(
                app,
                theme=theme_name,
                invert_secondary=invert_secondary,
                extra=extra_config
            )

            self.current_theme = theme_name
            self.theme_changed.emit(theme_name)

            self.logger.info(f"主题应用成功: {theme_name}")
            return True

        except Exception as e:
            self.logger.error(f"应用主题失败: {e}")
            return False
    
    def _get_extra_config(self, theme_name: str) -> Dict[str, Any]:
        """
        获取主题的额外配置
        
        Args:
            theme_name: 主题名称
            
        Returns:
            额外配置字典
        """
        # 基础配置
        extra = {
            'QMenu': {
                'height': 40,
                'padding': '8px 16px 8px 16px'
            },
            'QTabWidget::pane': {
                'border-radius': '8px'
            },
            'QGroupBox': {
                'border-radius': '8px',
                'padding-top': '16px'
            }
        }
        
        # 根据主题类型调整配置
        if theme_name.startswith('dark_'):
            extra.update({
                'QStatusBar': {
                    'border-top': '1px solid #3C3C3C'
                }
            })
        else:
            extra.update({
                'QStatusBar': {
                    'border-top': '1px solid #E0E0E0'
                }
            })
        
        return extra
    
    def get_current_theme(self) -> str:
        """
        获取当前主题名称
        
        Returns:
            当前主题名称
        """
        return self.current_theme or 'dark_teal.xml'
    
    def is_dark_theme(self, theme_name: Optional[str] = None) -> bool:
        """
        判断是否为深色主题
        
        Args:
            theme_name: 主题名称，如果为None则使用当前主题
            
        Returns:
            是否为深色主题
        """
        if theme_name is None:
            theme_name = self.get_current_theme()
        
        return theme_name.startswith('dark_')
    
    def get_icon(self, icon_name: str, color: Optional[str] = None) -> Any:
        """
        获取Material Design图标
        
        Args:
            icon_name: 图标名称 (FontAwesome图标名)
            color: 图标颜色，如果为None则使用主题默认颜色
            
        Returns:
            QIcon对象
        """
        if not QT_MATERIAL_AVAILABLE:
            from .icons import get_icon
            return get_icon(icon_name)
        
        try:
            # 根据当前主题确定默认颜色
            if color is None:
                color = '#FFFFFF' if self.is_dark_theme() else '#000000'
            
            return qta.icon(f'fa5s.{icon_name}', color=color)
        except Exception as e:
            self.logger.warning(f"获取图标失败: {icon_name}, {e}")
            # 回退到原有图标系统
            from .icons import get_icon
            return get_icon(icon_name)
    
    def save_theme_config(self):
        """保存当前主题配置到配置文件"""
        try:
            # 更新配置
            if 'gui' not in self.config:
                self.config['gui'] = {}
            
            self.config['gui']['material_theme'] = self.current_theme
            
            # 这里应该调用配置管理器保存配置
            # 由于我们不直接访问配置管理器，发出信号让主窗口处理
            self.logger.info(f"主题配置已更新: {self.current_theme}")
            
        except Exception as e:
            self.logger.error(f"保存主题配置失败: {e}")

    def _apply_fallback_theme(self, theme_name: str, app: QApplication) -> bool:
        """
        应用备用主题（当qt-material不可用时）

        Args:
            theme_name: 主题名称
            app: QApplication实例

        Returns:
            是否应用成功
        """
        try:
            # 基于主题名称确定是深色还是浅色
            is_dark = theme_name.startswith('dark_')

            # 定义备用样式
            fallback_style = self._get_fallback_style(is_dark)

            # 应用样式
            app.setStyleSheet(fallback_style)

            self.current_theme = theme_name
            self.theme_changed.emit(theme_name)

            self.logger.info(f"备用主题应用成功: {theme_name}")
            return True

        except Exception as e:
            self.logger.error(f"应用备用主题失败: {e}")
            return False

    def _get_fallback_style(self, is_dark: bool) -> str:
        """
        获取备用样式表

        Args:
            is_dark: 是否为深色主题

        Returns:
            样式表字符串
        """
        if is_dark:
            # 深色主题样式
            return """
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 13px;
            }
            QMainWindow {
                background-color: #2b2b2b;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #404040;
                color: #808080;
            }
            QTabWidget::pane {
                border: 1px solid #404040;
                background-color: #323232;
            }
            QTabBar::tab {
                background-color: #404040;
                color: #ffffff;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #0078d4;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #404040;
                border-radius: 4px;
                margin-top: 8px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 4px 0 4px;
            }
            """
        else:
            # 浅色主题样式
            return """
            QWidget {
                background-color: #ffffff;
                color: #000000;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 13px;
            }
            QMainWindow {
                background-color: #f5f5f5;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #e0e0e0;
                color: #808080;
            }
            QTabWidget::pane {
                border: 1px solid #d0d0d0;
                background-color: #ffffff;
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                color: #000000;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #0078d4;
                color: white;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                margin-top: 8px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 4px 0 4px;
            }
            """


# 全局主题管理器实例
_theme_manager: Optional[MaterialThemeManager] = None


def get_theme_manager() -> Optional[MaterialThemeManager]:
    """获取全局主题管理器实例"""
    return _theme_manager


def init_theme_manager(config: Dict[str, Any]) -> MaterialThemeManager:
    """
    初始化全局主题管理器
    
    Args:
        config: 应用程序配置
        
    Returns:
        主题管理器实例
    """
    global _theme_manager
    _theme_manager = MaterialThemeManager(config)
    return _theme_manager
