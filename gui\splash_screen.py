"""
苹果风格的启动画面
提供优雅的应用程序启动体验
"""

import logging
from typing import Optional
from datetime import datetime

from PySide6.QtWidgets import (
    QSplashScreen, QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QProgressBar, QGraphicsDropShadowEffect
)
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, Signal
from PySide6.QtGui import QPixmap, QPainter, QFont, QColor, QLinearGradient

from .styles import APPLE_COLORS
from .logo import EWRebornLogo


class AppleSplashScreen(QSplashScreen):
    """苹果风格的启动画面"""
    
    # 信号定义
    loading_progress = Signal(int, str)  # 进度和消息
    loading_finished = Signal()  # 加载完成
    
    def __init__(self, width: int = 500, height: int = 300):
        """
        初始化启动画面
        
        Args:
            width: 宽度
            height: 高度
        """
        # 创建自定义的启动画面背景
        pixmap = self._create_splash_pixmap(width, height)
        super().__init__(pixmap)
        
        self.setWindowFlags(Qt.SplashScreen | Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 设置变量
        self.width = width
        self.height = height
        self.current_progress = 0
        self.current_message = "正在启动..."
        
        # 创建UI组件
        self._setup_ui()
        
        # 设置动画
        self._setup_animations()
        
        # 居中显示
        self._center_on_screen()
    
    def _create_splash_pixmap(self, width: int, height: int) -> QPixmap:
        """创建启动画面背景图片"""
        pixmap = QPixmap(width, height)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 创建渐变背景
        gradient = QLinearGradient(0, 0, 0, height)
        gradient.setColorAt(0, QColor(APPLE_COLORS['surface']))
        gradient.setColorAt(1, QColor(APPLE_COLORS['surface_secondary']))
        
        # 绘制圆角矩形背景
        painter.setBrush(gradient)
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(0, 0, width, height, 20, 20)
        
        # 绘制边框
        painter.setPen(QColor(APPLE_COLORS['border']))
        painter.setBrush(Qt.NoBrush)
        painter.drawRoundedRect(1, 1, width-2, height-2, 19, 19)
        
        painter.end()
        return pixmap
    
    def _setup_ui(self):
        """设置用户界面"""
        # 创建主容器
        container = QWidget(self)
        container.setGeometry(0, 0, self.width, self.height)
        
        layout = QVBoxLayout(container)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(20)
        
        # 应用图标和标题区域
        header_layout = QVBoxLayout()
        header_layout.setAlignment(Qt.AlignCenter)
        
        # 应用图标（使用新的Logo组件）
        self.icon_label = EWRebornLogo(80)
        self.icon_label.setStyleSheet(f"""
            QLabel {{
                margin-bottom: 10px;
            }}
        """)
        header_layout.addWidget(self.icon_label)
        
        # 应用标题
        self.title_label = QLabel("EWReborn")
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 28px;
                font-weight: 700;
                color: {APPLE_COLORS['text_primary']};
                margin-bottom: 5px;
            }}
        """)
        header_layout.addWidget(self.title_label)
        
        # 副标题
        self.subtitle_label = QLabel("I/O点自动分配系统")
        self.subtitle_label.setAlignment(Qt.AlignCenter)
        self.subtitle_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                font-weight: 400;
                color: {APPLE_COLORS['text_secondary']};
                margin-bottom: 20px;
            }}
        """)
        header_layout.addWidget(self.subtitle_label)
        
        layout.addLayout(header_layout)
        layout.addStretch()
        
        # 进度区域
        progress_layout = QVBoxLayout()
        progress_layout.setSpacing(10)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setFixedHeight(6)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: none;
                border-radius: 3px;
                background-color: {APPLE_COLORS['surface_tertiary']};
            }}
            QProgressBar::chunk {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                           stop: 0 {APPLE_COLORS['primary']},
                                           stop: 1 #5AC8FA);
                border-radius: 3px;
            }}
        """)
        progress_layout.addWidget(self.progress_bar)
        
        # 状态消息
        self.status_label = QLabel("正在启动...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet(f"""
            QLabel {{
                font-size: 12px;
                font-weight: 400;
                color: {APPLE_COLORS['text_tertiary']};
                margin-top: 5px;
            }}
        """)
        progress_layout.addWidget(self.status_label)
        
        layout.addLayout(progress_layout)
        
        # 版本信息
        version_layout = QHBoxLayout()
        version_layout.setAlignment(Qt.AlignCenter)
        
        self.version_label = QLabel("v1.0.0")
        self.version_label.setStyleSheet(f"""
            QLabel {{
                font-size: 10px;
                color: {APPLE_COLORS['text_quaternary']};
            }}
        """)
        version_layout.addWidget(self.version_label)
        
        layout.addLayout(version_layout)
        
        # 添加阴影效果
        self._add_shadow_effect()
    
    def _add_shadow_effect(self):
        """添加阴影效果"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 10)
        self.setGraphicsEffect(shadow)
    
    def _setup_animations(self):
        """设置动画效果"""
        # 图标脉冲动画
        self.icon_animation = QPropertyAnimation(self.icon_label, b"geometry")
        self.icon_animation.setDuration(2000)
        self.icon_animation.setEasingCurve(QEasingCurve.InOutSine)
        self.icon_animation.setLoopCount(-1)  # 无限循环
        
        # 淡入动画
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(500)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def _center_on_screen(self):
        """在屏幕中央显示"""
        screen = self.screen().availableGeometry()
        x = (screen.width() - self.width) // 2
        y = (screen.height() - self.height) // 2
        self.move(x, y)
    
    def show_with_animation(self):
        """带动画效果显示启动画面"""
        self.show()
        self.fade_animation.start()
        
        # 启动图标动画
        QTimer.singleShot(300, self._start_icon_animation)
    
    def _start_icon_animation(self):
        """启动图标动画"""
        original_geometry = self.icon_label.geometry()
        larger_geometry = original_geometry.adjusted(-2, -2, 2, 2)
        
        self.icon_animation.setStartValue(original_geometry)
        self.icon_animation.setKeyValueAt(0.5, larger_geometry)
        self.icon_animation.setEndValue(original_geometry)
        self.icon_animation.start()
    
    def update_progress(self, progress: int, message: str = ""):
        """
        更新进度
        
        Args:
            progress: 进度百分比 (0-100)
            message: 状态消息
        """
        self.current_progress = progress
        self.progress_bar.setValue(progress)
        
        if message:
            self.current_message = message
            self.status_label.setText(message)
        
        # 发射信号
        self.loading_progress.emit(progress, self.current_message)
        
        # 如果完成，准备关闭
        if progress >= 100:
            QTimer.singleShot(500, self._finish_loading)
    
    def _finish_loading(self):
        """完成加载"""
        self.loading_finished.emit()
        
        # 淡出动画
        fade_out = QPropertyAnimation(self, b"windowOpacity")
        fade_out.setDuration(300)
        fade_out.setStartValue(1.0)
        fade_out.setEndValue(0.0)
        fade_out.setEasingCurve(QEasingCurve.InCubic)
        fade_out.finished.connect(self.close)
        fade_out.start()
    
    def simulate_loading(self):
        """模拟加载过程（用于演示）"""
        loading_steps = [
            (10, "初始化配置..."),
            (25, "加载核心模块..."),
            (40, "初始化数据库..."),
            (60, "加载用户界面..."),
            (80, "应用主题样式..."),
            (95, "准备就绪..."),
            (100, "启动完成")
        ]
        
        def update_step(step_index):
            if step_index < len(loading_steps):
                progress, message = loading_steps[step_index]
                self.update_progress(progress, message)
                
                # 安排下一步
                if step_index < len(loading_steps) - 1:
                    QTimer.singleShot(800, lambda: update_step(step_index + 1))
        
        # 开始模拟
        QTimer.singleShot(500, lambda: update_step(0))


